build:task-test:
  extends:
    - .build-image:kaniko
  variables:
    PROFILE_NAME: test
    SERVICE_NAME: mibpm-runtime-be-task
    IMAGE: ${CI_REGISTRY_IMAGE}/${SERVICE_NAME}/${PROFILE_NAME}:${CI_COMMIT_TAG}
  only:
    - /^test-.*$/

build:task-pre:
  extends:
    - .build-image:kaniko
  variables:
    PROFILE_NAME: pre
    SERVICE_NAME: mibpm-runtime-be-task
    IMAGE: ${CI_REGISTRY_IMAGE}/${SERVICE_NAME}/${PROFILE_NAME}:${CI_COMMIT_TAG}
  only:
    - /^pre-.*$/
    - /^hotfix-.*$/

build:task-prod:
  extends:
    - .build-image:kaniko
  variables:
    PROFILE_NAME: prod
    SERVICE_NAME: mibpm-runtime-be-task
    IMAGE: ${CI_REGISTRY_IMAGE}/${SERVICE_NAME}/${PROFILE_NAME}:${CI_COMMIT_TAG}
  only:
    - /^prod-.*$/
