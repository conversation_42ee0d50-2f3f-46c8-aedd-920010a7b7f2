#应用名称是一定要设置的，名字换成系统别名
spring:
  application:
    name: mibpm-runtime-be-predict
  profiles:
    #默认加载的application-*等文件
    active: actuator,datasource,jackson,orm,web,infra,bpm,redis
  main:
    web-application-type: none
flowable:
  idm:
    enabled: false
  cmmn:
    enabled: false
  content:
    enabled: false
  eventregistry:
    enabled: false
  common:
    app:
      idm-url: 1
mi:
  idm:
    enabled: false
    id:
      app-id: 1
      app-key: 1
      host: http://www.mi.com
    mdd-engine:
      app-id: 1
      app-key: 1
      host: http://www.mi.com
    service:
      app-id: 1
      app-key: 1
      host: http://www.mi.com
    service2:
      app-id: 1
      app-key: 1
      host: http://www.mi.com
    radar:
      app-id: 1
      app-key: 1
      host: http://www.mi.com
    organization:
      appId: MPlUtWIdINAl
      app-key: Qvj8aL9k38HOAkcY
      host: https://org.infra.mioffice.cn
    uc:
      host: http://uc-infra.test.mioffice.cn/admin
      app-id: MPlUtWIdINAl
      app-key: Qvj8aL9k38HOAkcY
  mibpm:
    create-process:
      url: http://www.mi.com


