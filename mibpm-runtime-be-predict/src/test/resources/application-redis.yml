spring:
  redis:
    host: wcc.cache01.test.b2c.srv
    port: 22122
    password: cn_info-sales-middle_startsample_4CGYvn3yMqM6P
    # 连接超时时间（毫秒）
    timeout: 2000
    # Redis默认情况下有16个分片，这里配置具体使用的分片，默认是0
    database: 0
    # 连接池最大连接数（使用负值表示没有限制） 默认 8
    lettuce:
      shutdown-timeout: 2000ms
      pool:
        max-active: 20
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
        max-wait: 2000
        # 连接池中的最大空闲连接 默认 8
        max-idle: 20
        # 连接池中的最小空闲连接 默认 0
        min-idle: 2
