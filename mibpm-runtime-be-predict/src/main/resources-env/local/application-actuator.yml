management:
  server:
    port: 10109
  endpoints:
    web:
      exposure:
        include: "info,health,metrics,prometheus"
      base-path: /actuator
      path-mapping:
        prometheus: /prometheus
        metrics: /metric
  metrics:
    tags:
      env: local
      application: ${spring.application.name}
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5,0.9,0.95,0.99
      maximum-expected-value:
        http.server.requests: 10000
      minimum-expected-value:
        http.server.requests: 5
      sla:
        http.server.requests: 5, 10, 25, 50, 75, 100, 250, 500, 750, 1000, 2500, 5000, 7500, 10000
  health:
    redis:
      enabled: false
