package com.mi.oa.infra.mibpm.predict.infra;

import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.predict.domain.model.PredictDo;
import com.mi.oa.infra.mibpm.predict.domain.model.PredictTaskDo;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/21 15:05
 **/
public interface PredictRepository {

    /**
     * 加载流程定义信息
     *
     * @param processDefId 原始的procDefId
     * @return 内存引擎中的procDefId
     */
    String loadProcessDefinition(String processDefId);

    /**
     * 强制刷新流程定义缓存
     *
     * @param processDefId
     * @return
     */
    String refreshProcessDefinition(String processDefId);

    /**
     * 在预测引擎发起流程实例
     *
     * @param procDefId  原始的流程定义id
     * @param procInstId 原始的流程实例id，在预测引擎中作为businessKey
     * @param variables  流程变量
     * @return
     */
    ProcessInstanceDo startProcessInstance(String procDefId, String procInstId, Map<String, Object> variables);

    /**
     * @param businessKey
     * @return
     */
    List<Task> listTasks(String businessKey);

    /**
     * 根据流程实例id查询任务
     *
     * @param procInstId 流程实例id
     * @param bpmnModel  流程定义模型
     * @return
     */
    List<Task> listTasksByProcInstId(String procInstId, BpmnModel bpmnModel);

    /**
     * @param procInstId
     * @return
     */
    List<HistoricTaskInstance> listHistoricTasks(String procInstId);

    /**
     * 预测
     *
     * @param predictDo
     */
    void predictTasks(PredictDo predictDo);

    /**
     * 预测
     *
     * @param processInstId       需要预测的流程实例id
     * @param variables           流程变量
     * @param currentTaskKeys     当前任务key
     * @param processInstance     预测引擎中的流程实例id
     * @param processDefinitionId 流程定义id
     * @return
     */
    List<PredictTaskDo> startPredict(String processInstId, Map<String, Object> variables, List<String> currentTaskKeys, ProcessInstanceDo processInstance, String processDefinitionId);

    /**
     * 从缓存中加载预测任务（加载到PredictDo）
     *
     * @param predictDo
     */
    void loadPredictTasksFromCache(PredictDo predictDo);

    /**
     * 从缓存中获取
     *
     * @param procInstId 原始流程实例id
     * @return
     */
    List<PredictTaskDo> loadPredictTasksFromCache(String procInstId);
}
