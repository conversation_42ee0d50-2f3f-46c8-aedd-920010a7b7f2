package com.mi.oa.infra.mibpm.predict.remote.impl;

import com.mi.id.oaucf.feign.BaseResp;
import com.mi.oa.infra.mibpm.predict.remote.RuntimeRemoteService;
import com.mi.oa.infra.mibpm.predict.remote.model.ProcInstMonitorVo;
import com.mi.oa.infra.mibpm.predict.remote.sdk.RuntimeRemote;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/22 16:07
 **/
@Service
@Slf4j
public class RuntimeRemoteServiceImpl implements RuntimeRemoteService {

    @Autowired
    private RuntimeRemote runtimeRemote;

    @Override
    public ProcInstMonitorVo queryDetail(String processInstId) {
        if (StringUtils.isBlank(processInstId)) {
            return null;
        }
        log.info("query monitor detail procInstId={}", processInstId);
        try {
            BaseResp<ProcInstMonitorVo> procInstMonitorVoBaseResp = runtimeRemote.queryDetail(processInstId);
            if (procInstMonitorVoBaseResp.getCode() == 0) {
                return procInstMonitorVoBaseResp.getData();
            }
        } catch (Exception e) {
            log.error("query detail error procInstId={}", processInstId, e);
        }
        return null;
    }

    @Override
    public Map<String, Object> queryVariables(String processInstId) {
        if (StringUtils.isBlank(processInstId)) {
            return new HashMap<>();
        }
        BaseResp<List<ProcInstMonitorVo.VariableVo>> listBaseResp = runtimeRemote.queryVariables(
                processInstId);
        if (listBaseResp.getCode() == 0) {
            Map<String, Object> collect = listBaseResp.getData().stream()
                    .collect(Collectors.toMap(ProcInstMonitorVo.VariableVo::getVariableName,
                            ProcInstMonitorVo.VariableVo::getValue, (k1, k2) -> k1));
            log.info("query variables result procInstId={} variables={}", processInstId, collect);
            return collect;
        } else {
            return new HashMap<>();
        }
    }
}
