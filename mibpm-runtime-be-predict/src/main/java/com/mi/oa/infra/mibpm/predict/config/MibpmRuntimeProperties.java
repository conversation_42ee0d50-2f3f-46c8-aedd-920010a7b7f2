package com.mi.oa.infra.mibpm.predict.config;

import com.mi.oa.infra.oaucf.feign.jwt.model.ServerJwtProperties;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/22 15:50
 **/
@Data
@ConfigurationProperties(prefix = "mibpm.runtime")
public class MibpmRuntimeProperties extends ServerJwtProperties {
    private String url;
    private String serverAppId;
}
