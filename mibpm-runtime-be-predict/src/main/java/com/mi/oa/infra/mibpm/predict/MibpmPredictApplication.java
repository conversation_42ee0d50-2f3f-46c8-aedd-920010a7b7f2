/*
 * Copyright (c) 2020. XiaoMi Inc.All Rights Reserved
 */
package com.mi.oa.infra.mibpm.predict;


import com.mi.oa.infra.mibpm.common.model.MiBpmProperties;
import com.mi.oa.infra.mibpm.common.model.MibpmPredictProperties;
import com.mi.oa.infra.mibpm.infra.repository.impl.CategoryRepositoryImpl;
import com.mi.oa.infra.mibpm.predict.cache.JedisProperties;
import com.mi.oa.infra.mibpm.predict.config.MibpmRuntimeProperties;
import com.mi.oa.infra.mibpm.sdk.autoconfig.MibpmRepositoryAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Import;

@SpringBootApplication
@ComponentScan(basePackages = {"com.mi.oa"},
        excludeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes =
                {CategoryRepositoryImpl.class}))
@Import(value = {MibpmRepositoryAutoConfiguration.class, DataSourceAutoConfiguration.class})
@EnableConfigurationProperties(value = {MibpmRuntimeProperties.class, MibpmPredictProperties.class,
        JedisProperties.class, MiBpmProperties.class})
public class MibpmPredictApplication {

    public static void main(String[] args) {
        SpringApplication.run(MibpmPredictApplication.class, args);
    }

}
