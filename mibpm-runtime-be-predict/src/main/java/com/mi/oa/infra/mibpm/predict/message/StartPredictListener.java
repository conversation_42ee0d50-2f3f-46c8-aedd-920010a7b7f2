package com.mi.oa.infra.mibpm.predict.message;

import com.mi.oa.infra.mibpm.common.event.PredictEvent;
import com.mi.oa.infra.mibpm.predict.service.PredictService;
import com.mi.oa.infra.mibpm.utils.ExternalThreadPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/1 20:24
 **/
@Service
@Slf4j
@RocketMQMessageListener(consumerGroup = "${rocketmq.task-predict-group}",
        topic = "${rocketmq.task-predict-topic}",
        consumeMode = ConsumeMode.CONCURRENTLY)
public class StartPredictListener implements RocketMQListener<PredictEvent> {

    @Autowired
    private PredictService predictService;
    private final Executor executor = ExternalThreadPool.getInstance();

    @Override
    public void onMessage(PredictEvent predictEvent) {
        String procInstId = predictEvent.getProcInstId();
        Map<String, Object> variables = predictEvent.getVariables();
        List<String> currentTaskKey = predictEvent.getCurrentTaskKeys();
        String processDefinitionId = predictEvent.getProcessDefinitionId();
        log.info("predict consume event procInstId={}", procInstId);
        executor.execute(() -> predictService.startPredict(procInstId, variables, currentTaskKey, processDefinitionId));
    }
}
