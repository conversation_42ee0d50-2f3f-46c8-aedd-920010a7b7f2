package com.mi.oa.infra.mibpm.predict.cache;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/27 19:40
 **/
@Service
public class RedisKeyGenerator {
    public static final String BPM_KEY_PREFIX = "mibpm:predict:";
    public static final String PROC_DEF_PREFIX = "procdef";
    public static final int MAX_EXPIRE_TIME = 3 * 24 * 3600;

    public static String redisKey(String s) {
        return BPM_KEY_PREFIX + s;
    }

    public static String redisKey(String s, List<String> keys) {
        return BPM_KEY_PREFIX + s + ":" + keys.stream().sorted().collect(Collectors.joining("_"));
    }

    public static String redisKey(String s, String key) {
        return BPM_KEY_PREFIX + s + ":" + key;
    }
}
