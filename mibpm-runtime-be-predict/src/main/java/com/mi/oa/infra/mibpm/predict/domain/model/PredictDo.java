package com.mi.oa.infra.mibpm.predict.domain.model;

import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/21 14:42
 **/
@Data
public class PredictDo {

    private String procDefId;
    private String procInstId;
    private ProcessInstanceDo predictProcessInstance;
    private List<PredictTaskDo> historicTasks;
    private List<PredictTaskDo> currentTasks;
    private List<PredictTaskDo> predictTasks;
    private Map<String, Object> variables;
    private boolean needPredict;
}
