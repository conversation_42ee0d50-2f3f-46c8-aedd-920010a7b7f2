package com.mi.oa.infra.mibpm.predict.domain.model;

import com.mi.oa.infra.mibpm.common.model.BpmUser;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/25 14:57
 **/
@Data
public class PredictTaskDo {

    private String taskDefKey;
    private String taskName;
    private String taskEnName;
    private String taskId;
    private BpmUser assignee;
    private ZonedDateTime endTime;
    protected ZonedDateTime createTime;
    private String description;
    private String processDefinitionId;
}
