package com.mi.oa.infra.mibpm.infra.task.repository;

import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;

import java.util.List;

/**
 * <AUTHOR>
 * @description 抄送任务仓储层
 * @date 2022/3/31 2:56 PM
 **/
public interface NotifiedTaskRepository {

    /**
     * @param processKey
     * @param processInstanceId
     * @param taskDefinitionKey
     * @param userIdList
     */
    void createNotifiedTask(String processKey, String processInstanceId, String taskDefinitionKey, List<String> userIdList, BpmUser createUser);

    /**
     * 校验用户是否有查看抄送任务的权限
     *
     * @param processInstId
     * @param taskDefinitionKey
     * @return
     */
    boolean checkNotifiedTaskPermission(String processInstId, String taskDefinitionKey, BpmUser bpmUser);

    /**
     * 查询抄送任务
     *
     * @param processInstId
     * @return
     */
    List<TaskDo> listNotifiedTask(String processInstId);
}
