package com.mi.oa.infra.mibpm.infra.remote.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 创建GSC待办任务
 *
 * @author: qiuzhipeng
 * @Date: 2022/10/31 14:52
 */
@Data
@Builder
public class CreateGscTodoTaskReq {

    /**
     * 任务标题
     */
    private String subject;
    /**
     * 任务描述
     */
    private String description;
    /**
     * 截止时间
     */
    private Long dueTime;
    /**
     * 任务执行人UID
     */
    private String executorId;
    /**
     * 任务参与人UID，最大支持100
     */
    private List<String> participantIds;
    /**
     * 详情页跳转地址
     */
    private DetailUrl detailUrl;
    /**
     * 业务系统侧的唯一标识，即业务ID
     */
    private String sourceId;
    /**
     * 分类
     */
    private String category;
    /**
     * 优先级
     * <p>
     * 10：较低
     * 20：普通
     * 30：紧急
     * 40：非常紧急
     * </p>
     */
    private Integer priority;

    /**
     * 摘要
     */
    private Map<String, String> summary;

    /**
     * 授权应用code
     */
    private String authAppCode;

    @Data
    @AllArgsConstructor
    public static class DetailUrl {

        /**
         * PC端详情页url跳转地址
         */
        private String pcUrl;
        /**
         * APP端详情页url跳转地址
         */
        private String appUrl;
    }

}




