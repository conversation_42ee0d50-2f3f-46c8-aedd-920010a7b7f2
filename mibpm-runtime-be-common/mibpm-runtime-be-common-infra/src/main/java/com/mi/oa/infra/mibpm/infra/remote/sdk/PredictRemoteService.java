package com.mi.oa.infra.mibpm.infra.remote.sdk;

import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/1 15:26
 **/
public interface PredictRemoteService {

    /**
     * 获取预测的任务列表
     *
     * @param procInstId   流程实例id
     * @param taskKeys     当前任务key列表，逗号连接
     * @param processDefId 流程定义id
     * @param lang 国际化
     * @param sync         是否同步返回结果
     * @return
     */
    List<TaskDo> getPredictTasks(String procInstId, String taskKeys, String processDefId, String lang, boolean sync);

    /**
     * 发送预测消息
     *
     * @param taskDo 任务实例领域对象
     */
    void sendPredictMessage(TaskDo taskDo);

    /**
     * 发送预测消息
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void sendPredictMessage(ProcessInstanceDo processInstanceDo);
}
