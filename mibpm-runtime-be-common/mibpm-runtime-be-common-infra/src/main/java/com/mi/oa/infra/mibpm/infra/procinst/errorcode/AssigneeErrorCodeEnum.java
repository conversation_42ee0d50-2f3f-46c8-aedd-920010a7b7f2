package com.mi.oa.infra.mibpm.infra.procinst.errorcode;

import com.mi.oa.infra.oaucf.core.exception.InfraErrorCode;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/9 11:26 AM
 **/
public enum AssigneeErrorCodeEnum implements InfraErrorCode {

    ASSIGNEE_UNKNOWN_ERROR(1, "人员分配未知错误"),

    INFRA_UTILS_ERROR(2, "基础设施层工具错误"),

    ASSIGNEE_GROUP_LIMIT(3, "组 %s 映射次数不能超过5次"),

    EL_EMPTY(4, "ConditionValue EL表达式为空"),

    EL_ERROR(5, "ConditionValue EL表达式格式错误：示例\"${status=='agree'}\""),


    ;


    /**
     * 具体错误码
     */
    private int errCode;
    /**
     * 描述
     */
    private String errDesc;

    @Override
    public int getBizCode() {
        return 0;
    }

    @Override
    public int getErrorCode() {
        return errCode;
    }

    @Override
    public String getErrDesc() {
        return this.errDesc;
    }

    AssigneeErrorCodeEnum(int errCode, String errDesc) {
        this.errCode = errCode;
        this.errDesc = errDesc;
    }
}
