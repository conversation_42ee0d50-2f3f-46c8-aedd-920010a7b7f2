package com.mi.oa.infra.mibpm.infra.procinst.remote;

import com.mi.oa.infra.mibpm.infra.procinst.remote.model.ProcessDefinitionModel;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/3/8 19:01
 */
public interface ProcessDefinitionRemoteService {

    ProcessDefinitionModel queryLastProcessDefinition(String modelCode);

    ProcessDefinitionModel queryProcessDefinition(String processDefinitionId);

    Set<String> listFirstNodeDef(String processDefinitionId);
}
