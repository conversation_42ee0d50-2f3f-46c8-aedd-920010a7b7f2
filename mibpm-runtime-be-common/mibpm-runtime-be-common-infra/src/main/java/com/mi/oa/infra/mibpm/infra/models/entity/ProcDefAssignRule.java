package com.mi.oa.infra.mibpm.infra.models.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/12 11:08
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("act_re_procdef_assign_rule")
public class ProcDefAssignRule {

    @TableId(type = IdType.AUTO)
    private Long id;
    private String procDefId;
    private String nodeId;
    private String assignType;
    private String ruleType;
    private String ruleJson;

}
