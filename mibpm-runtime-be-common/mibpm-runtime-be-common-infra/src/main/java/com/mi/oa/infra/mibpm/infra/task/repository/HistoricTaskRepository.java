package com.mi.oa.infra.mibpm.infra.task.repository;

import com.mi.oa.infra.mibpm.common.model.HistoricTaskQueryDto;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.oaucf.core.dto.PageModel;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/9 17:02
 */
public interface HistoricTaskRepository {

    /**
     * 按条件查询历史任务（分页）
     *
     * @param historicTask 历史任务查询条件
     * @param pageNum      页数
     * @param pageSize     每页大小
     * @return 历史任务（分页）
     */
    PageModel<TaskDo> queryHistoricTaskPage(HistoricTaskQueryDto historicTask, long pageNum, long pageSize);

    /**
     * 按条件查询历史任务数量
     *
     * @param historicTaskQuery 历史任务查询条件
     * @return 历史任务数据量
     */
    Long queryHistoricTaskCount(HistoricTaskQueryDto historicTaskQuery);

    /**
     * 通过流程实例ID查询历史任务
     *
     * @param processInstanceId 流程实例ID
     * @return 历史任务
     */
    List<TaskDo> queryHistoricTasksByProcInstId(String processInstanceId);

    /**
     * 通过ID批量查询历史任务
     *
     * @param taskIds 任务id列表
     * @return 历史任务
     */
    List<TaskDo> queryHistoricTasksByIds(List<String> taskIds);

    /**
     * 批量通过流程实例ID查询历史任务
     *
     * @param processInstanceId 流程实例ID
     * @return 历史任务
     */
    List<TaskDo> queryHistoricTasksByProcInstId(List<String> processInstanceId);

    /**
     * 查询历史任务
     *
     * @param taskId 任务ID
     * @return 历史任务
     */
    TaskDo queryHistoricTask(String taskId);

    /**
     * 加载历史任务变量
     *
     * @param taskDo
     */
    void loadHistoricVariables(TaskDo taskDo);


    /**
     * 加载历史执行变量
     *
     * @param taskDo
     */
    void loadHistoricExecutionVariables(TaskDo taskDo);

    /**
     * 查询最为符合的任务
     *
     * @param processInstanceDo 流程实例ID
     * @param assignee 任务处理人
     * @return 最为符合的任务
     */
    TaskDo queryMostConformTask(ProcessInstanceDo processInstanceDo, String assignee);

    /**
     * 查询流程实例的历史活动
     *
     * @param processInstId
     * @return
     */
    List<List<TaskDo>> queryHistoricActivitiesByProcInstId(String processInstId);
}
