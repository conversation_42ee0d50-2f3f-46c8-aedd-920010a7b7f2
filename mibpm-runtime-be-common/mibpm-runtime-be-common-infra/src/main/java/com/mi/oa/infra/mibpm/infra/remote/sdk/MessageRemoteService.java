package com.mi.oa.infra.mibpm.infra.remote.sdk;

/**
 * MQ发送消息
 *
 * <AUTHOR>
 * @date 2022/3/14 16:08
 */
public interface MessageRemoteService {

    /**
     * 发送消息到MQ
     *
     * @param topic       消息主题
     * @param index       消息索引
     * @param messageBody 消息体
     */
    void sendMessage(String topic, String index, Object messageBody);

    /**
     * 发送顺序消息到MQ
     *
     * @param topic       消息主题
     * @param index       消息索引
     * @param hashKey     消息顺序KEY
     * @param messageBody 消息体
     */
    void sendMessageOrderly(String topic, String index, String hashKey, Object messageBody);

    /**
     * 发送定时消息到MQ
     *
     * @param topic       消息主题
     * @param index       消息索引
     * @param timestamp   定时消费的时间戳（毫秒）
     * @param messageBody 消息体
     */
    void sendMessageAtTime(String topic, String index, Long timestamp, Object messageBody);

}
