package com.mi.oa.infra.mibpm.common.event;

import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.eventbus.Event;
import lombok.Builder;
import lombok.Data;

/**
 * 任务查看权限申请事件
 *
 * @author: qiuzhipeng
 * @Date: 2022/7/7 16:28
 */

@Data
@Builder
public class TaskViewAuthApplyEvent extends Event {

    private String taskId;

    private String message;
    /**
     * 申请人
     */
    private BpmUser applyUser;

    /**
     * 可见性配置
     */
    private String processViewAuthConfig;
}
