package com.mi.oa.infra.mibpm.common.constant;

import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;

/**
 * <AUTHOR>
 * @description
 * @date 2022/6/2 10:47 AM
 **/
public class ExtProcessInstanceStatusConstants {
    /**
     * 审批中
     */
    public static final String PENDING = "PENDING";
    /**
     * 审批流程结束，结果为同意
     */
    public static final String APPROVED = "APPROVED";
    /**
     * 审批流程结束，结果为拒绝
     */
    public static final String REJECTED = "REJECTED";
    /**
     * 审批发起人撤回
     */
    public static final String CANCELED = "CANCELED";
    /**
     * 审批被删除
     */
    public static final String DELETED = "DELETED";

    public static ProcessInstanceStatus map(String status) {
        switch (status) {
            case PENDING:
                return ProcessInstanceStatus.RUNNING;
            case APPROVED:
                return ProcessInstanceStatus.COMPLETED;
            case REJECTED:
                return ProcessInstanceStatus.REJECTED;
            case CANCELED:
            case DELETED:
                return ProcessInstanceStatus.TERMINATED;
            default:
                return null;
        }
    }
}
