package com.mi.oa.infra.mibpm.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/1/13 16:33
 */
@Getter
@AllArgsConstructor
public enum EventIdentify {
    // flowable 引擎事件
    /**
     * 任务创建
     */
    TASK_CREATED,
    /**
     * 任务结束
     */
    TASK_COMPLETED,
    /**
     * 活动节点开始
     */
    ACTIVITY_STARTED,
    /**
     * 活动节点结束
     */
    ACTIVITY_COMPLETED,
    /**
     * 流程开始
     */
    PROCESS_STARTED,
    /**
     * 流程结束
     */
    PROCESS_COMPLETED,
    /**
     * 任务实体删除
     */
    TASK_DELETED,
    /**
     * 任务实体更新
     */
    TASK_UPDATED,

    // 审批事件
    /**
     * 同意任务
     */
    OPERATE_APPROVED,
    /**
     * 委派任务
     */
    OPERATE_DELEGATED,
    /**
     * 委派任务完成
     */
    OPERATE_RESOLED,
    /**
     * 委托任务拒绝
     */
    OPERATE_DELEGATE_REJECTED,
    /**
     * 拒绝任务
     */
    OPERATE_REJECTED,
    /**
     * 退回任务
     */
    OPERATE_RETURNED,
    /**
     * 加签任务
     */
    OPERATE_SIGNED,
    /**
     * 新加签
     */
    OPERATE_SIGNED_NEW,
    /**
     * 提交任务
     */
    OPERATE_SUBMITTED,
    /**
     * 转审任务
     */
    OPERATE_TRANSFERRED,
    /**
     * 抄送任务
     */
    OPERATE_CC,
    /**
     * 终止任务
     */
    OPERATE_TERMINATED,
    /**
     * 撤回任务
     */
    OPERATE_RECALLED,
    /**
     * 领取任务
     */
    OPERATE_CLAIM,
    /**
     * 流程发起的任务提交事件
     */
    OPERATE_START_SUBMIT,
    // 业务事件
    /**
     * 自动跳过发起任务事件
     */
    SUBMIT_TASK_AUTO_COMPLETE,

    /**
     * 自动跳过任务事件
     */
    TASK_AUTO_COMPLETE,

    /**
     * 人员离职事件
     */
    PERSON_RESIGN,

    /**
     * 人员离职消息事件
     */
    PERSON_RESIGN_MESSAGE,

    /**
     * 流程负责人填写提醒
     */
    BUSINESS_OWNER_REMINDER,

    /**
     * 人员转岗事件
     */
    PERSON_TRANSFER,

    /**
     * 催办事件
     */
    URGE,

    /**
     * 建群催办事件
     */
    URGE_GROUP,

    /**
     * 待办提醒
     */
    WAIT_REMIND_NOTIFY,

    /**
     * 服务调用失败事件
     */
    REMOTE_CALL_ERROR,

    /**
     * 任务查看权限申请
     */
    TASK_VIEW_AUTH_APPLY,
    /**
     * 超时提醒
     */
    TIMEOUT_REMIND,
    /**
     * 超时自动通过
     */
    TIMEOUT_AGREE,
    /**
     * 超时自动驳回
     */
    TIMEOUT_REJECT,
    /**
     * 待办中心任务创建
     */
    TODO_TASK_CREATE,
    /**
     * 审批人变更
     */
    ASSIGNEE_CHANGE,
    /**
     * 投票同意
     */
    VOTE_AGREE,
    /**
     * 投票拒绝
     */
    VOTE_REJECT,
    /**
     * 投票弃权
     */
    VOTE_ABSTAIN,
    /**
     * 请假委托
     */
    LEAVE_DELEGATION,
    /**
     * 请假成功
     */
    LEAVE_DELEGATION_SUCCESS,
    /**
     * 统一待办事件
     */
    UNIFIED_TODO_EVENT
}
