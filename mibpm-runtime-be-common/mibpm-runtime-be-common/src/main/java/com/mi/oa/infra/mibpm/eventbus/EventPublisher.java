package com.mi.oa.infra.mibpm.eventbus;

import com.google.common.eventbus.EventBus;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022/1/13 14:31
 */
@Slf4j
public class EventPublisher {

    private EventHub eventHub;

    public EventPublisher(EventHub eventHub) {
        this.eventHub = eventHub;
    }

    public <T extends Event> void publish(T event) {
        EventBus eventBus = eventHub.getEventBus(event.getIdentifier());
        if (eventBus == null) {
            log.error("identifier [ {} ] 没有事件监听者", event.getIdentifier());
            return;
        }
        eventBus.post(event);
    }
}
