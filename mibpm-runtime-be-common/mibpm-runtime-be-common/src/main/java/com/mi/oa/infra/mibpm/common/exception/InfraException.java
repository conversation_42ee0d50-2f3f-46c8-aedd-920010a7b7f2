package com.mi.oa.infra.mibpm.common.exception;

import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.core.exception.ErrorCode;

/**
 * 基础设施层异常
 *
 * <AUTHOR>
 * @date 2021/12/25 11:31
 */
public class InfraException extends BizException {

    public InfraException(ErrorCode errorCode) {
        super(errorCode);
    }

    public InfraException(ErrorCode errorCode, String... param) {
        super(errorCode, param);
    }

    public InfraException(Throwable cause, ErrorCode errorCode, String... param) {
        super(cause, errorCode, param);
    }
}
