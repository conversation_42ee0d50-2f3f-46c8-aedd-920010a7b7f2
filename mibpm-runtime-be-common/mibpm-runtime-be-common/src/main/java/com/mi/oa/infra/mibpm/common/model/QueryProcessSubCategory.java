package com.mi.oa.infra.mibpm.common.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/27
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QueryProcessSubCategory {
    private String subCategoryCode; // 二级分类的code
    private String subCategoryName; // 二级分类的名称
    private String subCategoryEnName; // 二级分类的名称
    private List<QueryProcessVo> processes; // 流程列表
}
