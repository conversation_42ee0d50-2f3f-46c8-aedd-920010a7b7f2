package com.mi.oa.infra.mibpm.common.event;

import com.mi.oa.infra.mibpm.common.enums.AutoOperationTypeEnum;
import com.mi.oa.infra.mibpm.eventbus.Event;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * 自动跳过任务事件
 *
 * <AUTHOR>
 * @date 2022/09/16 11:42
 */
@Data
@Builder
public class TaskAutoCompleteEvent extends Event {

    /**
     * 流程定义ID
     */
    private String processDefinitionId;
    /**
     * 流程实例ID
     */
    private String processInstanceId;
    /**
     * 任务实例ID
     */
    private String taskId;
    /**
     * 评论
     */
    private String comment;
    private String commentEn;
    /**
     * 表单数据
     */
    private Map<String, Object> formData;
    /**
     * 自动操作类型
     */
    private AutoOperationTypeEnum autoOperationType;
}
