package com.mi.oa.infra.mibpm.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 流程内置的系统变量
 *
 * <AUTHOR>
 * @date 2021/12/27 20:43
 */
@Getter
@AllArgsConstructor
public enum SystemVariable {

    /**
     * 上级领导
     */
    SUPER_ONE("super1", "上级领导", "reportLine", null),
    /**
     * 上上级领导
     */
    SUPER_TWO("super2", "上上级领导", "reportLine", null),
    /**
     * 汇报线一级领导
     */
    SUPER_LEVEL_ONE("level1", "汇报线一级领导", "reportLine", null),
    /**
     * 汇报线二级领导
     */
    SUPER_LEVEL_TWO("level2", "汇报线二级领导", "reportLine", null),
    /**
     * 汇报线三级领导
     */
    SUPER_LEVEL_THREE("level3", "汇报线三级领导", "reportLine", null),
    /**
     * 汇报线四级领导
     */
    SUPER_LEVEL_FOUR("level4", "汇报线四级领导", "reportLine", null),
    /**
     * 汇报线五级领导
     */
    SUPER_LEVEL_FIVE("level5", "汇报线五级领导", "reportLine", null),
    /**
     * 用户层级
     */
    USER_LEVEL("userlevel", "用户层级", "userLevel", null),
    /**
     * 用户所属部门
     */
    USER_DEPT("userdept", "用户所属部门", "userdept", null),
    /**
     * 用户所属一级部门
     */
    USER_DEPT_LEVEL_ONE("userdeptlevel1", "用户所属一级部门", "userdept", null),
    /**
     * 用户所属二级部门
     */
    USER_DEPT_LEVEL_TWO("userdeptlevel2", "用户所属二级部门", "userdept", null),
    /**
     * 用户所属三级部门
     */
    USER_DEPT_LEVEL_THREE("userdeptlevel3", "用户所属三级部门", "userdept", null),
    /**
     * 用户所属四级部门
     */
    USER_DEPT_LEVEL_FOUR("userdeptlevel4", "用户所属四级部门", "userdept", null),
    /**
     * 用户所属五级部门
     */
    USER_DEPT_LEVEL_FIVE("userdeptlevel5", "用户所属五级部门", "userdept", null),
    /**
     * 用户所属六级部门
     */
    USER_DEPT_LEVEL_SIX("userdeptlevel6", "用户所属六级部门", "userdept", null),
    /**
     * 用户职级
     */
    USER_RANK("userrank", "用户职级", "userrank", null),

    USER_LEADER("leader", "用户上级", "userleader", null),
    NEW_USER_DEPT("dept", "用户部门", "userdept", null),
    NEW_USER_DEPT_DIRECT("direct", "用户直属部门", "userdept", null),
    NEW_USER_DEPT1("dept1", "用户一级部门", "userdept", null),
    NEW_USER_DEPT2("dept2", "用户二级部门", "userdept", null),
    NEW_USER_DEPT3("dept3", "用户三级部门", "userdept", null),
    NEW_USER_DEPT4("dept4", "用户四级部门", "userdept", null),
    NEW_USER_DEPT5("dept5", "用户五级部门", "userdept", null),
    NEW_USER_DEPT6("dept6", "用户六级部门", "userdept", null),
    ;

    private final String code;
    private final String name;
    private final String group;
    private final Object attribute;

    public static SystemVariable getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (SystemVariable value : SystemVariable.values()) {
            if (code.equals(value.getCode())) {
                return value;
            }
        }
        return null;
    }
}
