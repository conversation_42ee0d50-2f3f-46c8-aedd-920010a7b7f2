package com.mi.oa.infra.mibpm.common.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @author: qiuzhipeng
 * @Date: 2022/8/9 16:04
 */
@Data
public class FormFlatDataDto {

    /**
     * 表单实例ID
     */
    private Long fromInstanceId;
    /**
     * 业务KEY
     */
    private String businessKey;
    /**
     * 流程实例ID
     */
    private String processInstanceId;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 表单定义ID
     */
    private String formDefinitionId;
    /**
     * 主表单平铺数据
     */
    private Map<String, FieldData> mainFieldData;
    /**
     * 明细表字段数据
     */
    private Map<String, TableData> tableFieldData;

    @Data
    public static class TableData {
        private String label;
        private String name;
        List<Map<String, FieldData>> tableRowData;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FieldData {
        private String label;
        private String name;
        private String type;
        private Object data;
        private Object field;
    }
}
