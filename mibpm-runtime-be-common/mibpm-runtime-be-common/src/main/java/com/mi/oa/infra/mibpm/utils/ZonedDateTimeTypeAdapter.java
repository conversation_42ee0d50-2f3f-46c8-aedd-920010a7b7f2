package com.mi.oa.infra.mibpm.utils;

import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;

import java.io.IOException;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2023/9/22 14:28
 */
public class ZonedDateTimeTypeAdapter extends TypeAdapter<ZonedDateTime> {

    @Override
    public void write(JsonWriter out, ZonedDateTime value) throws IOException {
        out.value(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(value));
    }

    @Override
    public ZonedDateTime read(J<PERSON><PERSON>eader in) throws IOException {
        return ZonedDateTime.parse(in.nextString());
    }

}
