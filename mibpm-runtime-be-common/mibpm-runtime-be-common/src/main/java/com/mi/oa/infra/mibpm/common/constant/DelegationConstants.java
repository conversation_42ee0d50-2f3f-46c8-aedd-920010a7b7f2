package com.mi.oa.infra.mibpm.common.constant;

/**
 *
 * 委托相关常量
 * @author: qiuzhipeng
 * @Date: 2021/4/7 17:21
 */
public class DelegationConstants {

    /**
     * 所有分类类型
     */
    public static final int ALL_CATEGORY_TYPE = 0;

    /**
     * 流程类型
     */
    public static final int MODEL_CODE_TYPE = 1;

    /**
     * 无效状态
     */
    public static final int INVALID = -1;
    /**
     * 有效状态
     */
    public static final int VALID = 1;



}
