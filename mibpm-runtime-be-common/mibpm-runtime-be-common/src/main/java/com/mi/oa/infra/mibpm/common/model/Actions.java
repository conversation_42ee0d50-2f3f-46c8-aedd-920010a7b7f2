package com.mi.oa.infra.mibpm.common.model;

import com.mi.oa.infra.mibpm.common.constant.LarkMessageConstants;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 消息卡片支持按钮
 *
 * @author: qiuzp
 * @Date: 2021/4/7 18:54
 */

@Data
public class Actions implements Comparable<Actions> {

    public static final Map<String, Integer> SORT_MAP = new HashMap<>(16);
    public static final Map<String, String> ACTION_NAME_MAP = new HashMap<>(16);
    public static final Map<String, String> ACTION_NAME_EN_MAP = new HashMap<>(16);

    {
        //sort
        SORT_MAP.put(LarkMessageConstants.APPROVED, 1);
        SORT_MAP.put(LarkMessageConstants.REJECTED, 2);
        SORT_MAP.put(LarkMessageConstants.ADD, 3);
        SORT_MAP.put(LarkMessageConstants.DETAIL, 4);
        SORT_MAP.put(LarkMessageConstants.DONE, 5);
        SORT_MAP.put(LarkMessageConstants.URL, 7);
        SORT_MAP.put(LarkMessageConstants.DELEGATION_HANDOVER, 6);
        SORT_MAP.put(LarkMessageConstants.DELEGATION_OTHER, 7);

        //action name
        ACTION_NAME_MAP.put(LarkMessageConstants.APPROVED, "同意");
        ACTION_NAME_MAP.put(LarkMessageConstants.REJECTED, "驳回");
        ACTION_NAME_MAP.put(LarkMessageConstants.DETAIL, "查看详情");
        ACTION_NAME_MAP.put(LarkMessageConstants.ADD, "加签");
        ACTION_NAME_MAP.put(LarkMessageConstants.CANCELLED, "撤回");
        ACTION_NAME_MAP.put(LarkMessageConstants.FORWARDED, "转交");
        ACTION_NAME_MAP.put(LarkMessageConstants.ROLLBACK, "回退");
        ACTION_NAME_MAP.put(LarkMessageConstants.DELETED, "删除");
        ACTION_NAME_MAP.put(LarkMessageConstants.DONE, "已处理");
        ACTION_NAME_MAP.put(LarkMessageConstants.URL, "跳转");
        ACTION_NAME_MAP.put(LarkMessageConstants.DELEGATION_HANDOVER, "委托给交接人");
        ACTION_NAME_MAP.put(LarkMessageConstants.DELEGATION_OTHER, "委托给其他人");

        ACTION_NAME_EN_MAP.put(LarkMessageConstants.APPROVED, "Agree");
        ACTION_NAME_EN_MAP.put(LarkMessageConstants.REJECTED, "Reject");
        ACTION_NAME_EN_MAP.put(LarkMessageConstants.DETAIL, "Details");
        ACTION_NAME_EN_MAP.put(LarkMessageConstants.ADD, "Sign");
        ACTION_NAME_EN_MAP.put(LarkMessageConstants.CANCELLED, "Cancel");
        ACTION_NAME_EN_MAP.put(LarkMessageConstants.FORWARDED, LarkMessageConstants.FORWARDED);
        ACTION_NAME_EN_MAP.put(LarkMessageConstants.ROLLBACK, LarkMessageConstants.ROLLBACK);
        ACTION_NAME_EN_MAP.put(LarkMessageConstants.DELETED, LarkMessageConstants.DELETED);
        ACTION_NAME_EN_MAP.put(LarkMessageConstants.DONE, "Done");
        ACTION_NAME_EN_MAP.put(LarkMessageConstants.URL, "URL");
        ACTION_NAME_EN_MAP.put(LarkMessageConstants.DELEGATION_HANDOVER, "Delegation Handover");
        ACTION_NAME_EN_MAP.put(LarkMessageConstants.DELEGATION_OTHER, "Delegation Other");
    }

    private String actionName;

    private String actionKey;

    private String androidUrl;
    private String iosUrl;
    private String pcUrl;

    private Integer sort;

    @Override
    public int compareTo(Actions o) {
        return this.sort - o.sort;
    }
}
