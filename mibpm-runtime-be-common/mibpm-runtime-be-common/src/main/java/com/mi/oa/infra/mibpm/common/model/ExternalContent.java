package com.mi.oa.infra.mibpm.common.model;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DateTime 2024/10/23 19:58
 * @Description
 **/
@Data
public class ExternalContent {

    private String username;
    private String approverName;
    private String deptInfo;
    /**
     *是否是审批人 默认false
     */
    private boolean isApprover;

    private List<Map<String, String>> summaries;
}
