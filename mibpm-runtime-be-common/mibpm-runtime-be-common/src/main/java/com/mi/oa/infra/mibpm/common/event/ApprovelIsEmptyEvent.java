package com.mi.oa.infra.mibpm.common.event;

import com.mi.oa.infra.mibpm.common.enums.AutoOperationTypeEnum;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.eventbus.Event;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/10
 */
@Data
@Builder
public class ApprovelIsEmptyEvent extends Event {

    /**
     * 任务ID
     */
    protected String taskId;

    /**
     * 任务名称
     */
    protected String taskName;

    /**
     * 任务定义key
     */
    protected String taskDefinitionKey;
    /**
     * 流程实例ID
     */
    protected String processInstanceId;
    /**
     * 流程定义ID
     */
    protected String processDefinitionId;
    /**
     * 操作人
     */
    protected BpmUser operator;
    /**
     * 评论
     */
    protected String comment;
    /**
     * 表单数据
     */
    protected Map<String, Object> formData;

}
