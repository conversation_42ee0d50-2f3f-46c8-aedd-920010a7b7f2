package com.mi.oa.infra.mibpm.common.event;

import com.mi.oa.infra.mibpm.eventbus.Event;
import lombok.Builder;
import lombok.Data;

/**
 * 流程实例结束起事件
 *
 * <AUTHOR>
 * @date 2022/3/18 16:29
 */
@Builder
@Data
public class ProcInstEndEvent extends Event {

    /**
     * 流程实例ID
     */
    protected String processInstanceId;

    /**
     * 流程定义ID
     */
    protected String processDefinitionId;


}
