/*
 * Copyright (c) 2020. XiaoMi Inc.All Rights Reserved
 */

package com.mi.oa.infra.mibpm.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 领域/业务枚举.
 *
 * <AUTHOR>
 * @date 2021/6/16 17:34
 */
@Getter
@AllArgsConstructor
public enum BizCodeEnum {
    /**
     * 通用业务/领域（service层不区分业务）
     */
    COMMON(0, "通用业务/领域"),


    MODELS(1, "模型领域"),
    /**
     * 系统业务不相关的类型/领域
     */
    SYSTEM_UNKNOWN(90000, "系统业务不相关的类型/领域");

    private int bizCode;

    private String bizName;
}
