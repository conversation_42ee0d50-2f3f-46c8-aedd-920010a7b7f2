package com.mi.oa.infra.mibpm.eventbus;

import com.google.common.eventbus.AsyncEventBus;
import com.google.common.eventbus.EventBus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @date 2022/1/13 13:52
 */
@Slf4j
public class EventHub implements ApplicationListener<ContextRefreshedEvent> {

    private final Map<String, AsyncEventBus> eventBusMap = new ConcurrentHashMap<>();
    private ApplicationContext applicationContext;
    private Executor executor;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        // 只加载顶级spring context
        if (contextRefreshedEvent.getApplicationContext().getParent() == null) {
            this.applicationContext = contextRefreshedEvent.getApplicationContext();
            loadEventHandler();
        }
    }

    public void loadEventHandler() {
        Map<String, EventSubscriber> eventHandlerMap = applicationContext.getBeansOfType(EventSubscriber.class);
        for (EventSubscriber eventHandler : eventHandlerMap.values()) {
            this.register(eventHandler);
        }
    }

    private void register(EventSubscriber eventHandler) {
        String identifier = eventHandler.identifier();
        if (StringUtils.isBlank(identifier)) {
            log.error("EventHandler[ {} ]没有配置 identifier ，注册失败", eventHandler.getClass().getSimpleName());
            return;
        }
        eventBusMap.computeIfAbsent(identifier,
                        s -> new AsyncEventBus(identifier, Objects.isNull(executor)
                                ? EventThreadPoolFactory.buildDefaultExecutor(identifier) : executor))
                .register(eventHandler);

    }

    public EventBus getEventBus(String identifier) {
        return eventBusMap.get(identifier);
    }

    public Executor getExecutor() {
        return executor;
    }

    public void setExecutor(Executor executor) {
        this.executor = executor;
    }

}
