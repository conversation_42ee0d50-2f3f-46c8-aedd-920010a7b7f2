package com.mi.oa.infra.mibpm.common.enums;

/**
 * <AUTHOR>
 * @date 2024/8/22
 * @Description
 */
public enum DelegationStatusEnum {
    INVALID(-1, "委托失效"),
    UNDER_REVIEW(0, "委托审批中"),
    VALID(1, "委托生效"),
    AWAITING(2, "委托待开始"),
    NOT_APPROVED(3, "委托审批未通过");

    private final int code;
    private final String description;

    DelegationStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static DelegationStatusEnum fromCode(int code) {
        for (DelegationStatusEnum status : DelegationStatusEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }
}
