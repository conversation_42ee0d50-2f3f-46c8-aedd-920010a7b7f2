package com.mi.oa.infra.mibpm.application.userconfig.dto.reps;

import lombok.Data;

import java.util.List;

/**
 * @author: qiuzhipeng
 * @Date: 2022/4/6 10:13
 */
@Data
public class LarkUserInfoReps {
    protected String name;
    protected String namePy;
    protected String enName;
    protected String employeeId;
    protected String employeeNo;
    protected String openId;
    protected int status;
    protected int employeeType;
    protected String avatar72;
    protected String avatar240;
    protected String avatar640;
    protected String avatarUrl;
    protected int gender;
    protected String email;
    protected String description;
    protected String country;
    protected String city;
    protected String workStation;
    protected Boolean isTenantManager;
    protected Long joinTime;
    protected Long updateTime;
    protected String leaderEmployeeId;
    protected String leaderOpenId;
    protected List<String> departments;
}
