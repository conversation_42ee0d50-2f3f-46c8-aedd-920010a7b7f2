package com.mi.oa.infra.mibpm.application.process.service;

import java.util.List;

import com.mi.oa.infra.mibpm.application.process.dto.resp.ProcessResp;

public interface ProcessService {
    
    /**
     * 查询全部流程
     */
    List<ProcessResp> queryAllProcess();

    /**
     * 查询单个流程
     */
    ProcessResp queryOneProcess(String modelCode);

    /**
     * 查询指定流程
     */
    List<ProcessResp> queryProcessByModelCodes(List<String> modelCodes);

    /**
     * 查询指定应用下的流程
     */
    List<ProcessResp> queryProcessByAppCode(String appCode);

    /**
     * 查询指定分类下的流程
     */
    List<ProcessResp> queryProcessByCategoryCodes(List<String> categoryCodes);
}
