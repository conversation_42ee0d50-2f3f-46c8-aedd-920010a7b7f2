package com.mi.oa.infra.mibpm.application.task.dto.req;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * @author: qiuzhipeng
 * @Date: 2022/5/23 20:14
 */

@Data
@ApiModel(value = "修改任务审批人请求", description = "修改任务审批人请求")
public class UpdateAssigneeListReq {

    protected List<UpdateAssigneeReq> updateAssigneeListReq;

    @Data
    public static class UpdateAssigneeReq{
        /**
         * 任务id
         */
        private String taskId;
        /**
         * 用户id
         */
        private String userId;

        /**
         * 评论
         */
        private String comment;
    }
}
