package com.mi.oa.infra.mibpm.application.task.dto.req;

import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.Operator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2022/3/14 15:23
 */
@Data
@ApiModel(value = "审批任务认领请求", description = "审批任务认领请求")
public class TaskSignatureReq implements Operator {

    @ApiModelProperty(value = "任务ID", required = true)
    @NotBlank(message = "任务ID不能为空")
    private String taskId;
    @ApiModelProperty(value = "操作用户", required = false, hidden = true)
    private BpmUser operator;
    @ApiModelProperty(value = "签名", required = true)
    @NotBlank(message = "签名不能为空")
    private String signature;
    @ApiModelProperty(value = "是否默认", required = false)
    private boolean saveAsDefault;
}
