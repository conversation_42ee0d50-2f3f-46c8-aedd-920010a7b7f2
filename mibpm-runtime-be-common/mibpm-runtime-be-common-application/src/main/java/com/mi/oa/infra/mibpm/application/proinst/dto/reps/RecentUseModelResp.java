package com.mi.oa.infra.mibpm.application.proinst.dto.reps;

import com.mi.oa.infra.oaucf.core.dto.DTO;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 最近发起的流程模型列表
 *
 * @author: qiuzhipeng
 * @Date: 2022/9/13 20:26
 */
@Data
@Builder
public class RecentUseModelResp extends DTO {

    protected String appCode;
    protected String name;
    protected String nameEn;
    protected String modelCode;
    protected String description;
    protected String descriptionEn;
    protected Integer fromOld;
    protected List<String> owners;
    protected Integer migrationProcDefVersion;
    /**
     * 启用状态 0禁用 1 启用
     */
    protected Integer modelEnableStatus;
    /**
     * 分类信息
     */
    protected String categoryCode;
    protected String categoryName;
    protected String categoryNameEn;
    protected String categoryIcon;
}
