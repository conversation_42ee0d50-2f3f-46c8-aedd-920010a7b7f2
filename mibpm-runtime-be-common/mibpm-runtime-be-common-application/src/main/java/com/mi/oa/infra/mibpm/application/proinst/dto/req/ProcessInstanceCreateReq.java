package com.mi.oa.infra.mibpm.application.proinst.dto.req;

import com.mi.oa.infra.mibpm.common.model.TaskAssignee;
import com.mi.oa.infra.oaucf.core.dto.DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * 创建流程实例请求
 *
 * <AUTHOR>
 * @date 2022/2/9 17:34
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "创建流程实例请求", description = "创建流程实例请求")
public class ProcessInstanceCreateReq extends DTO {

    /**
     * 流程定义ID
     */
    @ApiModelProperty(value = "流程定义ID（模型编码和流程定义ID不允许同时为空）", required = false)
    protected String processDefinitionId;
    /**
     * 模型编码
     */
    @ApiModelProperty(value = "模型编码（模型编码和流程定义ID不允许同时为空）", required = false)
    protected String modelCode;
    /**
     * 流程实例名称
     */
    @ApiModelProperty(value = "流程实例名称", required = false)
    protected String name;
    /**
     * 业务唯一编码
     */
    @ApiModelProperty(value = "业务唯一编码", required = false)
    protected String businessKey;
    /**
     * 发起人
     */
    @ApiModelProperty(value = "发起人", required = false)
    protected String startUserId;
    /**
     * 流程变量
     */
    @ApiModelProperty(value = "流程变量", required = false)
    protected Map<String, Object> variables;
    /**
     * 表单数据
     */
    @ApiModelProperty(value = "表单数据", required = false)
    protected Map<String, Object> formData;
    /**
     * 表单内容
     */
    @ApiModelProperty(value = "表单内容（兼容历史流程，新接入流程请忽略）", required = false)
    protected FormContent formContent;
    /**
     * 任务处理人
     */
    @ApiModelProperty(value = "任务处理人", required = false)
    protected List<TaskAssignee> taskAssignees;
    /**
     * 评论
     */
    @ApiModelProperty(value = "评论", required = false)
    protected String comment;

    protected Integer processDefinitionVersion;

    /**
     * 表单内容
     */
    @Data
    public static class FormContent {
        /**
         * PC端
         */
        @ApiModelProperty(value = "PC端", required = false)
        protected String web;
        /**
         * 移动端
         */
        @ApiModelProperty(value = "移动端", required = false)
        protected String app;
    }
}
