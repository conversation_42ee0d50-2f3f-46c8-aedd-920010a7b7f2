package com.mi.oa.infra.mibpm.application.proinst.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2022/5/25 10:49
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "管理员终止流程实例请求", description = "管理员终止流程实例请求")
public class AdminTerminateProcInstReq {

    /**
     * 流程实例ID
     */
    @ApiModelProperty(value = "流程实例ID", required = true)
    @NotBlank(message = "流程实例ID")
    protected String instanceId;

    /**
     * 评论
     */
    @ApiModelProperty(value = "评论", required = false)
    private String comment;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人", required = true)
    @NotBlank(message = "操作人不能为空")
    private String operator;
}
