package com.mi.oa.infra.mibpm.application.delegation.vo;

import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/26
 * @Description
 */
@Data
@Builder
public class QueryDelegationInfoVo {
    /**
     * id
     */
    private Long id;
    /**
     * 委托人
     */
    private String userId;
    /**
     * 被委托人
     */
    private String delegationUserId;
    /**
     * 更新记录人id（一般为管理员）
     */
    private String updateUser;
    /**
     * 记录创建人
     */
    private String createUser;
    /**
     * 委托开始时间
     */
    private Date startTime;
    /**
     * 委托结束时间
     */
    private Date endTime;
    /**
     * 委托类型，0代表全部流程委托，1代表部分流程委托
     */
    private Integer type;
    /**
     * 委托状态，0委托审批中  1委托生效 2委托失效 3委托审批未通过
     */
    private Integer status;
    /**
     * 委托理由
     */
    private String delegationReason;
    /**
     * 部分委托的流程信息
     */
    private String modelCode;
    /**
     * 全部流程排除部分委托的流程信息
     */
    private List<String> exclusionModelCodes;
    /**
     * 表示流程的范围或适用性，1: 属于 2: 不属于 3: 不限
     */
    private Integer delegationScope;
    /**
     * 表示流程发起人的部门
     */
    private List<String> initiatingDeptCodes;
    /**
     * 委托流程实例化id
     */
    private String delegationProcessInstanceId;
    /**
     * 委托需求提出人
     */
    private String delegationRequirementProposer;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 创建时间
     */
    private Date createTime;
}
