package com.mi.oa.infra.mibpm.application.delegation.vo;

import com.mi.oa.infra.mibpm.common.model.CancelInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/27
 * @Description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CancelDelegationVo {
    List<CancelInfo> cancelInfoList;
}


