package com.mi.oa.infra.mibpm.application.proinst.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2022/3/15 18:38
 */
@Data
@ApiModel(value = "Open API审批任务退回请求", description = "Open API审批任务退回请求")
public class OpenReturnTaskReq {

    @ApiModelProperty(value = "任务ID", required = true)
    @NotBlank(message = "任务ID不能为空")
    private String taskId;
    @ApiModelProperty(value = "目标节点ID", required = true)
    private String targetActivityId;
    @ApiModelProperty(value = "意见", required = false)
    private String comment;
    @ApiModelProperty(value = "操作用户", required = true)
    @NotBlank(message = "操作用户不能为空")
    private String operator;
}
