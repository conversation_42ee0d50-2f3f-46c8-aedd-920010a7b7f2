package com.mi.oa.infra.mibpm.application.apicall.dto.reps;

import lombok.Data;

import java.time.ZonedDateTime;

/**
 * @author: qiuzhipeng
 * @Date: 2022/2/9 17:09
 */

@Data
public class ApiCallHistoryReps {

    /**
     * id
     */
    protected Integer id;

    /**
     * 消息ID
     */
    protected String sequenceId;

    /**
     * 所属应用
     */
    protected String appCode;

    /**
     * api id
     */
    protected String apiId;

    /**
     * 流程实例ID
     */
    protected String instanceId;

    /**
     * modelcode
     */
    protected String modelCode;

    /**
     * url
     */
    protected String url;

    /**
     * 请求体
     */
    protected String request;

    /**
     * 响应体
     */
    protected String response;

    /**
     * 请求状态
     */
    protected Byte status;

    /**
     * 创建时间
     */
    protected ZonedDateTime createTime;
}
