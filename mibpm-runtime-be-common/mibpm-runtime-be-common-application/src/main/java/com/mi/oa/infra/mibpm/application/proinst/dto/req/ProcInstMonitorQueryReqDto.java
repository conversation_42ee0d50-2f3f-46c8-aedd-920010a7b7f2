package com.mi.oa.infra.mibpm.application.proinst.dto.req;

import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 按条件查询流程实例请求
 *
 * <AUTHOR>
 * @date 2022/5/24 15:04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcInstMonitorQueryReqDto {

    protected String modelCode;
    protected String processInstanceName;
    protected String businessKey;
    protected String processInstanceId;
    protected List<ProcessInstanceStatus> status;
    protected String createTimeStart;
    protected String createTimeEnd;
    protected String completeTimeStart;
    protected String completeTimeEnd;
    protected String startUserId;
    protected String startUserName;
    protected List<String> startUser;
    protected String createUserName;
    protected Integer pageNum;
    protected Integer pageSize;
}