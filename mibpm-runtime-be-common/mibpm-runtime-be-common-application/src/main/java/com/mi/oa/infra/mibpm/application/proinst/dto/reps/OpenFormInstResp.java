package com.mi.oa.infra.mibpm.application.proinst.dto.reps;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/9 10:54
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "Open API表单实例响应", description = "Open API表单实例响应")
public class OpenFormInstResp {

    /**
     * 表单实例ID
     */
    private Long fromInstanceId;
    /**
     * 业务KEY
     */
    private String businessKey;
    /**
     * 流程实例ID
     */
    private String processInstanceId;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 表单定义ID
     */
    private String formDefinitionId;
    /**
     * 业务数据
     */
    private Map<String, Object> data;
}
