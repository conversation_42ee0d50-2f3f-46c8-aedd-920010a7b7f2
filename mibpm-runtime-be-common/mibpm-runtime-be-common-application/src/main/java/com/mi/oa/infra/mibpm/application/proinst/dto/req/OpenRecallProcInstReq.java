package com.mi.oa.infra.mibpm.application.proinst.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2022/5/31 19:23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "撤回流程实例请求", description = "终止流程实例请求")
public class OpenRecallProcInstReq {

    /**
     * 流程实例业务编码
     */
    @ApiModelProperty(value = "流程实例业务侧编码", required = true)
    @NotBlank(message = "流程实例业务侧编码不能为空")
    protected String businessKey;

    /**
     * 评论
     */
    @ApiModelProperty(value = "评论", required = false)
    private String comment;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人", required = true)
    @NotBlank(message = "操作人不能为空")
    private String operator;
}
