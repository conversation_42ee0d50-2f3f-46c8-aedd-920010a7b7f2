package com.mi.oa.infra.mibpm.application.task.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/16 15:27
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TaskDetailCcResp {

    @ApiModelProperty("流程实例id")
    private String processInstanceId;
    @ApiModelProperty("任务节点列表")
    private List<CcTaskResp> taskList;

}
