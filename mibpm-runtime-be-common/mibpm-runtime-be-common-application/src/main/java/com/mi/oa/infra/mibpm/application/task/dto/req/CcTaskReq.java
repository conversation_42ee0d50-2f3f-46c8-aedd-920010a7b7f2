package com.mi.oa.infra.mibpm.application.task.dto.req;

import com.mi.oa.infra.mibpm.common.enums.ClientEnum;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.Operator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/15 16:59
 */
@Data
@ApiModel(value = "审批任务抄送请求", description = "审批任务抄送请求")
public class CcTaskReq implements Operator {

    @ApiModelProperty(value = "任务ID", required = true)
    @NotBlank(message = "任务ID不能为空")
    private String taskId;
    @ApiModelProperty(value = "意见", required = false)
    private String comment;
    @ApiModelProperty(value = "操作用户", required = false, hidden = true)
    private BpmUser operator;
    @ApiModelProperty(value = "操作客户端", required = false)
    private ClientEnum client;
    @ApiModelProperty(value = "被抄送人id", required = true)
    private List<String> assignee;
    @ApiModelProperty("区分新旧流程 0新流程 1/2 旧流程")
    private Integer oldType;
    @ApiModelProperty(value = "流程变量", required = false)
    private Map<String, Object> variables;
}
