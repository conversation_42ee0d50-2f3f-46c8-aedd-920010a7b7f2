package com.mi.oa.infra.mibpm.application.userconfig.dto.reps;

import com.mi.oa.infra.oaucf.core.dto.DTO;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * @author: qiuzhipeng
 * @Date: 2022/3/21 17:43
 */
@Data
public class QueryDelegationReps extends DTO {
    /**
     * id
     */
    private Long id;
    /**
     * 委托人
     */
    private String userId;
    /**
     * 被委托人
     */
    private String delegationUserId;
    /**
     * modelCode
     */
    private List<String> modelCode;
    /**
     * 委托开始时间
     */
    private ZonedDateTime startTime;
    /**
     * 委托结束时间
     */
    private ZonedDateTime endTime;
    /**
     * 委托类型 0全部 1分类 2流程
     */
    private int type;
    /**
     * 委托状态 -1 无效  1 有效
     */
    private int status;
    /**
     * 更新时间
     */
    private ZonedDateTime updateTime;
    /**
     * 更新人
     */
    private String updateUser;
}
