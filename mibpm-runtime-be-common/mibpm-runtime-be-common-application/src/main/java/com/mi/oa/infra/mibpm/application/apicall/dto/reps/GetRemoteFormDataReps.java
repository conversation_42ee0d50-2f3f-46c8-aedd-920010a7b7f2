package com.mi.oa.infra.mibpm.application.apicall.dto.reps;

import lombok.Builder;
import lombok.Data;

/**
 * 远程获取表单数据
 *
 * @author: qiuzhipeng
 * @Date: 2022/7/19 17:12
 */
@Data
@Builder
public class GetRemoteFormDataReps {
    /**
     * 流程实例id
     */
    private String processInstanceId;
    /**
     * 节点定义key
     */
    private String taskDefinitionKey;
    /**
     * 国际化
     */
    private String locale;

    /**
     * 表单数据
     */
    private Object formData;
}
