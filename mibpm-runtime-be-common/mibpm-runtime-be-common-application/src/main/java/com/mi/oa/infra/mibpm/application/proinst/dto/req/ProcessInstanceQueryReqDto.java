package com.mi.oa.infra.mibpm.application.proinst.dto.req;

import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 按条件查询流程实例请求
 *
 * <AUTHOR>
 * @date 2022/2/9 18:04
 */
@Data
@Builder
public class ProcessInstanceQueryReqDto {
    protected String categoryCode;
    protected String modelCode;
    protected String processInstanceName;
    protected ProcessInstanceStatus status;
    protected List<ProcessInstanceStatus> statusList;
    protected String createTimeStart;
    protected String createTimeEnd;
    protected String currentUserId;
    protected String currentUserName;
    protected Boolean byCreateTimeAsc;
    /**
     * 是否走MiTask 查询
     */
    protected boolean newQueryFlag;
}
