package com.mi.oa.infra.mibpm.application.proinst.service;

import com.mi.oa.infra.mibpm.application.proinst.dto.reps.OpenCreateProcInstResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.OpenQueryProcInstResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.OpenViewAuthResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.*;
import com.mi.oa.infra.mibpm.common.model.ProcessInstanceQueryDto;
import com.mi.oa.infra.oaucf.core.dto.PageModel;

import java.util.List;

/**
 * Open-API 流程实例服务
 *
 * <AUTHOR>
 * @date 2022/5/6 18:18
 */
public interface OpenProcessInstanceService {

    /**
     * 启动流程
     *
     * @param createProcInstReq 发起流程实例请求
     * @return 发起流程实例响应
     */
    OpenCreateProcInstResp startProcessInstance(OpenCreateProcInstReq createProcInstReq);

    /**
     * 终止流程
     *
     * @param openTerminateProcInstReq 终止流程实例请求
     */
    void terminateProcessInstance(OpenTerminateProcInstReq openTerminateProcInstReq);

    /**
     * 撤回流程
     *
     * @param openRecallProcInstReq 撤回流程实例请求
     */
    void recallProcessInstance(OpenRecallProcInstReq openRecallProcInstReq);

    /**
     * 批量获取流程实例ID(分页)
     *
     * @param openListProcInstReq 批量获取流程实例请求
     * @return 流程实例ID分页对象
     */
    PageModel<String> listPageProcessInstanceId(OpenListProcInstReq openListProcInstReq);

    /**
     * 根据 processInstanceId 查询流程实例（检查是否存在）
     *
     * @param processInstanceId 流程实例ID
     * @return 获取流程实例详情响应
     */
    OpenQueryProcInstResp queryProcessInstanceById(String processInstanceId);

    /**
     * 根据 businessKey 查询流程实例（检查是否存在）
     *
     * @param businessKey 流程实例业务层编码
     * @param businessKey 流程实例ID
     * @return 获取流程实例详情响应
     */
    OpenQueryProcInstResp queryProcessInstanceByBusinessKey(String businessKey);

    /**
     * 按条件查询历史流程实例（分页）
     *
     * @param processInstanceQueryDto 查询流程实例条件
     * @param pageNum                 页数
     * @param pageSize                每页大小
     * @return 历史流程实例（分页）
     */
    PageModel<OpenQueryProcInstResp> queryProcessInstanceListPage(ProcessInstanceQueryDto processInstanceQueryDto,
                                                                  Long pageNum, Long pageSize);

    /**
     * 对某个用户授予查看权限
     *
     * @param req
     */
    void setProcInstViewAuth(OpenSetViewAuthReq req);

    /**
     * 查询某个用户被授予的查看权限
     *
     * @param req
     */
    List<OpenViewAuthResp> queryProcInstViewAuth(OpenQueryViewAuthReq req);
}
