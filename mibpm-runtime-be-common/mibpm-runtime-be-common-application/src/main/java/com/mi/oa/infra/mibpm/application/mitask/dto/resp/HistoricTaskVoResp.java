package com.mi.oa.infra.mibpm.application.mitask.dto.resp;

import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.FormSummaryDto;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskSignType;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/17
 * @Description
 */
@Data
public class HistoricTaskVoResp {
    protected String taskId;
    protected String taskName;
    protected String taskDefinitionId;
    protected String taskDefinitionKey;
    protected BpmUser processInstanceStartUser;
    protected String processInstanceName;
    protected BpmUser assignee;
    protected String processDefinitionId;
    protected String processInstanceId;
    protected String scopeId;
    protected ZonedDateTime createTime;
    protected ZonedDateTime endTime;
    protected List<HistoricProcInstVoResp.HistoricTask> currentTasks;
    protected ProcessInstanceStatus processInstanceStatus;
    private String pcLink;
    private String mobileLink;
    private ZonedDateTime processStartTime;
    private Integer oldType;
    protected List<FormSummaryDto> summary;
    protected UserTaskSignType signType;
}
