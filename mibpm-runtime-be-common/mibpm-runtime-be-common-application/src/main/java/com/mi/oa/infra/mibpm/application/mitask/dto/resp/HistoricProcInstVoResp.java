package com.mi.oa.infra.mibpm.application.mitask.dto.resp;

import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.FormSummaryDto;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskSignType;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/17
 * @Description
 */
@Data
public class HistoricProcInstVoResp {
    protected HistoricTask submitTask;
    protected List<HistoricTask> currentTasks;
    protected String processCode;
    protected String processDefinitionId;
    protected String processDefinitionName;
    protected Integer processDefinitionVersion;
    protected String businessKey;
    protected String processInstanceId;
    protected String processInstanceName;
    protected ProcessInstanceStatus processInstanceStatus;
    protected ZonedDateTime startTime;
    protected ZonedDateTime endTime;
    protected Long durationInMillis;
    protected String startUserId;
    protected Integer oldType;

    protected List<FormSummaryDto> summary;

    @Data
    public static class HistoricTask {
        protected String taskId;
        protected String taskName;
        protected String taskDefinitionId;
        protected String taskDefinitionKey;
        protected BpmUser assignee;
        private String pcLink;
        private String mobileLink;
        private UserTaskSignType signType;
    }
}
