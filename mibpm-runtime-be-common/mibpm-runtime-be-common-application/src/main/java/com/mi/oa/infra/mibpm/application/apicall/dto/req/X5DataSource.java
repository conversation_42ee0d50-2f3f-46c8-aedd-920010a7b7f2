package com.mi.oa.infra.mibpm.application.apicall.dto.req;

import lombok.Data;

import java.util.Map;

/**
 * 通过X5请求获取的数据源
 *
 * <AUTHOR>
 * @date 2022/4/19 11:41
 */
@Data
public class X5DataSource {

    /**
     * 在流程管理后台应用管理中注册的应用
     */
    private String appCode;
    /**
     * 接口Path，不包括服务域名
     * 示例：/api/datasource
     */
    private String path;

    /**
     * 参数列表
     */
    private Map<String, Object> params;
}
