package com.mi.oa.infra.mibpm.application.proinst.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/22 17:09
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("查询流程查看权限请求体")
public class OpenQueryViewAuthReq {

    @ApiModelProperty(value = "业务唯一编码", required = false)
    private String businessKey;
    @ApiModelProperty(value = "授权的任务定义key", required = false)
    private String taskDefKey;
    @ApiModelProperty(value = "被授权人", required = true)
    private String userId;
}
