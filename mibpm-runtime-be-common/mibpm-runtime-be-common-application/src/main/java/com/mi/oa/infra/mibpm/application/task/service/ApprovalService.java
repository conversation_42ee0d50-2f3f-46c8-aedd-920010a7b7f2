package com.mi.oa.infra.mibpm.application.task.service;

import com.mi.oa.infra.mibpm.application.task.dto.req.CcTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.ClaimTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.CompleteTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.DelegateTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.PinTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.ReceiveTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.RejectTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.ReturnAfterSubmitReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.ReturnTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.ReturnToSubmitReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.SignTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.TransferTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.VoteTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailResp;
import com.mi.oa.infra.mibpm.common.enums.ClientEnum;
import com.mi.oa.infra.mibpm.common.model.UserTaskActivity;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;

import java.util.List;

/**
 * 审批服务，主要涉及中国式流程操作
 *
 * <AUTHOR>
 * @date 2022/3/14 15:28
 */
public interface ApprovalService {

    /**
     * 审批任务同意
     *
     * @param completeTaskReps 审批任务同意请求
     */
    void approve(CompleteTaskReq completeTaskReps);

    /**
     * 审批任务拒绝
     *
     * @param rejectTaskReq 审批任务拒绝请求
     */
    void reject(RejectTaskReq rejectTaskReq);

    /**
     * 审批任务转交
     *
     * @param transferTaskReq 审批任务转交请求
     */
    void transfer(TransferTaskReq transferTaskReq);

    /**
     * 审批任务加签
     *
     * @param req 审批任务加签请求
     */
    void sign(SignTaskReq req);

    /**
     * 审批任务抄送
     *
     * @param ccTaskReq 审批任务抄送请求
     */
    void carbonCopy(CcTaskReq ccTaskReq);

    /**
     * 审批任务退回
     *
     * @param returnTaskReq 审批任务退回请求
     */
    void returns(ReturnTaskReq returnTaskReq);

    /**
     * 审批任务加签到提交节点
     *
     * @param returnToSubmitReq 审批任务加签到提交节点请求
     */
    void returnToSubmit(ReturnToSubmitReq returnToSubmitReq);

    /**
     * 审批任务加签后重新提交
     *
     * @param returnAfterSubmitReq 审批任务加签后重新提交请求
     */
    void submit(ReturnAfterSubmitReq returnAfterSubmitReq);

    /**
     * 审批任务委派
     *
     * @param delegateTaskReq 审批任务委派请求
     */
    void delegate(DelegateTaskReq delegateTaskReq);

    /**
     * 审批任务详情
     *
     * @param taskId 任务ID
     * @param client
     * @return 审批任务详情
     */
    TaskDetailResp detail(String taskId, ClientEnum client);

    /**
     * 查询可以退回的节点
     *
     * @param taskId 任务ID
     * @return 可以退回的ID
     */
    List<UserTaskActivity> queryReturnActivities(String taskId);

    /**
     * 收藏任务
     *
     * @param completeTaskReps 审批任务同意请求
     */
    void pin(PinTaskReq completeTaskReps);

    /**
     * 跳转到指定节点
     *
     * @param taskId 任务ID
     * @param targetActivityId 跳转到的目标节点ID
     */
    void skip(String taskId, String targetActivityId);

    /**
     * 触发接收任务
     *
     * @param receiveTaskReq 接收任务请求
     */
    void receiveTask(ReceiveTaskReq receiveTaskReq);

    /**
     * 认领候选中的审批任务
     *
     * @param claimTaskReq
     */
    void claim(ClaimTaskReq claimTaskReq);

    /**
     * 跳过流程实例中的执行实例
     *
     * @param procInstId
     * @param targetActivityId
     */
    void skipExecution(String procInstId, String targetActivityId);

    /**
     * 保存任务签名
     *
     * @param taskId
     * @param operator
     * @param signature
     */
    void saveTaskSignature(String taskId, String operator, String signature, boolean isDefault);

    String getTaskSignature(String taskId, String operator);

    /**
     * 投票同意
     *
     * @param req
     */
    void voteAgree(VoteTaskReq req);

    /**
     * 投票拒绝
     *
     * @param req
     */
    void voteReject(VoteTaskReq req);

    /**
     * 投票弃权
     *
     * @param req
     */
    void voteAbstain(VoteTaskReq req);

    /**
    * <AUTHOR>
    * @description 统一待办任务同意
    * @date 上午10:16 2025/3/13
    * @param completeTaskReq
     * @param status
    * @return
    */
    void  extApprove(CompleteTaskReq completeTaskReq, UserTaskOperation status);

    /**
     * <AUTHOR>
     * @description 统一待办任务拒绝
     * @date 上午10:16 2025/3/13
     * @param rejectTaskReq
     * @param status
     * @return
     */
    void  extReject(RejectTaskReq rejectTaskReq, UserTaskOperation status);
}
