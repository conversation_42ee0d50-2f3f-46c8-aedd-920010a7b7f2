package com.mi.oa.infra.mibpm.application.proinst.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2022/5/7 14:48
 */
@Data
@ApiModel(value = "Open API审批任务重新提交请求", description = "Open API审批任务重新提交请求")
public class OpenReturnAfterSubmitReq {

    @ApiModelProperty(value = "任务ID", required = true)
    @NotBlank(message = "任务ID不能为空")
    private String taskId;
    @ApiModelProperty(value = "表单数据", required = false)
    private Map<String, Object> formData;
    @ApiModelProperty(value = "意见", required = false)
    private String comment;
    @ApiModelProperty(value = "操作用户", required = true)
    @NotBlank(message = "操作用户不能为空")
    private String operator;
    @ApiModelProperty(value = "流程变量", required = false)
    private Map<String, Object> variables;
    @ApiModelProperty(value = "是否根据表单权限更新数据", required = false)
    private boolean updateFormByPermission = false;
}
