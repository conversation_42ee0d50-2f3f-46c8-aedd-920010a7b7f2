package com.mi.oa.infra.mibpm.domain.mitask.factory;

import com.mi.flowable.external.api.ProcessInstanceRepresentation;
import com.mi.flowable.external.api.TaskRepresentation;
import com.mi.oa.infra.mibpm.domain.mitask.model.MiTaskDo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import org.flowable.engine.delegate.event.FlowableActivityEvent;
import org.flowable.engine.delegate.event.impl.FlowableEntityEventImpl;
import org.flowable.engine.delegate.event.impl.FlowableProcessStartedEventImpl;
import org.flowable.task.service.impl.persistence.entity.HistoricTaskInstanceEntity;
import org.flowable.task.service.impl.persistence.entity.TaskEntityImpl;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/14
 * @Description 引擎事件构建MiTaskDo工厂
 */
public interface MiTaskDoFactory {
    /**
     * @param taskDo 任务对象
     * @return MiTaskDo
     * <AUTHOR>
     * @date 2024/10/11
     * @description 任务发起。构建发起节点任务信息的MiTask
     **/
    MiTaskDo buildMiTaskDoByTaskDo(TaskDo taskDo);

    /**
     * @param flowableProcessStartedEvent 流程事件对象
     * @return MiTaskDo
     * <AUTHOR>
     * @date 2024/11/14
     * @description 流程发起。构建流程任务信息的MiTask
     **/
    MiTaskDo buildProcessMiTaskDoByProcessStartEvent(FlowableProcessStartedEventImpl flowableProcessStartedEvent);

    /**
     * @author: zoutongxu
     * @date: 2024/10/11
     * @description: 任务事件。通过引擎实体构建MiTskDo
     * @param: taskEntity 引擎当前实体
     * @param: isCreate 是否是任务创建事件
     * @param: isUpdate 是否是节点结束更新
     * @return: MiTaskDo
     **/
    MiTaskDo buildMiTaskDoByTaskEvent(TaskEntityImpl taskEntity, boolean isCreate, boolean isUpdate);



    MiTaskDo buildMiTaskDoByHistoryTask(HistoricTaskInstanceEntity taskEntity);

    /**
     * 构建竞签任务
     *
     * @param miTaskDo
     * @param candidates
     * @return MiTasks
     */
    List<MiTaskDo> buildCandidateMiTaskDos(MiTaskDo miTaskDo, TaskEntityImpl taskEntity, List<String> candidates);

    /**
     * @author: zoutongxu
     * @date: 2024/10/11
     * @description: 节点事件。构建节点下所有MiTask的任务完成时间
     * @param: flowableEntityEvent
     * @return: MiTaskProcessInstPo>
     **/
    List<MiTaskDo> buildMiTaskDoByActivityEntityEvent(FlowableActivityEvent flowableActivityEvent);

    /**
     * @author: zoutongxu
     * @date: 2024/10/11
     * @description: 流程事件。构建流程下所有任务的 流程完成终态 MiTaskDo列表
     * @param: flowableEntityEvent
     * @return: MiTaskProcessInstPo>
     **/
    List<MiTaskDo> buildMiTaskDoByProcessEvent(FlowableEntityEventImpl flowableEntityEventImpl);

    void buildMiTaskDo(ProcessInstanceRepresentation processInstanceRepresentation, MiTaskDo processInstanceEntity);

    /**
     * 构建任务执行对象
     *
     * @param taskRepresentation 任务表示对象
     */
    MiTaskDo buildMiTaskDo(ProcessInstanceRepresentation processInstanceRepresentation,
                           TaskRepresentation taskRepresentation);
}
