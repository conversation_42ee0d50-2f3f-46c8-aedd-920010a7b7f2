package com.mi.oa.infra.mibpm.domain.userconfig.ability;

import com.mi.oa.infra.mibpm.domain.userconfig.model.UserConfigDo;

/**
 * 用户个性化配置能力
 *
 * @author: qiuzhipeng
 * @Date: 2022/3/21 17:16
 */
public interface UserConfigFillAbility {

    /**
     * 填充用户信息
     * @param userConfigDo
     */
    void fillUserInfo(UserConfigDo userConfigDo);

    /**
     * 填充委托基本信息
     * @param userConfigDo
     */
    void fillDelegationMetaInfo(UserConfigDo userConfigDo);

    /**
     * 填充连续审批配置
     * @param userConfigDo
     */
    void fillAutoNextApprovalConfig(UserConfigDo userConfigDo);

    /**
     * 填充消息推送配置
     * @param userConfigDo
     */
    void fillMessagePushRuleConfig(UserConfigDo userConfigDo);


}
