package com.mi.oa.infra.mibpm.domain.message.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @author: qiuzp
 * @Date: 2021/4/7 18:57
 * @Description:
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Content {
    /**
     * 用户name
     */
    private String username;
    /**
     * 部门信息
     */
    private String deptInfo;
    /**
     *是否是审批人 默认false
     */
    @JsonProperty("isApprove")
    protected boolean isApprove;
    /**
     * 摘要信息
     */
    private List<Map<String, String>> summaries;
}
