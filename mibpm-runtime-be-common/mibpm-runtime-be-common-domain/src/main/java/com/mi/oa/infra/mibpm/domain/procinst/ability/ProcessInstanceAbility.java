package com.mi.oa.infra.mibpm.domain.procinst.ability;

import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;

/**
 * 流程实例能力
 *
 * <AUTHOR>
 * @date 2022/2/9 11:44
 */
public interface ProcessInstanceAbility {

    /**
     * 启动流程实例
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void start(ProcessInstanceDo processInstanceDo);

    /**
     * 终止流程实例
     *
     * @param processInstanceDo 流程实例ID
     */
    void terminate(ProcessInstanceDo processInstanceDo);

    /**
     * 撤回流程实例
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void recall(ProcessInstanceDo processInstanceDo);

    /**
     * 根据 businessKey 查询流程实例（检查是否存在）
     *
     * @param businessKey 业务唯一编码
     * @return 流程实例领域对象
     */
    ProcessInstanceDo queryProcessInstanceByBusinessKey(String businessKey);

    /**
     * 查询流程实例（检查是否存在）
     *
     * @param processInstanceId 流程实例ID
     * @return 流程实例领域对象
     */
    ProcessInstanceDo queryProcessInstance(String processInstanceId);

    /**
     * 根据 businessKey 查询历史流程实例（检查是否存在）
     *
     * @param businessKey 业务唯一编码
     * @return 流程实例领域对象
     */
    ProcessInstanceDo queryHistoricProcessInstanceByBusinessKey(String businessKey);

    /**
     * 指定流程实例加载变量
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void loadProcessVariables(ProcessInstanceDo processInstanceDo);

    /**
     * 设置流程变量
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void setProcessVariables(ProcessInstanceDo processInstanceDo);


    /**
     * 设置流程变量
     *
     * @param processInstanceDo 流程实例领域对象
     * @param variableName      流程变量名
     * @param value             变量值
     */
    void setProcessVariable(ProcessInstanceDo processInstanceDo, String variableName, Object value);

    /**
     * 指定流程实例加载流程发起人对象
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void loadProcessStartUser(ProcessInstanceDo processInstanceDo);

    /**
     * 加载流程包装对象
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void loadProcessWrapper(ProcessInstanceDo processInstanceDo);

    /**
     * 校验流程实例是否已经启用
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void checkProcessStatus(ProcessInstanceDo processInstanceDo);
}
