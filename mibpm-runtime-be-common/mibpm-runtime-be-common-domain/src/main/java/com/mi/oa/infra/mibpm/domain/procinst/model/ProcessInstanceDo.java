package com.mi.oa.infra.mibpm.domain.procinst.model;

import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.flowable.extension.model.ProcessWrapper;
import com.mi.oa.infra.oaucf.core.domain.DomainObject;
import lombok.*;

import java.time.ZonedDateTime;
import java.util.Map;

/**
 * 流程实例
 *
 * <AUTHOR>
 * @date 2021/10/14 16:14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProcessInstanceDo extends DomainObject<ProcessInstanceDo> {

    /**
     * 分类编码
     */
    protected String categoryCode;
    /**
     * 模型编码
     */
    protected String modelCode;
    /**
     * 流程定义ID
     */
    protected String processDefinitionId;
    /**
     * 流程定义名称
     */
    protected String processDefinitionName;
    /**
     * 流程定义版本
     */
    protected Integer processDefinitionVersion;
    /**
     * 业务唯一编码
     */
    protected String businessKey;
    /**
     * 流程实例ID
     */
    protected String processInstanceId;
    /**
     * 流程实例名称
     */
    protected String processInstanceName;
    /**
     * 流程是否暂停
     */
    protected Boolean suspensionState;
    /**
     * 流程状态
     */
    protected ProcessInstanceStatus processInstanceStatus;
    /**
     * 描述
     */
    protected String description;
    /**
     * 流程开始时间
     */
    protected ZonedDateTime startTime;
    /**
     * 流程结束时间
     */
    protected ZonedDateTime endTime;
    /**
     * 发起人
     */
    protected String startUserId;
    /**
     * 发起人
     */
    protected BpmUser startUser;
    /**
     * 流程变量（需要时调用流程服务进行加载）
     */
    protected Map<String, Object> processVariables;
    /**
     * 当前操作人
     */
    protected BpmUser operator;
    /**
     * 流程包装对象
     */
    protected ProcessWrapper processWrapper;

    @Override
    protected boolean sameIdentityAs(ProcessInstanceDo processInstanceDo) {
        return false;
    }
}
