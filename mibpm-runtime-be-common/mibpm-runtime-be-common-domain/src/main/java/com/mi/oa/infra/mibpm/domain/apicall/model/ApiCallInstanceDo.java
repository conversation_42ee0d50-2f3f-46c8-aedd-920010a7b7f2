package com.mi.oa.infra.mibpm.domain.apicall.model;

import com.mi.oa.infra.mibpm.common.enums.ApiCallProtocolEnum;
import com.mi.oa.infra.oaucf.core.domain.DomainObject;
import lombok.Data;
import org.springframework.http.HttpStatus;

/**
 *
 * 服务调用 实例对象
 * @author: qiuzhipeng
 * @Date: 2022/2/10 10:33
 */

@Data
public class ApiCallInstanceDo extends DomainObject<ApiCallInstanceDo> {

    public Long id;
    /**
     * 消息id
     */
    public String sequenceId;

    /**
     * 消息模版code
     */
    protected String apiTemplateCode;

    /**
     * 消息投递次数
     */
    protected Integer deliveryTimes;

    /**
     * 所属应用
     */
    protected String appCode;

    /**
     * 流程实例ID
     */
    protected String instanceId;

    /**
     * model code
     */
    protected String modelCode;

    /**
     * 请求地址
     */
    protected String url;

    /**
     * 请求协议
     */
    protected ApiCallProtocolEnum protocol;

    /**
     * 请求header
     */
    protected String header;

    /**
     * 是否同步调用
     */
    protected Boolean isSync;

    /**
     * appId
     */
    protected String appId;

    /**
     * appKey
     */
    protected String appKey;

    /**
     * 请求方法
     */
    protected String method;

    /**
     * 请求体
     */
    protected String payload;

    /**
     * 响应体
     */
    protected String response;

    /**
     * 响应状态码
     */
    protected HttpStatus httpStatus;

    /**
     * 耗时
     */
    protected Long cost;

    /**
     * 是否记录日志
     */
    protected Boolean isRecordLog;
    /**
     * 回调失败时通知管理员
     */
    protected Boolean warnOnFail;
    /**
     * 事件编码
     */
    protected String eventCode;

    /**
     * 是否成功
     *
     * @return boolean
     */
    public boolean isSuccess(){
        return this.httpStatus == HttpStatus.OK;
    }

    @Override
    protected boolean sameIdentityAs(ApiCallInstanceDo apiCallInstanceDo) {
        return this.equals(apiCallInstanceDo);
    }
}
