package com.mi.oa.infra.mibpm.domain.apicall.ability;

import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;

import java.util.Map;

/**
 * 远程请求变量构建
 *
 * @author: qiuzhipeng
 * @Date: 2022/3/14 14:48
 */
public interface ApiCallVariablesBuildAbility {

    /**
     * 构建请求变量
     * 包含流程信息与任务信息
     *
     * @param processInstanceDo
     * @param taskDo
     * @return variables
     */
    Map<String, Object> buildBaseApiCallVariables(ProcessInstanceDo processInstanceDo, TaskDo taskDo);
}
