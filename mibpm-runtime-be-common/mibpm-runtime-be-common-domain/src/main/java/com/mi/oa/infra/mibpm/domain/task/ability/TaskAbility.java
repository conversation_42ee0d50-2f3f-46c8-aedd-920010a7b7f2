package com.mi.oa.infra.mibpm.domain.task.ability;

import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.TaskLink;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.flowable.extension.model.SignAddTypeEnum;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import org.flowable.bpmn.model.BpmnModel;

import java.util.List;

/**
 * 任务能力
 *
 * <AUTHOR>
 * @date 2022/2/9 12:49
 */
public interface TaskAbility {

    /**
     * 查询待处理任务（检查是否存在）
     *
     * @param taskId 待处理任务ID
     * @return 待处理任务
     */
    TaskDo queryTask(String taskId);

    /**
     * 查询历史任务（检查是否存在）
     *
     * @param taskId 任务ID
     * @return 历史任务
     */
    TaskDo queryHistoricTask(String taskId);

    /**
     * 查询任务（先查待办再查已办，都查不到返回null，不抛异常）
     *
     * @param taskId 任务ID
     * @return 任务
     */
    TaskDo queryTaskDo(String taskId);

    /**
     * 查询查询第一个任务节点的任务
     *
     * @param procInstId 流程实例id
     * @return 待处理任务
     */
    TaskDo queryFirstTask(String procInstId);


    /**
     * 查询流程实例下的任务列表
     *
     * @param procInstId 流程实例id
     * @return 待处理任务
     */
    List<TaskDo> queryTaskList(String procInstId);

    /**
     * 查询流程实例下的接收任务列表
     *
     * @param procInstId 流程实例id
     * @return 待处理的接收任务
     */
    List<TaskDo> queryReceiveTaskList(String procInstId);

    /**
     * 加载流程变量
     *
     * @param taskDo 任务领域对象
     */
    void loadTaskVariables(TaskDo taskDo);

    /**
     * 加载任务的本地变量
     *
     * @param taskDo 任务对象
     */
    void loadTaskVariablesLocal(TaskDo taskDo);

    /**
     * 加载用户任务节点配置
     *
     * @param taskDo 任务领域对象
     */
    void loadUserTaskWrapper(TaskDo taskDo);

    /**
     * 加载用户任务节点配置
     *
     * @param taskDo    任务领域对象
     * @param bpmnModel 流程模型
     */
    void loadUserTaskWrapper(TaskDo taskDo, BpmnModel bpmnModel);

    /**
     * 加载表单数据
     *
     * @param taskDo 任务领域对象
     */
    void loadFromData(TaskDo taskDo);

    /**
     * 加载节点类型
     *
     * @param taskDo 任务领域对象
     */
    void loadActivityType(TaskDo taskDo);

    /**
     * 加载节点类型
     *
     * @param taskDo    任务领域对象
     * @param bpmnModel 流程模型
     */
    void loadActivityType(TaskDo taskDo, BpmnModel bpmnModel);

    /**
     * 完成任务
     *
     * @param taskDo 任务领域对象
     */
    void completeTask(TaskDo taskDo);

    /**
     * 退回
     *
     * @param taskDo 任务领域对象
     * @param targetActivityId 目标流程节点的ID
     */
    void returnTask(TaskDo taskDo, String targetActivityId);

    /**
     * 委派任务给指定人
     *
     * @param taskDo 任务领域对象
     * @param userId 委托人ID
     */
    void delegateTask(TaskDo taskDo, String userId);

    /**
     * 处理一个委派任务
     *
     * @param taskDo 任务领域对象
     */
    void resolveTask(TaskDo taskDo);

    /**
     * 转交任务
     *
     * @param taskDo 任务领域对象
     */
    void transferTask(TaskDo taskDo);

    /**
     * 加签任务
     *
     * @param taskDo   任务
     * @param assignee
     * @param signType
     */
    void signTask(TaskDo taskDo, List<BpmUser> assignee, SignAddTypeEnum signType);

    /**
     * 流程退回到发起节点，重新提交后会再次返回原来的任务节点
     *
     * @param taskDo 任务
     */
    void returnTaskToStartEvent(TaskDo taskDo);

    /**
     * 重新提交后流程继续，如果是加签后的重新提交再次返回原来的任务节点，退回到发起节点后重新提交按照流程图继续流转
     *
     * @param taskDo 任务
     */
    void continueTaskFromStartEvent(TaskDo taskDo);

    /**
     * 保存任务，将给定的任务保存到持久数据存储中。如果任务已经存在于持久存储中，那么它将被更新。在保存新任务之后，传递给这个方法的任务实例将
     * 使用新创建任务的id进行更新。
     *
     * @param taskDo 任务领域对象
     */
    void saveTask(TaskDo taskDo);

    /**
     * 设置流程变量
     *
     * @param taskDo 任务领域对象
     */
    void setVariables(TaskDo taskDo);

    /**
     * 设置流程变量
     *
     * @param taskDo 任务领域对象
     */
    void setAssignee(TaskDo taskDo);

    /**
     * 收藏
     *
     * @param taskDo 任务领域对象
     */
    void pinTask(TaskDo taskDo);

    /**
     * 获取任务详情链接
     *
     * @param taskDo 任务领域对象
     * @return 任务详情链接
     */
    TaskLink getTaskDetailLink(TaskDo taskDo);

    /**
     * 保存表单数据
     *
     * @param taskDo 任务领域对象
     */
    void saveFormData(TaskDo taskDo);

    /**
     * 接收任务
     *
     * @param taskDo 任务领域对象
     */
    void receiveTask(TaskDo taskDo);

    /**
     * 认领任务
     *
     * @param taskDo 任务
     */
    void claimTask(TaskDo taskDo);

    /**
     * 加载任务候选人
     *
     * @param taskDo 任务领域对象
     */
    void loadTaskCandidates(TaskDo taskDo);

    /**
     * 加载预测任务
     *
     * @param taskDo       领域对象
     * @param historyTasks 历史任务
     */
    void loadPredictTasks(TaskDo taskDo, List<TaskDo> historyTasks);


    /**
     * 加载预测任务
     *
     * @param taskDo          领域对象
     * @param historyTaskKeys 历史任务
     */
    List<TaskDo> loadPredictTasksWithKeys(TaskDo taskDo, List<String> historyTaskKeys);

    /**
     * 同步加载预测任务
     *
     * @param taskDo
     * @param historyTaskKeys
     * @return
     */
    List<TaskDo> syncLoadPredictTasksWithKeys(TaskDo taskDo, List<String> historyTaskKeys);

    /**
     * 计算任务详情操作按钮
     *
     * @param taskDo            领域对象
     * @param processInstanceDo 流程实例
     * @return 按钮集合
     */
    List<UserTaskOperation> calculateTaskOperations(TaskDo taskDo, ProcessInstanceDo processInstanceDo);
}
