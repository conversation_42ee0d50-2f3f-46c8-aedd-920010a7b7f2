package com.mi.oa.infra.mibpm.domain.procinst.ability;

import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.AccountAuthorityResp;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/9 19:16
 */
public interface CheckProcessInstanceAbility {

    /**
     * 检查发起人是否缺失
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void checkProcessInstanceStartUser(ProcessInstanceDo processInstanceDo);

    /**
     * 检查发起人是否具有发起流程的权限
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void checkProcessInstanceStartPermission(ProcessInstanceDo processInstanceDo);

    /**
     * 检查流程实例的模型编码
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void checkProcessInstanceModelCode(ProcessInstanceDo processInstanceDo);

    /**
     * 检查流程实例的流程定义ID
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void checkProcessInstanceProcDefId(ProcessInstanceDo processInstanceDo,Integer version);


    /**
     * 检查流程实例名称
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void checkProcessInstanceName(ProcessInstanceDo processInstanceDo);

    /**
     * 检查流程实例业务唯一ID
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void checkProcessInstanceBusinessKey(ProcessInstanceDo processInstanceDo);

    /**
     * 检查当前登录人是否具有终止流程的权限,流程发起人和超级管理员拥有权限
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void checkProcessInstanceTerminatePermission(ProcessInstanceDo processInstanceDo, List<TaskDo> taskDos);

    /**
     * 检查当前登录人是否具有撤回流程的权限,流程发起人和超级管理员拥有权限
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void checkProcessInstanceRecallPermission(ProcessInstanceDo processInstanceDo);

    /**
     * 检查当前登录人是否具有查看流程监控的权限,流程管理员和超级管理员拥有权限
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void checkProcessInstanceMonitorPermission(ProcessInstanceDo processInstanceDo);

    /**
     * 检查当前登录人是否具有查看流程监控的权限,流程管理员和超级管理员拥有权限
     *
     * @param processInstanceDo 流程实例领域对象
     * @param accountAuthority 用户拥有的数据权限
     */
    void checkProcessInstanceMonitorPermission(ProcessInstanceDo processInstanceDo, AccountAuthorityResp accountAuthority);

    boolean checkModelPermission(AccountAuthorityResp accountAuthority, String categoryCode, String modelCode);

    /**
     * 检查流程实例是否已经发起
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void checkProcessInstanceStarted(ProcessInstanceDo processInstanceDo);

    /**
     * 检查操作人是否缺失
     *
     * @param processInstanceDo 流程实例领域对象
     */
    void checkProcessInstanceOperator(ProcessInstanceDo processInstanceDo);

    /**
     * 校验用户是否有导出对应类型流程实例数据的权限
     *
     * @param modelCode
     */
    void checkProcessExportPermission(String modelCode);

    /**
     * 校验表单数据是否合法
     *
     * @param processInstanceDo 流程实例领域对象
     * @param formData 表单数据
     */
    void checkFormData(ProcessInstanceDo processInstanceDo, Map<String, Object> formData);
}
