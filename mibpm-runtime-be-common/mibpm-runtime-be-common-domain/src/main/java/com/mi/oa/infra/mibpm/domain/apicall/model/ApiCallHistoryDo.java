package com.mi.oa.infra.mibpm.domain.apicall.model;

import com.mi.oa.infra.oaucf.core.domain.DomainObject;
import lombok.Builder;
import lombok.Data;

import java.time.ZonedDateTime;

/**
 * @author: qiuzhipeng
 * @Date: 2022/3/10 15:07
 */

@Data
@Builder
public class ApiCallHistoryDo extends DomainObject<ApiCallHistoryDo> {

    /**
     * id
     */
    protected Long id;

    /**
     * 消息ID
     */
    protected String sequenceId;

    /**
     * 所属应用
     */
    protected String appCode;

    /**
     * api id
     */
    protected String apiId;

    /**
     * 流程实例ID
     */
    protected String instanceId;

    /**
     * modelcode
     */
    protected String modelCode;

    /**
     * 请求地址
     */
    protected String url;

    /**
     * 请求体
     */
    protected String request;

    /**
     * 响应体
     */
    protected String response;

    /**
     * 耗时
     */
    protected Long cost;

    /**
     * 请求状态
     */
    protected Byte status;

    /**
     * 请求上下文信息
     */
    protected String callContext;

    /**
     * 创建时间
     */
    protected ZonedDateTime createTime;

    @Override
    protected boolean sameIdentityAs(ApiCallHistoryDo apiCallHistoryDo) {
        return this.equals(apiCallHistoryDo);
    }
}

