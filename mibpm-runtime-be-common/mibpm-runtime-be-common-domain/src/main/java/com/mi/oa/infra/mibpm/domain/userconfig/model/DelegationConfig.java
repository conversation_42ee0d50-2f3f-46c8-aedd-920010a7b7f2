package com.mi.oa.infra.mibpm.domain.userconfig.model;

import lombok.Builder;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;

/**
 * 委托
 *
 * @author: qiuzhipeng
 * @Date: 2022/3/21 12:37
 */
@Data
@Builder
public class DelegationConfig {

    /**
     * id
     */
    private Long id;
    /**
     * 委托人
     */
    private String userId;
    /**
     * 被委托人
     */
    private String delegationUserId;
    /**
     * modelCode
     */
    private List<String> modelCode;
    /**
     * 全部流程排除部分委托的流程信息
     */
    private List<String> exclusionModelCodes;
    /**
     * 委托开始时间
     */
    private ZonedDateTime startTime;
    /**
     * 委托结束时间
     */
    private ZonedDateTime endTime;
    /**
     * 委托类型 0全部 1流程
     */
    private int type;
    /**
     * 委托状态 -1 无效  1 有效
     */
    private int status;
    /**
     * 更新时间
     */
    private ZonedDateTime updateTime;
    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 委托理由
     */
    private String delegationReason;
    /**
     * 表示流程的范围或适用性，1: 属于 2: 不属于 0: 不限
     */
    private Integer delegationScope;
    /**
     * 表示流程发起人的部门
     */
    private List<String> initiatingDeptCodes;
    /**
     * 委托流程实例化id
     */
    private String delegationProcessInstanceId;
    /**
     * 委托需求提出人
     */
    private String delegationRequirementProposer;
    /**
     * 创建时间
     */
    private ZonedDateTime createTime;
    /**
     * 兼容旧委托流程字段
     */
    private String processKey;
}
