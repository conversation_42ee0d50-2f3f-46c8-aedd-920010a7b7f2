package com.mi.oa.infra.mibpm.domain.mitask.service;

import com.mi.flowable.idm.api.AppInfo;
import com.mi.oa.infra.mibpm.common.model.CardBot;
import com.mi.oa.infra.mibpm.common.model.MessageResult;

/**
 * <AUTHOR>
 * @DateTime 2024/10/23 20:59
 * @Description 主要为bpm2.0迁移过来的统一待办卡片发送
 **/
public interface MiTaskExternalDomainService {

    /**
     * <AUTHOR>
     * @Description 发送飞书卡片
     * @DateTime 2024/10/24 11:13
     * @Params [userId]
     * @Return void
     */
    MessageResult doSendMessageCard(CardBot cardBotBO, AppInfo appInfo, boolean isSend);
}
