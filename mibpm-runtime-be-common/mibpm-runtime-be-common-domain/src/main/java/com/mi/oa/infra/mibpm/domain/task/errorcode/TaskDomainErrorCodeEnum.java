package com.mi.oa.infra.mibpm.domain.task.errorcode;

import com.mi.oa.infra.oaucf.core.exception.DomainErrorCode;

/**
 * by roger
 */
public enum TaskDomainErrorCodeEnum implements DomainErrorCode {
    // 领域层未知错误
    DOMAIN_UNKNOWN_ERROR(1, "领域层未知错误"),
    // 任务不存在
    TASK_NOT_EXISTS(2, "任务 %s 不存在"),
    // 没有完成任务权限
    NO_PERMISSION_COMPLETE_TASK(3, "%s 没有完成任务权限"),
    // 任务转交人不能是自己
    TASK_TRANSFER_USER_CANNOT_BE_HIMSELF(4, "任务转交人不能是自己"),
    //  没有查看任务权限
    NO_PERMISSION_READ_TASK(5, "%s 没有查看任务权限"),
    // 任务操作人缺失
    TASK_OPERATOR_IS_EMPTY(6, "任务操作人缺失"),
    // 不允许退回到 %s 节点
    NOT_ALLOW_RETURN_ACTIVITY(7, "不允许退回到 %s 节点"),
    //
    REMOTE_CHECK_CONDITION_ERROR(8, "%s"),
    // 业务校验异常，请咨询业务系统人员
    REMOTE_CHECK_CONDITION_DEFAULT_ERROR(9, "业务校验异常，请咨询业务系统人员"),
    // 认领错误, 候选人为空
    TASK_CLAIM_CANDIDATE_EMPTY(10, "认领错误，候选人为空"),
    // 任务已被领取过
    TASK_CLAIM_ALREADY_CLAIMED(11, "认领错误，任务已被领取"),
    // 操作人不是候选人
    TASK_CLAIM_NOT_IN_CANDIDATES(12, "认领错误，操作人不是候选人"),
    // 加签校验错误
    TASK_SIGN_ERROR(13, "加签校验错误");


    /**
     * 具体错误码
     */
    private int errCode;
    /**
     * 描述
     */
    private String errDesc;

    @Override
    public int getBizCode() {
        return 0;
    }

    @Override
    public int getErrorCode() {
        return errCode;
    }

    @Override
    public String getErrDesc() {
        return this.errDesc;
    }

    /**
     * 构造方法
     *
     * @param errCode
     * @param errDesc
     */
    TaskDomainErrorCodeEnum(int errCode, String errDesc) {
        this.errCode = errCode;
        this.errDesc = errDesc;
    }


}
