package com.mi.oa.infra.mibpm.domain.message.ability;

import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo;
import com.mi.oa.infra.mibpm.domain.userconfig.model.MessagePushRuleConfig;

/**
 * 消息发送规则计算
 *
 * @author: qiuzhipeng
 * @Date: 2022/3/16 20:35
 */
public interface MessageRuleAbility {

    /**
     * 是否发送飞书消息
     *
     * @param larkMessageDo
     * @return boolean
     */
    boolean isSendLarkMessage(LarkMessageDo larkMessageDo);

    /**
     * 统一待办是否发送飞书消息
     *
     * @param userId
     * @return boolean
     */
    boolean isSendLarkMessage(String userId);

    /**
     * 是否发送待办飞书消息
     *
     * @param larkMessageDo
     * @return boolean
     */
    boolean isSendTodoLarkMessage(LarkMessageDo larkMessageDo);

    /**
     * 是否发送审批通知飞书消息
     *
     * @param larkMessageDo
     * @return boolean
     */
    boolean isSendApprovalLarkMessage(LarkMessageDo larkMessageDo);

    /**
     * 是否发送抄送通知飞书消息
     *
     * @param larkMessageDo
     * @return boolean
     */
    boolean isSendCcTaskLarkMessage(LarkMessageDo larkMessageDo);

    /**
     * 是否发送待办聚合通知飞书消息
     *
     * @param messagePushRuleConfig
     * @param time
     * @return boolean
     */
    boolean isSendWaitNumLarkMessage(MessagePushRuleConfig messagePushRuleConfig, String time);
}
