package com.mi.oa.infra.mibpm.domain.task.model;

import com.mi.oa.infra.mibpm.common.enums.AutoOperationTypeEnum;
import com.mi.oa.infra.mibpm.common.enums.ClientEnum;
import com.mi.oa.infra.mibpm.flowable.extension.model.SignAddTypeEnum;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

/**
 * 任务附加属性
 *
 * <AUTHOR>
 * @date 2022/3/11 10:38
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class TaskAttribute {

    /**
     * 任务操作
     */
    private UserTaskOperation operation;
    /**
     * 评论
     */
    private String comment;
    private String commentEn;
    /**
     * 处理任务的客户端
     */
    private ClientEnum client;
    /**
     * 加签任务的加签类型（只有加签才有值）
     */
    private SignAddTypeEnum signAddType;

    /**
     * 流程模型编码
     */
    private String modelCode;
    /**
     * 流程实例业务key
     */
    private String businessKey;
    /**
     * 流程实例名称
     */
    private String processInstanceName;
    /**
     * 流程实例发起人
     */
    private String startUserId;
    /**
     * 流程实例发起时间
     */
    private Date startTime;
    /**
     * 百特搭表单key
     */
    private Map<String, String> formKey;
    /**
     *
     */
    private AutoOperationTypeEnum autoOperationType;

    private Boolean isVetoTask;

}
