package com.mi.oa.infra.mibpm.domain.message.service;

import com.larksuite.appframework.sdk.client.message.card.Card;
import com.larksuite.appframework.sdk.client.message.card.module.Module;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo;

import javax.annotation.Nullable;
import java.util.List;

/**
 * 飞书通知服务
 *
 * @author: qiuzhipeng
 * @Date: 2022/3/15 18:29
 */
public interface LarkMessageDomainService {


    /**
     * 检查实例对象
     *
     * @param larkMessageDo
     */
    void checkLarkMessage(LarkMessageDo larkMessageDo);

    /**
     * 构建飞书消息结构体json
     *
     * @param larkMessageDo 消息领域对象
     * @return card 飞书消息结构体
     */
    Card buildLarkMessageCard(LarkMessageDo larkMessageDo);

    /**
     * 发送消息对象
     *
     * @param card
     * @param larkMessageDo
     * @return
     */
    void sendMessageCard(LarkMessageDo larkMessageDo, @Nullable Card card);


    /**
     * 发送待办消息
     *
     * @param larkMessageDo
     * @return
     */
    void sendTodoMessageCard(LarkMessageDo larkMessageDo);

    /**
     * 发送审批进度消息
     *
     * @param larkMessageDo
     * @return
     */
    void sendApprovalMessageCard(LarkMessageDo larkMessageDo);

    /**
     * 发送待办任务聚合消息
     *
     * @param larkMessageDo
     * @return
     */
    void sendWaitNumMessageCard(LarkMessageDo larkMessageDo);

    /**
     * 发送催办消息
     *
     * @param larkMessageDo
     * @return
     */
    void sendUrgeMessageCard(LarkMessageDo larkMessageDo);


    /**
     * 发送催办消息
     *
     * @param larkMessageDo
     * @return
     */
    void sendMessageCard(LarkMessageDo larkMessageDo);

    /**
     * 发送催办消息
     *
     * @param larkMessageDo
     * @return
     */
    void sendCcNotifyMessageCard(LarkMessageDo larkMessageDo);


    /**
     * 提醒管理员填写没有负责人的启用流程
     */
    void sendReminderMessageCard(LarkMessageDo larkMessageDo);


    /**
     * 离职转岗消息
     */
    void sendHanoverMessageCard(LarkMessageDo larkMessageDo);


    /**
     * 建立流程负责人提醒卡片
     *
     * @param larkMessageDo
     * @return
     */
    Card buildProcessOwnerReminderCard(LarkMessageDo larkMessageDo);

    /**
     * 填入跳转链接按钮
     *
     * @param larkMessageDo
     * @param modules
     */
    void fillUrlActions(LarkMessageDo larkMessageDo, List<Module> modules);
}
