package com.mi.oa.infra.mibpm.domain.task.service;

import com.mi.oa.infra.mibpm.common.enums.AutoOperationTypeEnum;
import com.mi.oa.infra.mibpm.common.exception.DomainException;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.TaskLink;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskAttribute;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.flowable.extension.model.SignAddTypeEnum;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import org.flowable.bpmn.model.BpmnModel;

import java.util.List;
import java.util.Map;

/**
 * 任务领域服务
 *
 * <AUTHOR>
 * @date 2022/2/9 10:45
 */
public interface TaskDomainService {

    /**
     * 查询待处理任务（检查是否存在）
     *
     * @param taskId 待处理任务ID
     * @return 待处理任务
     * @throws DomainException 任务不存在则抛异常
     */
    TaskDo queryTask(String taskId);

    /**
     * 查询历史任务（检查是否存在）
     *
     * @param taskId 任务ID
     * @return 历史任务
     * @throws DomainException 任务不存在则抛异常
     */
    TaskDo queryHistoricTask(String taskId);

    /**
     * 查询任务（先查待办再查已办，都查不到返回null，不抛异常）
     *
     * @param taskId 任务ID
     * @return 任务
     */
    TaskDo queryTaskDo(String taskId);

    /**
     * 查询查询第一个任务节点的任务
     *
     * @param procInstId 流程实例id
     * @return 待处理任务
     */
    TaskDo queryFirstTask(String procInstId);

    /**
     * 查询流程实例下的任务列表
     *
     * @param procInstId 流程实例id
     * @return 待处理任务
     */
    List<TaskDo> queryTaskList(String procInstId);

    /**
     * 查询流程实例下的接收任务列表
     *
     * @param procInstId 流程实例id
     * @return 待处理的接收任务
     */
    List<TaskDo> queryReceiveTaskList(String procInstId);

    /**
     * 检查用户完成任务的权限
     *
     * @param taskDo 待处理任务
     */
    void checkCompleteTaskPermission(TaskDo taskDo);

    /**
     * 检查是否满足业务提交条件
     *
     * @param taskDo 待处理任务
     */
    void checkCompleteRemoteCondition(TaskDo taskDo);

    /**
     * 检查修改审批人的权限
     *
     * @param taskDo 待处理任务
     */
    void checkSetAssigneeTaskPermission(TaskDo taskDo);

    /**
     * 校验是否是可读
     *
     * @param taskDo
     */
    void checkTaskReadPermission(TaskDo taskDo);

    /**
     * 校验是否是历史审批人
     *
     * @param taskDo
     * @return true 是历史审批人
     */
    boolean checkHistoricTaskRead(TaskDo taskDo);

    /**
     * 检查任务是否已经完成
     *
     * @param taskDo 任务领域对象
     * @return 任务是否已经完成
     */
    boolean checkTaskCompleted(TaskDo taskDo);

    /**
     * 检查任务是否可以加签
     *
     * @param taskDo 任务领域对象
     * @param signUsers
     */
    void checkTaskAddSign(TaskDo taskDo, SignAddTypeEnum signType, List<BpmUser> signUsers);

    /**
     * 检查任务领取的权限
     *
     * @param taskDo 任务领域对象
     */
    void checkTaskClaimPermission(TaskDo taskDo);

    /**
     * 检查任务是否可以退回
     *
     * @param taskDo 任务领域对象
     * @param targetActivityId 指定退回节点ID
     */
    void checkReturnActivities(TaskDo taskDo, String targetActivityId);

    /**
     * 填充任务属性
     *
     * @param taskDo 任务领域对象
     * @param taskAttribute 任务属性
     */
    void fillTaskAttribute(TaskDo taskDo, TaskAttribute taskAttribute);

    /**
     * 填充流程变量
     *
     * @param taskDo 任务领域对象
     * @param formData 表单数据
     * @param isFastApproval 是否是快捷审批，如果是快捷审批不处理变量
     */
    void fillTaskVariables(TaskDo taskDo, Map<String, Object> variables, Map<String, Object> formData, boolean isFastApproval);

    /**
     * 填充流程变量
     *
     * @param taskDo 任务领域对象
     * @param variableName 变量名称
     * @param variableValue 变量值
     */
    void fillTaskVariable(TaskDo taskDo, String variableName, Object variableValue);

    /**
     * 填充任务操作人
     *
     * @param task 任务领域对象
     * @param operator 当前操作人
     */
    void fillTaskOperator(TaskDo task, BpmUser operator);

    /**
     * 填充任务处理人
     *
     * @param task 任务领域对象
     * @param assignee 任务处理人
     */
    void fillTaskAssignee(TaskDo task, String assignee);

    /**
     * 填充表单数据
     *
     * @param task 任务领域对象
     * @param data 表单数据
     * @param isFastApproval 是否是快捷审批，是快捷审批的不填充表单
     */
    void fillFormData(TaskDo task, Map<String, Object> data, boolean isFastApproval, boolean byPermission);

    /**
     * 委派任务给指定人
     *
     * @param taskDo 任务
     * @param assignee 委托人ID
     */
    void delegateTask(TaskDo taskDo, String assignee);

    /**
     * 处理一个委托任务
     *
     * @param taskDo 任务
     */
    void resolveTask(TaskDo taskDo);

    /**
     * 完成任务
     *
     * @param taskDo 任务
     */
    void completeTask(TaskDo taskDo);

    /**
     * 退回任务到指定节点
     *
     * @param taskDo 任务
     * @param targetActivityId 目标流程节点的ID
     */
    void returnTask(TaskDo taskDo, String targetActivityId);

    /**
     * 转交任务
     *
     * @param taskDo 任务
     */
    void transferTask(TaskDo taskDo);

    /**
     * 加签任务
     *
     * @param taskDo 任务
     * @param assignee
     * @param signType
     */
    void signTask(TaskDo taskDo, List<BpmUser> assignee, SignAddTypeEnum signType);

    /**
     * 流程退回到发起节点，重新提交后会再次返回原来的任务节点
     *
     * @param taskDo 任务
     */
    void returnTaskToStartEvent(TaskDo taskDo);

    /**
     * 重新提交后流程继续，如果是加签后的重新提交再次返回原来的任务节点，退回到发起节点后重新提交按照流程图继续流转
     *
     * @param taskDo 任务
     */
    void continueTaskFromStartEvent(TaskDo taskDo);

    /**
     * 保存任务，将给定的任务保存到持久数据存储中。如果任务已经存在于持久存储中，那么它将被更新。在保存新任务之后，传递给这个方法的任务实例将
     * 使用新创建任务的id进行更新。
     *
     * @param taskDo 任务
     */
    void saveTask(TaskDo taskDo);

    /**
     * 加载用户任务节点配置
     *
     * @param taskDo 任务领域对象
     */
    void loadUserTaskWrapper(TaskDo taskDo);

    /**
     * 加载用户任务节点配置
     *
     * @param taskDo 任务领域对象
     * @param bpmnModel 流程模型
     */
    void loadUserTaskWrapper(TaskDo taskDo, BpmnModel bpmnModel);

    /**
     * 加载流程变量
     *
     * @param taskDo 任务领域对象
     */
    void loadTaskVariables(TaskDo taskDo);

	/**
     * 加载任务的本地变量
     *
     * @param taskDo 任务对象
     */
    void loadTaskVariablesLocal(TaskDo taskDo);

    /**
     * 加载表单数据
     *
     * @param taskDo 任务领域对象
     */
    void loadFromData(TaskDo taskDo);

    /**
     * 加载节点类型
     *
     * @param taskDo 任务领域对象
     */
    void loadActivityType(TaskDo taskDo);

    /**
     * 加载节点类型
     *
     * @param taskDo 任务领域对象
     * @param bpmnModel 流程模型
     */
    void loadActivityType(TaskDo taskDo, BpmnModel bpmnModel);

    /**
     * 批量终止任务
     *
     * @param taskDos 任务领域对象
     * @param comment 评论
     * @param autoOperationTypeEnum
     */
    void terminateTasks(List<TaskDo> taskDos, String comment, AutoOperationTypeEnum autoOperationTypeEnum);

    /**
     * 设置审批人
     *
     * @param taskDo
     */
    void setAssignee(TaskDo taskDo);

    /**
     * 批量撤回任务
     *
     * @param taskDos 任务领域对象
     * @param comment 评论
     */
    void recallTasks(List<TaskDo> taskDos, String comment);

    /**
     * 收藏
     *
     * @param taskDo 任务领域对象
     */
    void pinTask(TaskDo taskDo);

    /**
     * 获取任务详情链接
     *
     * @param taskDo 任务领域对象
     * @return 任务详情链接
     */
    TaskLink getTaskDetailLink(TaskDo taskDo);

    /**
     * 接收任务
     *
     * @param taskDo 任务领域对象
     */
    void receiveTask(TaskDo taskDo);

    /**
     * 认领任务
     *
     * @param taskDo 任务
     */
    void claimTask(TaskDo taskDo);

    /**
     * 加载任务候选人
     *
     * @param taskDo 任务领域对象
     */
    void loadTaskCandidates(TaskDo taskDo);

    /**
     * 加载预测任务
     *
     * @param taskDo 领域对象
     * @param historyTasks 历史任务
     */
    void loadPredictTasks(TaskDo taskDo, List<TaskDo> historyTasks);

    /**
     * 加载预测任务
     *
     * @param taskDo 领域对象
     * @param historyTaskKeys 历史任务
     */
    List<TaskDo> loadPredictTasksWithKeys(TaskDo taskDo, List<String> historyTaskKeys);

    List<TaskDo> syncLoadPredictTasksWithKeys(TaskDo taskDo, List<String> historyTaskKeys);

    /**
     * 计算任务详情操作按钮
     *
     * @param taskDo 领域对象
     * @param processInstanceDo 流程实例
     * @return 按钮集合
     */
    List<UserTaskOperation> calculateTaskOperations(TaskDo taskDo, ProcessInstanceDo processInstanceDo);

    void loadUserSignature(TaskDo taskDo);
}
