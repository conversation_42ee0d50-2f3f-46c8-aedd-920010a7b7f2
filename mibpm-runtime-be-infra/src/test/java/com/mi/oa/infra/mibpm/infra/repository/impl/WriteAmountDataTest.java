package com.mi.oa.infra.mibpm.infra.repository.impl;

import com.mi.oa.infra.mibpm.common.model.FormFlatDataDto;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
/**
 * <AUTHOR>
 * @date 2025/5/15
 */
@ExtendWith(MockitoExtension.class)
public class WriteAmountDataTest {
    @Test
    public void testWriteAmountData_ValidJson() throws Exception {
        // 准备测试数据
        FormFlatDataDto.FieldData fieldData = new FormFlatDataDto.FieldData();
        fieldData.setLabel("金额");
        fieldData.setName("amount");
        fieldData.setData("{\"amount\":\"100.50\",\"unit\":\"CNY\"}");

        Map.Entry<String, FormFlatDataDto.FieldData> entry = new AbstractMap.SimpleEntry<>("amount", fieldData);
        List<String> row = new ArrayList<>(Arrays.asList(null, null, null));
        List<String> mainHeader = Arrays.asList("其他字段", "金额amount", "金额币种amount");

        // 使用反射调用私有静态方法
        Method method = ProcessInstanceExportRepositoryImpl.class.getDeclaredMethod(
                "writeAmountData",
                Map.Entry.class,
                List.class,
                List.class
        );
        method.setAccessible(true);
        method.invoke(null, entry, row, mainHeader);

        // 验证结果
        assertEquals(3, row.size());
        assertEquals(null, row.get(0));
        assertEquals("100.50", row.get(1));
        assertEquals("CNY", row.get(2));
    }

    @Test
    public void testWriteAmountData_ScientificNotation() throws Exception {
        // 准备测试数据 - 使用科学计数法表示的大数
        FormFlatDataDto.FieldData fieldData = new FormFlatDataDto.FieldData();
        fieldData.setLabel("金额");
        fieldData.setName("amount");
        fieldData.setData("{\"amount\":\"1.2345E7\",\"unit\":\"USD\"}");

        Map.Entry<String, FormFlatDataDto.FieldData> entry = new AbstractMap.SimpleEntry<>("amount", fieldData);
        List<String> row = new ArrayList<>(Arrays.asList(null, null, null));
        List<String> mainHeader = Arrays.asList("其他字段", "金额amount", "金额币种amount");

        // 使用反射调用私有静态方法
        Method method = ProcessInstanceExportRepositoryImpl.class.getDeclaredMethod(
                "writeAmountData",
                Map.Entry.class,
                List.class,
                List.class
        );
        method.setAccessible(true);
        method.invoke(null, entry, row, mainHeader);

        // 验证结果 - 应该转换为普通数字格式
        assertEquals(3, row.size());
        assertEquals(null, row.get(0));
        assertEquals("12345000", row.get(1));
        assertEquals("USD", row.get(2));
    }

    @Test
    public void testWriteAmountData_InvalidJson() throws Exception {
        // 准备测试数据 - 无效的JSON
        FormFlatDataDto.FieldData fieldData = new FormFlatDataDto.FieldData();
        fieldData.setLabel("金额");
        fieldData.setName("amount");
        fieldData.setData("这不是有效的JSON");

        Map.Entry<String, FormFlatDataDto.FieldData> entry = new AbstractMap.SimpleEntry<>("amount", fieldData);
        List<String> row = new ArrayList<>(Arrays.asList("原始值1", "原始值2", "原始值3"));
        List<String> mainHeader = Arrays.asList("其他字段", "金额amount", "金额币种amount");

        // 使用反射调用私有静态方法
        Method method = ProcessInstanceExportRepositoryImpl.class.getDeclaredMethod(
                "writeAmountData",
                Map.Entry.class,
                List.class,
                List.class
        );
        method.setAccessible(true);
        method.invoke(null, entry, row, mainHeader);

        // 验证结果 - 行不应该被修改
        assertEquals(3, row.size());
        assertEquals("原始值1", row.get(0));
        assertEquals("原始值2", row.get(1));
        assertEquals("原始值3", row.get(2));
    }

    @Test
    public void testWriteAmountData_EmptyAmount() throws Exception {
        // 准备测试数据 - 空金额
        FormFlatDataDto.FieldData fieldData = new FormFlatDataDto.FieldData();
        fieldData.setLabel("金额");
        fieldData.setName("amount");
        fieldData.setData("{\"amount\":\"\",\"unit\":\"EUR\"}");

        Map.Entry<String, FormFlatDataDto.FieldData> entry = new AbstractMap.SimpleEntry<>("amount", fieldData);
        List<String> row = new ArrayList<>(Arrays.asList(null, null, null));
        List<String> mainHeader = Arrays.asList("其他字段", "金额amount", "金额币种amount");

        // 使用反射调用私有静态方法
        Method method = ProcessInstanceExportRepositoryImpl.class.getDeclaredMethod(
                "writeAmountData",
                Map.Entry.class,
                List.class,
                List.class
        );
        method.setAccessible(true);
        method.invoke(null, entry, row, mainHeader);

        // 验证结果
        assertEquals(3, row.size());
        assertEquals(null, row.get(0));
        assertEquals("", row.get(1));
        assertEquals("EUR", row.get(2));
    }

    @Test
    public void testWriteAmountData_NullAmount() throws Exception {
        // 准备测试数据 - null金额
        FormFlatDataDto.FieldData fieldData = new FormFlatDataDto.FieldData();
        fieldData.setLabel("金额");
        fieldData.setName("amount");
        fieldData.setData("{\"unit\":\"JPY\"}");

        Map.Entry<String, FormFlatDataDto.FieldData> entry = new AbstractMap.SimpleEntry<>("amount", fieldData);
        List<String> row = new ArrayList<>(Arrays.asList(null, null, null));
        List<String> mainHeader = Arrays.asList("其他字段", "金额amount", "金额币种amount");

        // 使用反射调用私有静态方法
        Method method = ProcessInstanceExportRepositoryImpl.class.getDeclaredMethod(
                "writeAmountData",
                Map.Entry.class,
                List.class,
                List.class
        );
        method.setAccessible(true);
        method.invoke(null, entry, row, mainHeader);

        // 验证结果
        assertEquals(3, row.size());
        assertEquals(null, row.get(0));
        assertTrue(row.get(1) == null || row.get(1).isEmpty(), "金额应为null或空字符串");
        assertEquals("JPY", row.get(2));
    }

    @Test
    public void testWriteAmountData_RowSizeExpansion() throws Exception {
        // 准备测试数据 - 行大小需要扩展
        FormFlatDataDto.FieldData fieldData = new FormFlatDataDto.FieldData();
        fieldData.setLabel("金额");
        fieldData.setName("amount");
        fieldData.setData("{\"amount\":\"999.99\",\"unit\":\"GBP\"}");

        Map.Entry<String, FormFlatDataDto.FieldData> entry = new AbstractMap.SimpleEntry<>("amount", fieldData);
        List<String> row = new ArrayList<>(); // 空行
        List<String> mainHeader = Arrays.asList("其他字段", "金额amount", "金额币种amount");

        // 使用反射调用私有静态方法
        Method method = ProcessInstanceExportRepositoryImpl.class.getDeclaredMethod(
                "writeAmountData",
                Map.Entry.class,
                List.class,
                List.class
        );
        method.setAccessible(true);
        method.invoke(null, entry, row, mainHeader);

        // 验证结果 - 行应该扩展到与表头相同的大小
        assertEquals(3, row.size());
        assertEquals(null, row.get(0));
        assertEquals("999.99", row.get(1));
        assertEquals("GBP", row.get(2));
    }
}
