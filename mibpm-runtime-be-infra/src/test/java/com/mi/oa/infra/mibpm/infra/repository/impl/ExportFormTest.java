package com.mi.oa.infra.mibpm.infra.repository.impl;

import com.mi.oa.infra.mibpm.common.enums.ExportStatusEnum;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.FormFlatDataDto;
import com.mi.oa.infra.mibpm.common.model.ProcessInstanceQueryDto;
import com.mi.oa.infra.mibpm.domain.procinst.converter.ProcessInstanceExportConverter;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceExportHistoryDo;
import com.mi.oa.infra.mibpm.infra.procinst.repository.HistoricProcInstRepository;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.FormRemoteService;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.converter.ExportConverter;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.converter.TaskInstPoConverter;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.DataExportHistoryMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.DataExportHistoryPo;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.fds.utils.FDSUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.when;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest({FDSUtils.class})
public class ExportFormTest {

    @InjectMocks
    private ProcessInstanceExportRepositoryImpl exportService; // 替换为你实际的类名
    @Mock
    private HistoricProcInstRepository historicProcInstRepository;

    @Mock
    private AccountRemoteService accountRemoteService;

    @Mock
    private TaskInstPoConverter taskInstPoConverter;

    @Mock
    private ProcessInstanceExportConverter converter;
    @Mock
    private MiTaskRepositoryImpl miTaskRepository;
    @Mock
    private ExportConverter exportConverter;
    @Mock
    private DataExportHistoryMapper dataExportHistoryMapper;
    @Mock
    private FormRemoteService formRemoteService;


    @Before
    public void setUp() {
        // 这里可以添加一些初始化代码，例如设置mock的行为等。
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(FDSUtils.class);
        // 假设有 newFilePath 字段，使用反射或set方法注入一个临时目录路径
        String tempDir = System.getProperty("java.io.tmpdir") + File.separator + "export_test" + File.separator;
        new File(tempDir).mkdirs();  // 创建目录，确保存在
        exportService.setExportDir(tempDir); ;
        // 打印所有 Mock 对象
        System.out.println("historicProcInstRepository: " + historicProcInstRepository);
        System.out.println("accountRemoteService: " + accountRemoteService);
        System.out.println("taskInstPoConverter: " + taskInstPoConverter);
        System.out.println("converter: " + converter);
        System.out.println("miTaskRepository: " + miTaskRepository);
        // 验证 exportService 是否注入了 Mock 对象
        System.out.println("exportservice: " + exportService);


    }
    @Test
    public void testExportFormSuccess() throws NoSuchMethodException, InvocationTargetException,
                                               IllegalAccessException {
        Method privateExportFormData = ProcessInstanceExportRepositoryImpl.class
                .getDeclaredMethod("exportFormData", String.class, ProcessInstanceQueryDto.class, ProcessInstanceExportHistoryDo.class); // 替换方法名和参数
        privateExportFormData.setAccessible(true);
        //准备实验数据
        String fileName = "test.xlsx";
        ProcessInstanceQueryDto query = new ProcessInstanceQueryDto();
        List<String> modelCodes = new ArrayList<>();
        modelCodes.add("123");
        modelCodes.add("456");
        query.setInModelCodes(modelCodes);
        query.setBusinessKey("1234567890");
        ProcessInstanceExportHistoryDo result = new ProcessInstanceExportHistoryDo();

        PageModel<ProcessInstanceDo> firstPage = new PageModel<>();
        List<ProcessInstanceDo> list = new ArrayList<>();

        ProcessInstanceDo processInstance1 = new ProcessInstanceDo();
        processInstance1.setBusinessKey("1234567890");
        processInstance1.setProcessInstanceId("1234567890");
        processInstance1.setProcessInstanceName("testProcessInstanceName1");
        processInstance1.setStartTime(ZonedDateTime.now());
        processInstance1.setEndTime(ZonedDateTime.now());
        BpmUser user1 = new BpmUser();
        user1.setUserName("testUserName1");
        user1.setDisplayName("testDisplayName1 descr");
        processInstance1.setStartUser(user1);


        ProcessInstanceDo processInstance2 = new ProcessInstanceDo();
        processInstance2.setBusinessKey("12345678901234567890");
        processInstance2.setProcessInstanceId("12345678901234567890");
        processInstance2.setProcessInstanceName("testProcessInstanceName2");
        processInstance2.setStartTime(ZonedDateTime.now());
        processInstance2.setEndTime(ZonedDateTime.now());
        BpmUser user2 = new BpmUser();
        user2.setUserName("testUserName2");
        user2.setDisplayName("testDisplayName1 descr2");
        processInstance2.setStartUser(user2);


        list.add(processInstance1);
        list.add(processInstance2);
        firstPage.setTotal(2);
        firstPage.setPageNum(1);
        firstPage.setPageSize(10);
        firstPage.setList(list);
        firstPage.setPageTotal(1);

        when(historicProcInstRepository.queryHistoricProcInstPage(
                any(ProcessInstanceQueryDto.class), // 匹配任意 ProcessInstanceQueryDto 对象
                anyLong(), // 匹配任意 long 类型参数
                anyLong() // 匹配任意 long 类型参数
        )).thenReturn(firstPage); // 返回空列表的 PageModel/ 匹配任意 long 类型参数).thenReturn(new ArrayList<>());

        FormFlatDataDto formFlatDataDto = new FormFlatDataDto();

        Map<String, FormFlatDataDto.FieldData> mainFieldData = new HashMap<>();

        FormFlatDataDto.FieldData fieldData1 = new FormFlatDataDto.FieldData();
        fieldData1.setName("field1");
        fieldData1.setLabel("field1 descr");
        fieldData1.setType("text");
        mainFieldData.put("field1", fieldData1);


        FormFlatDataDto.FieldData fieldData2 = new FormFlatDataDto.FieldData();
        fieldData2.setName("field2");
        fieldData2.setLabel("money");
        fieldData2.setType("money");
        String json = "{\"amount\": 123, \"unit\": \"usa\"}";
        fieldData2.setData(json);
        mainFieldData.put("field2", fieldData2);


        formFlatDataDto.setMainFieldData(mainFieldData);


        Map<String, FormFlatDataDto.TableData> tableFieldData = new HashMap<>();
        FormFlatDataDto.TableData tableData = new FormFlatDataDto.TableData();
        tableData.setName("table1");
        tableData.setLabel("table1 descr");

        List<Map<String, FormFlatDataDto.FieldData>> tableRowData = new ArrayList<>();
        Map<String, FormFlatDataDto.FieldData> childFileData = new HashMap<>();
        childFileData.put("field1", fieldData1);
        tableRowData.add(childFileData);

        tableData.setTableRowData(tableRowData);
        tableFieldData.put("table1", tableData);
        formFlatDataDto.setTableFieldData(tableFieldData);

        // 模拟 FDSUtils
        PowerMockito.when(FDSUtils.putFile(any(File.class), eq(fileName), anyString()))
                .thenReturn("http://fakeurl.com/" + fileName);



        when(formRemoteService.getFormFlatData(any(String.class),any())).thenReturn(formFlatDataDto);


        //when(historicProcInstRepository.queryHistoricProcInstPage())
        result.setExportStatus(ExportStatusEnum.SUCCESS.getCode());
        result.setCompleteTime(ZonedDateTime.now());
        result.setUrl("http://fakeurl.com/test.xlsx");
        result.setType(1);

        DataExportHistoryPo dataExportHistoryPo = new DataExportHistoryPo();
        dataExportHistoryPo.setModelCode("testModel");
        when(exportConverter.doToPo(result)).thenReturn(dataExportHistoryPo);
        when(dataExportHistoryMapper.insert(any())).thenReturn(1);

        privateExportFormData.invoke(exportService, fileName, query,
                result); // 替换参数值

    }





}
