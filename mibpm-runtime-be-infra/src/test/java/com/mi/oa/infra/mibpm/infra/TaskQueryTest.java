package com.mi.oa.infra.mibpm.infra;

import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.infra.task.repository.HistoricTaskRepository;
import com.mi.oa.infra.mibpm.infra.task.repository.TaskRepository;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.runtime.Execution;
import org.flowable.task.api.Task;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/23 5:32 PM
 **/
@SpringBootTest(classes = {InfraTestApplication.class})
@RunWith(SpringRunner.class)
public class TaskQueryTest {

    @Autowired
    private HistoricTaskRepository historicTaskRepository;
    @Autowired
    private TaskRepository taskRepository;
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private TaskService taskService;

    @Test
    public void testHistoricTaskQueryByProcessInstanceId() {
        List<TaskDo> taskDos = historicTaskRepository.queryHistoricTasksByProcInstId("34442503");
        assertThat(taskDos);
    }

    @Test
    public void testReturnActivities() {
        //List<UserTaskActivity> taskActivities = taskRepository.queryReturnActivities("41080207");
        //System.out.println(taskActivities);
    }

    @Test
    public void testExecution(){
        Task currentTask = taskService.createTaskQuery().taskId("41449971").singleResult();
        Execution currentExecution = runtimeService.createExecutionQuery().executionId(currentTask.getExecutionId()).singleResult();
        Execution parentExecution = runtimeService.createExecutionQuery().executionId(currentExecution.getParentId()).singleResult();
        long executionCount = runtimeService.createExecutionQuery().parentId(parentExecution.getId()).count();

    }
}
