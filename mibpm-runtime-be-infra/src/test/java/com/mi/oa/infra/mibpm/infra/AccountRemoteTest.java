package com.mi.oa.infra.mibpm.infra;

import com.mi.flowable.idm.api.MiUser;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import org.assertj.core.api.ListAssert;
import org.assertj.core.util.Lists;
import org.flowable.idm.api.User;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/15 4:51 PM
 **/
@SpringBootTest(classes = {InfraTestApplication.class})
@RunWith(SpringRunner.class)
public class AccountRemoteTest {
    @Autowired
    private AccountRemoteService accountRemoteService;

    @Test
    public void testReportLineRemote() {
        List<BpmUser> users = accountRemoteService.listUserReportLine("dingxiaoyan");
        ListAssert<BpmUser> userListAssert = assertThat(users);
        System.out.println(users);
    }

    @Test
    public void testGetByName() {
        BpmUser user = accountRemoteService.getUser("asdf");
        BpmUser user1 = accountRemoteService.getUser("7a9f963f239c4d508059ca86b2a58725");
        assertThat(user);
        System.out.println(user);
    }

    @Test
    public void testListByName() {
        List<BpmUser> user = accountRemoteService.listUsers(
                Lists.newArrayList("dingxiaoyan", "qiuzhipeng", "wangchen21"));
        assertThat(user);
        System.out.println(user);
    }
}
