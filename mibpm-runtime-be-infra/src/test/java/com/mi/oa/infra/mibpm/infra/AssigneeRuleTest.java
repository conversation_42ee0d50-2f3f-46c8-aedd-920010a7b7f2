package com.mi.oa.infra.mibpm.infra;

import com.google.common.collect.Lists;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.flowable.extension.model.DeptConfigRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.DeptConfigTypeEnum;
import com.mi.oa.infra.mibpm.flowable.extension.model.DeptManagerAssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.DeptTypeEnum;
import com.mi.oa.infra.mibpm.flowable.extension.model.LeaderAssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.LeaderConfigRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.LeaderTypeEnum;
import com.mi.oa.infra.mibpm.flowable.extension.model.MultiDeptManagerAssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.MultiLeaderAssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.PositionAssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.RelativeLevelEnum;
import com.mi.oa.infra.mibpm.flowable.extension.model.RoleAssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.SpecifiedLeaderTypeEnum;
import com.mi.oa.infra.mibpm.infra.flowable.assign.impl.DeptManagerAssigneeServiceImpl;
import com.mi.oa.infra.mibpm.infra.flowable.assign.impl.LeaderAssigneeServiceImpl;
import com.mi.oa.infra.mibpm.infra.flowable.assign.impl.MultiDeptManagerAssigneeServiceImpl;
import com.mi.oa.infra.mibpm.infra.flowable.assign.impl.MultiLeaderAssigneeServiceImpl;
import com.mi.oa.infra.mibpm.infra.flowable.assign.impl.PositionAssigneeServiceImpl;
import com.mi.oa.infra.mibpm.infra.flowable.assign.impl.RoleAssigneeServiceImpl;
import com.mi.oa.infra.mibpm.infra.remote.sdk.impl.NewAccountRemoteServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/15 15:01
 **/
@SpringBootTest(classes = {InfraTestApplication.class})
@RunWith(SpringRunner.class)
public class AssigneeRuleTest {

    @Autowired
    private DeptManagerAssigneeServiceImpl deptManagerAssigneeService;
    @Autowired
    private MultiDeptManagerAssigneeServiceImpl multiDeptManagerAssigneeService;
    @Autowired
    private LeaderAssigneeServiceImpl leaderAssigneeService;
    @Autowired
    private MultiLeaderAssigneeServiceImpl multiLeaderAssigneeService;
    @Autowired
    private NewAccountRemoteServiceImpl accountRemoteService;
    @Autowired
    private RoleAssigneeServiceImpl roleAssigneeServiceImpl;
    @Autowired
    private PositionAssigneeServiceImpl positionAssigneeService;

    @Test
    public void testUserReportLine() {
        List<BpmUser> bpmUsers = accountRemoteService.listUserReportLine("wujie17,haoleilei,yuyong");
        System.out.println(bpmUsers);
    }

    /**
     * 直属上级+1
     */
    @Test
    public void deptDirectPlus1() {
        DeptManagerAssigneeRule deptManagerAssigneeRule = new DeptManagerAssigneeRule();
        deptManagerAssigneeRule.setPassvalue("IT550203");
        deptManagerAssigneeRule.setDeptType(DeptConfigTypeEnum.DEPT.getCode());
        DeptConfigRule deptConfigRule = new DeptConfigRule();
        deptConfigRule.setDeptType(DeptTypeEnum.DIRECT_DEPT.getCode());
        deptConfigRule.setLevel(RelativeLevelEnum.PLUS_1.getCode());
        deptManagerAssigneeRule.setDeptConfig(deptConfigRule);
        Map<String, Object> variables = new HashMap<>(8);
        List<String> strings = deptManagerAssigneeService.buildAssignee(deptManagerAssigneeRule, null, variables);
        List<String> result = listUsers(strings);
        System.out.println(result);
        assertTrue(result.contains("qicen") && result.size() == 1);
    }

    /**
     * 部门参数
     */
    @Test
    public void deptParam2() {
        DeptManagerAssigneeRule deptManagerAssigneeRule = new DeptManagerAssigneeRule();
        deptManagerAssigneeRule.setPassvalue("bumen");
        deptManagerAssigneeRule.setDeptType(DeptConfigTypeEnum.DEPT_PARAM.getCode());
        DeptConfigRule deptConfigRule = new DeptConfigRule();
        deptConfigRule.setDeptType(DeptTypeEnum.DIRECT_DEPT.getCode());
        deptConfigRule.setLevel(RelativeLevelEnum.PLUS_1.getCode());
        deptManagerAssigneeRule.setDeptConfig(deptConfigRule);
        Map<String, Object> variables = new HashMap<>(8);
        variables.put("bumen", Lists.newArrayList("IT550203", "IT560402"));
        List<String> strings = deptManagerAssigneeService.buildAssignee(deptManagerAssigneeRule, null, variables);
        List<String> result = listUsers(strings);
        System.out.println(result);
        assertTrue(CollectionUtils.isEqualCollection(result, Lists.newArrayList("qicen", "wangchen20")));
    }

    /**
     * 5级部门负责人+1级
     */
    @Test
    public void userDept5plus1() {
        DeptManagerAssigneeRule deptManagerAssigneeRule = new DeptManagerAssigneeRule();
        deptManagerAssigneeRule.setPassvalue("initiator");
        deptManagerAssigneeRule.setDeptType(DeptConfigTypeEnum.USER_DEPT.getCode());
        DeptConfigRule deptConfigRule = new DeptConfigRule();
        deptConfigRule.setDeptType(DeptTypeEnum.LEVEL5_DEPT.getCode());
        deptConfigRule.setLevel(RelativeLevelEnum.PLUS_1.getCode());
        deptManagerAssigneeRule.setDeptConfig(deptConfigRule);
        Map<String, Object> variables = new HashMap<>(8);
        variables.put("initiator", "dingxiaoyan");
        List<String> strings = deptManagerAssigneeService.buildAssignee(deptManagerAssigneeRule, null, variables);
        List<String> result = listUsers(strings);
        System.out.println(listUsers(strings));
        assertTrue(result.contains("shenxing") && result.size() == 1);
    }

    /**
     * 五级部门负责人
     */
    @Test
    public void userDept5plus0() {
        DeptManagerAssigneeRule deptManagerAssigneeRule = new DeptManagerAssigneeRule();
        deptManagerAssigneeRule.setPassvalue("initiator");
        deptManagerAssigneeRule.setDeptType(DeptConfigTypeEnum.USER_DEPT.getCode());
        DeptConfigRule deptConfigRule = new DeptConfigRule();
        deptConfigRule.setDeptType(DeptTypeEnum.LEVEL5_DEPT.getCode());
        deptConfigRule.setLevel(RelativeLevelEnum.PLUS_0.getCode());
        deptManagerAssigneeRule.setDeptConfig(deptConfigRule);
        Map<String, Object> variables = new HashMap<>(8);
        variables.put("initiator", "dingxiaoyan");
        List<String> strings = deptManagerAssigneeService.buildAssignee(deptManagerAssigneeRule, null, variables);
        List<String> result = listUsers(strings);
        System.out.println(listUsers(strings));
        assertTrue(result.isEmpty());
    }

    /**
     * 五级部门到一级部门
     */
    @Test
    public void multiDept5to1() {
        MultiDeptManagerAssigneeRule deptManagerAssigneeRule = new MultiDeptManagerAssigneeRule();
        deptManagerAssigneeRule.setPassvalue("initiator");
        DeptConfigRule startConfig = new DeptConfigRule();
        startConfig.setDeptType(DeptTypeEnum.LEVEL5_DEPT.getCode());
        startConfig.setLevel(RelativeLevelEnum.PLUS_0.getCode());
        deptManagerAssigneeRule.setStartConfig(startConfig);
        DeptConfigRule endConfig = new DeptConfigRule();
        endConfig.setDeptType(DeptTypeEnum.LEVEL1_DEPT.getCode());
        endConfig.setLevel(RelativeLevelEnum.PLUS_0.getCode());
        deptManagerAssigneeRule.setEndConfig(endConfig);
        Map<String, Object> variables = new HashMap<>(8);
        variables.put("initiator", "dingxiaoyan");
        List<String> strings = multiDeptManagerAssigneeService.buildAssignee(deptManagerAssigneeRule, null, variables);
        List<String> users = listUsers(strings);
        System.out.println(users);
        assertTrue(CollectionUtils.isEqualCollection(users, Lists.newArrayList("shenxing", "shenxing", "tangxuexu",
                "fanyu")));
    }

    /**
     * 直属部门到一级部门
     */
    @Test
    public void multiDeptDirectTo1() {
        MultiDeptManagerAssigneeRule deptManagerAssigneeRule = new MultiDeptManagerAssigneeRule();
        deptManagerAssigneeRule.setPassvalue("initiator");
        DeptConfigRule startConfig = new DeptConfigRule();
        startConfig.setDeptType(DeptTypeEnum.DIRECT_DEPT.getCode());
        startConfig.setLevel(RelativeLevelEnum.PLUS_0.getCode());
        Map<String, Object> variables = new HashMap<>(8);
        variables.put("initiator", "baopan1");
        deptManagerAssigneeRule.setStartConfig(startConfig);
        DeptConfigRule endConfig = new DeptConfigRule();
        endConfig.setDeptType(DeptTypeEnum.LEVEL1_DEPT.getCode());
        deptManagerAssigneeRule.setEndConfig(endConfig);
        List<String> strings = multiDeptManagerAssigneeService.buildAssignee(deptManagerAssigneeRule, null, variables);
        List<String> result = listUsers(strings);
        System.out.println(result);
        assertTrue(CollectionUtils.isEqualCollection(result, Lists.newArrayList("chaizhi", "wangchen20", "suzeyao",
                "suzeyao", "fanyu")));
    }

    /**
     * 多个人员参数-直属部门到一级部门
     */
    @Test
    public void multiDeptDirectTo1MultiUser() {
        MultiDeptManagerAssigneeRule deptManagerAssigneeRule = new MultiDeptManagerAssigneeRule();
        deptManagerAssigneeRule.setPassvalue("user123");
        DeptConfigRule startConfig = new DeptConfigRule();
        startConfig.setDeptType(DeptTypeEnum.DIRECT_DEPT.getCode());
        startConfig.setLevel(RelativeLevelEnum.PLUS_0.getCode());
        Map<String, Object> variables = new HashMap<>(8);
        variables.put("user123", Lists.newArrayList("baopan1", "dingxiaoyan", "wangzepeng5"));
        deptManagerAssigneeRule.setStartConfig(startConfig);
        DeptConfigRule endConfig = new DeptConfigRule();
        endConfig.setDeptType(DeptTypeEnum.LEVEL1_DEPT.getCode());
        deptManagerAssigneeRule.setEndConfig(endConfig);
        List<String> strings = multiDeptManagerAssigneeService.buildAssignee(deptManagerAssigneeRule, null, variables);
        List<String> result = listUsers(strings);
        System.out.println(result);
    }

    /**
     * 直属领导+1级
     */
    @Test
    public void leaderDirectPlus1() {
        LeaderAssigneeRule assigneeRule = new LeaderAssigneeRule();
        assigneeRule.setPassvalue("initiator");
        LeaderConfigRule startConfig = new LeaderConfigRule();
        startConfig.setLeaderType(LeaderTypeEnum.DIRECT.getCode());
        startConfig.setLevel(RelativeLevelEnum.PLUS_1.getCode());
        assigneeRule.setLeaderConfig(startConfig);
        Map<String, Object> variables = new HashMap<>(8);
        variables.put("initiator", "dingxiaoyan");
        List<String> strings = leaderAssigneeService.buildAssignee(assigneeRule, null, variables);
        List<String> users = listUsers(strings);
        System.out.println(users);
        assertTrue(users.contains("tangxuexu") && users.size() == 1);
    }

    /**
     * 最高级领导-1
     */
    @Test
    public void leaderHighestMinus1() {
        LeaderAssigneeRule assigneeRule = new LeaderAssigneeRule();
        assigneeRule.setPassvalue("initiator");
        LeaderConfigRule startConfig = new LeaderConfigRule();
        startConfig.setLeaderType(LeaderTypeEnum.HIGHEST.getCode());
        startConfig.setLevel(RelativeLevelEnum.MINUS_1.getCode());
        assigneeRule.setLeaderConfig(startConfig);
        Map<String, Object> variables = new HashMap<>(8);
        variables.put("initiator", "dingxiaoyan");
        List<String> strings = leaderAssigneeService.buildAssignee(assigneeRule, null, variables);
        List<String> result = listUsers(strings);
        System.out.println(result);
        assertTrue(result.contains("fanyu") && result.size() == 1);
    }

    /**
     * 逗号分隔多个人员参数
     */
    @Test
    public void leaderParam2UsersDirectComma() {
        LeaderAssigneeRule assigneeRule = getLeaderAssigneeRuleDirect();
        Map<String, Object> variables = new HashMap<>(8);
        variables.put("user111", "dingxiaoyan,baopan1");
        List<String> strings = leaderAssigneeService.buildAssignee(assigneeRule, null, variables);
        List<String> users = listUsers(strings);
        System.out.println(users);
        assertTrue(CollectionUtils.isEqualCollection(users, Lists.newArrayList("shenxing", "chaizhi")));
    }

    @Test
    public void leaderParam2UsersDirectArray() {
        LeaderAssigneeRule assigneeRule = getLeaderAssigneeRuleDirect();
        Map<String, Object> variables = new HashMap<>(8);
        variables.put("user111", Lists.newArrayList("dingxiaoyan", "baopan1"));
        List<String> strings = leaderAssigneeService.buildAssignee(assigneeRule, null, variables);
        List<String> users = listUsers(strings);
        System.out.println(users);
        assertTrue(CollectionUtils.isEqualCollection(users, Lists.newArrayList("shenxing", "chaizhi")));
    }

    @NotNull
    private static LeaderAssigneeRule getLeaderAssigneeRuleDirect() {
        LeaderAssigneeRule assigneeRule = new LeaderAssigneeRule();
        assigneeRule.setPassvalue("user111");
        LeaderConfigRule startConfig = new LeaderConfigRule();
        startConfig.setLeaderType(LeaderTypeEnum.DIRECT.getCode());
        startConfig.setLevel(RelativeLevelEnum.PLUS_0.getCode());
        assigneeRule.setLeaderConfig(startConfig);
        return assigneeRule;
    }

    /**
     * 直属领导到分管领导
     */
    @Test
    public void leaderDirectToCharge() {
        MultiLeaderAssigneeRule assigneeRule = new MultiLeaderAssigneeRule();
        assigneeRule.setPassvalue("initiator");
        LeaderConfigRule startConfig = new LeaderConfigRule();
        startConfig.setLeaderType(LeaderTypeEnum.DIRECT.getCode());
        startConfig.setLevel(RelativeLevelEnum.PLUS_0.getCode());
        assigneeRule.setStartConfig(startConfig);
        LeaderConfigRule endConfig = new LeaderConfigRule();
        endConfig.setLeaderType(LeaderTypeEnum.SPECIFIED.getCode());
        endConfig.setSpecifiedLeaderType(SpecifiedLeaderTypeEnum.LEADER_IN_CHARGE.getCode());
        endConfig.setLevel(RelativeLevelEnum.PLUS_0.getCode());
        assigneeRule.setEndConfig(endConfig);
        Map<String, Object> variables = new HashMap<>(8);
        variables.put("initiator", "dingxiaoyan");
        List<String> strings = multiLeaderAssigneeService.buildAssignee(assigneeRule, null, variables);
        List<String> users = listUsers(strings);
        System.out.println(users);
        assertTrue(CollectionUtils.isEqualCollection(users, Lists.newArrayList("shenxing", "tangxuexu", "fanyu",
                "quheng")));
    }

    /**
     * 直属领导到一级部门负责人-1
     * （直属领导是2级部门负责人）
     * 部门负责人（yuekai-fengxiaoshi）
     * 汇报线（sunying5-fengxiaoshi-jiangwen-yuekai-alain-leijun）
     */
    @Test
    public void leaderDirectToCharge1() {
        MultiLeaderAssigneeRule assigneeRule = new MultiLeaderAssigneeRule();
        assigneeRule.setPassvalue("initiator");
        LeaderConfigRule startConfig = new LeaderConfigRule();
        startConfig.setLeaderType(LeaderTypeEnum.DIRECT.getCode());
        startConfig.setLevel(RelativeLevelEnum.PLUS_0.getCode());
        assigneeRule.setStartConfig(startConfig);
        LeaderConfigRule endConfig = new LeaderConfigRule();
        endConfig.setLeaderType(LeaderTypeEnum.SPECIFIED.getCode());
        endConfig.setSpecifiedLeaderType(SpecifiedLeaderTypeEnum.LEVEL_ONE_DEPT_MANAGER.getCode());
        endConfig.setLevel(RelativeLevelEnum.PLUS_0.getCode());
        assigneeRule.setEndConfig(endConfig);
        Map<String, Object> variables = new HashMap<>(8);
        variables.put("initiator", "linen");
        List<String> strings = multiLeaderAssigneeService.buildAssignee(assigneeRule, null, variables);
        List<String> users = listUsers(strings);
        System.out.println(users);
        assertTrue(CollectionUtils.isEqualCollection(users, Lists.newArrayList()));
    }

    /**
     * 直属领导到一级部门负责人+1
     * （直属领导是2级部门负责人）
     * 部门负责人（wangjie17-wanglimei1）
     * 汇报线（xuyanan3-wanglimei1-wangjie17-alain-leijun）
     */
    @Test
    public void leaderDirectToCharge2() {
        MultiLeaderAssigneeRule assigneeRule = new MultiLeaderAssigneeRule();
        assigneeRule.setPassvalue("initiator");
        LeaderConfigRule startConfig = new LeaderConfigRule();
        startConfig.setLeaderType(LeaderTypeEnum.DIRECT.getCode());
        startConfig.setLevel(RelativeLevelEnum.PLUS_0.getCode());
        assigneeRule.setStartConfig(startConfig);
        LeaderConfigRule endConfig = new LeaderConfigRule();
        endConfig.setLeaderType(LeaderTypeEnum.SPECIFIED.getCode());
        endConfig.setSpecifiedLeaderType(SpecifiedLeaderTypeEnum.LEVEL_ONE_DEPT_MANAGER.getCode());
        endConfig.setLevel(RelativeLevelEnum.PLUS_1.getCode());
        assigneeRule.setEndConfig(endConfig);
        Map<String, Object> variables = new HashMap<>(8);
        variables.put("initiator", "xuyanan3");
        List<String> strings = multiLeaderAssigneeService.buildAssignee(assigneeRule, null, variables);
        List<String> users = listUsers(strings);
        System.out.println(users);
        assertTrue(CollectionUtils.isEqualCollection(users, Lists.newArrayList("wanglimei1", "wangjie17", "alain")));
    }

    /**
     * 指定二级部门负责人+1级
     */
    @Test
    public void leaderSpeLevel2Plus12() {
        LeaderAssigneeRule assigneeRule = new LeaderAssigneeRule();
        assigneeRule.setPassvalue("initiator");
        LeaderConfigRule startConfig = new LeaderConfigRule();
        startConfig.setLeaderType(LeaderTypeEnum.SPECIFIED.getCode());
        startConfig.setSpecifiedLeaderType(SpecifiedLeaderTypeEnum.LEVEL_TWO_DEPT_MANAGER.getCode());
        startConfig.setLevel(RelativeLevelEnum.PLUS_1.getCode());
        assigneeRule.setLeaderConfig(startConfig);
        Map<String, Object> variables = new HashMap<>(8);
        variables.put("initiator", "dingxiaoyan");
        List<String> strings = leaderAssigneeService.buildAssignee(assigneeRule, null, variables);
        List<String> users = listUsers(strings);
        System.out.println(users);
        assertTrue(users.contains("fanyu") && users.size() == 1);
    }

    /**
     * 四级部门负责人到一级部门负责人+1
     */
    @Test
    public void leaderSpeDirectToCharge3() {
        MultiLeaderAssigneeRule assigneeRule = new MultiLeaderAssigneeRule();
        assigneeRule.setPassvalue("initiator");
        LeaderConfigRule startConfig = new LeaderConfigRule();
        startConfig.setLeaderType(LeaderTypeEnum.SPECIFIED.getCode());
        startConfig.setSpecifiedLeaderType(SpecifiedLeaderTypeEnum.LEVEL_FOUR_DEPT_MANAGER.getCode());
        startConfig.setLevel(RelativeLevelEnum.PLUS_0.getCode());
        assigneeRule.setStartConfig(startConfig);
        LeaderConfigRule endConfig = new LeaderConfigRule();
        endConfig.setLeaderType(LeaderTypeEnum.SPECIFIED.getCode());
        endConfig.setSpecifiedLeaderType(SpecifiedLeaderTypeEnum.LEVEL_ONE_DEPT_MANAGER.getCode());
        endConfig.setLevel(RelativeLevelEnum.PLUS_1.getCode());
        assigneeRule.setEndConfig(endConfig);
        Map<String, Object> variables = new HashMap<>(8);
        variables.put("initiator", "dingxiaoyan");
        List<String> strings = multiLeaderAssigneeService.buildAssignee(assigneeRule, null, variables);
        List<String> users = listUsers(strings);
        System.out.println(users);
        assertTrue(CollectionUtils.isEqualCollection(users, Lists.newArrayList("shenxing", "tangxuexu", "fanyu",
                "quheng")));
    }

    /**
     * 指定二级部门负责人+1级
     */
    @Test
    public void leaderSpeLevel2Plus1() {
        LeaderAssigneeRule assigneeRule = new LeaderAssigneeRule();
        assigneeRule.setPassvalue("initiator");
        LeaderConfigRule startConfig = new LeaderConfigRule();
        startConfig.setLeaderType(LeaderTypeEnum.SPECIFIED.getCode());
        startConfig.setSpecifiedLeaderType(SpecifiedLeaderTypeEnum.LEVEL_TWO_DEPT_MANAGER.getCode());
        startConfig.setLevel(RelativeLevelEnum.PLUS_1.getCode());
        assigneeRule.setLeaderConfig(startConfig);
        Map<String, Object> variables = new HashMap<>(8);
        variables.put("initiator", "dingxiaoyan");
        List<String> strings = leaderAssigneeService.buildAssignee(assigneeRule, null, variables);
        List<String> users = listUsers(strings);
        System.out.println(users);
        assertTrue(users.contains("fanyu") && users.size() == 1);
    }

    /**
     * 四级部门负责人到一级部门负责人+1
     */
    @Test
    public void leaderSpeDirectToCharge2() {
        MultiLeaderAssigneeRule assigneeRule = new MultiLeaderAssigneeRule();
        assigneeRule.setPassvalue("initiator");
        LeaderConfigRule startConfig = new LeaderConfigRule();
        startConfig.setLeaderType(LeaderTypeEnum.SPECIFIED.getCode());
        startConfig.setSpecifiedLeaderType(SpecifiedLeaderTypeEnum.LEVEL_FOUR_DEPT_MANAGER.getCode());
        startConfig.setLevel(RelativeLevelEnum.PLUS_0.getCode());
        assigneeRule.setStartConfig(startConfig);
        LeaderConfigRule endConfig = new LeaderConfigRule();
        endConfig.setLeaderType(LeaderTypeEnum.SPECIFIED.getCode());
        endConfig.setSpecifiedLeaderType(SpecifiedLeaderTypeEnum.LEVEL_ONE_DEPT_MANAGER.getCode());
        endConfig.setLevel(RelativeLevelEnum.PLUS_1.getCode());
        assigneeRule.setEndConfig(endConfig);
        Map<String, Object> variables = new HashMap<>(8);
        variables.put("initiator", "dingxiaoyan");
        List<String> strings = multiLeaderAssigneeService.buildAssignee(assigneeRule, null, variables);
        List<String> users = listUsers(strings);
        System.out.println(users);
        assertTrue(CollectionUtils.isEqualCollection(users, Lists.newArrayList("shenxing", "tangxuexu", "fanyu",
                "quheng")));
    }

    /**
     * 直属领导到一级部门负责人+1
     * （直属领导是2级部门负责人）
     * 部门负责人（wangjie17-wanglimei1）
     * 汇报线（xuyanan3-wanglimei1-wangjie17-alain-leijun）
     */
    @Test
    public void leaderDirectToLevel1AU() {
        MultiLeaderAssigneeRule assigneeRule = new MultiLeaderAssigneeRule();
        assigneeRule.setPassvalue("initiator");
        LeaderConfigRule startConfig = new LeaderConfigRule();
        startConfig.setLeaderType(LeaderTypeEnum.DIRECT.getCode());
        startConfig.setLevel(RelativeLevelEnum.PLUS_0.getCode());
        assigneeRule.setStartConfig(startConfig);
        LeaderConfigRule endConfig = new LeaderConfigRule();
        endConfig.setLeaderType(LeaderTypeEnum.SPECIFIED.getCode());
        endConfig.setSpecifiedLeaderType(SpecifiedLeaderTypeEnum.LEVEL_ONE_DEPT_MANAGER.getCode());
        endConfig.setLevel(RelativeLevelEnum.PLUS_1.getCode());
        assigneeRule.setEndConfig(endConfig);
        Map<String, Object> variables = new HashMap<>(8);
        variables.put("initiator", "v-wuhaoran");
        List<String> strings = multiLeaderAssigneeService.buildAssignee(assigneeRule, null, variables);
        List<String> users = listUsers(strings);
        System.out.println(users);
        assertTrue(CollectionUtils.isEqualCollection(users, Lists.newArrayList("dengbihong", "gongchenjie",
                "liuzhengmei", "lixin68", "huzhengnan")));
    }

    /**
     * 五级部门到一级部门 汽车部
     */
    @Test
    public void multiDept5to1AU() {
        MultiDeptManagerAssigneeRule deptManagerAssigneeRule = new MultiDeptManagerAssigneeRule();
        deptManagerAssigneeRule.setPassvalue("initiator");
        DeptConfigRule startConfig = new DeptConfigRule();
        startConfig.setDeptType(DeptTypeEnum.LEVEL5_DEPT.getCode());
        startConfig.setLevel(RelativeLevelEnum.PLUS_0.getCode());
        deptManagerAssigneeRule.setStartConfig(startConfig);
        DeptConfigRule endConfig = new DeptConfigRule();
        endConfig.setDeptType(DeptTypeEnum.LEVEL1_DEPT.getCode());
        endConfig.setLevel(RelativeLevelEnum.PLUS_0.getCode());
        deptManagerAssigneeRule.setEndConfig(endConfig);
        Map<String, Object> variables = new HashMap<>(8);
        variables.put("initiator", "v-wuhaoran");
        List<String> strings = multiDeptManagerAssigneeService.buildAssignee(deptManagerAssigneeRule, null, variables);
        List<String> users = listUsers(strings);
        System.out.println(users);
        assertTrue(CollectionUtils.isEqualCollection(users, Lists.newArrayList("dengbihong", "gongchenjie", "liuzhengmei",
                "leijun")));
    }

    /**
     * 生成 positionAssigneeService 的测试用例
     *
     * @param
     * @return
     */
    @Test
    public void testPositionAssigneeService() {
        PositionAssigneeRule positionAssigneeRule = new PositionAssigneeRule();
        PositionAssigneeRule.Position position = new PositionAssigneeRule.Position();
        position.setJobCode("ODMResourcing");
        positionAssigneeRule.setJob(position);
        PositionAssigneeRule.OrgTree orgTree = new PositionAssigneeRule.OrgTree();
        orgTree.setOrgTreeCode("MI_PHONE");
        positionAssigneeRule.setOrgTree(orgTree);
        HashMap<String, String> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("MATERIAL", "input_959723a13211");

        positionAssigneeRule.setDimensionMap(objectObjectHashMap);
        Map<String, Object> variables = new HashMap<>(8);
        variables.put("input_959723a13211", "1230102000337A,123,123,4356,363");

        List<String> strings = positionAssigneeService.buildAssignee(positionAssigneeRule, "********", variables);
        List<String> users = listUsers(strings);
        System.out.println(users);
        assertTrue(users.contains("expectedUser") && users.size() == 1);
    }

    private List<String> listUsers(List<String> users) {
        List<BpmUser> bpmUsers = accountRemoteService.listUsers(users);
        return bpmUsers.stream().map(BpmUser::getUserName).collect(Collectors.toList());
    }

    @Test
    public void testBuildAssignee() {
        // Create a mock AssigneeRule instance
        RoleAssigneeRule roleAssigneeRule = new RoleAssigneeRule();

        RoleAssigneeRule.Role role = new RoleAssigneeRule.Role();
        role.setRoleCode("super_admin");
        role.setAppCode("4pN0ape8lvgd");
        role.setRoleName("super_admin");

        RoleAssigneeRule.Role roleProd = new RoleAssigneeRule.Role();
        roleProd.setRoleCode("super_admin");
        roleProd.setAppCode("wdkB2VVqNbne");
        roleProd.setRoleName("super_admin");

        roleAssigneeRule.setRole(role);
        roleAssigneeRule.setRoleProd(roleProd);
        roleAssigneeRule.setOperationModel(true);

        // Create a mock processInstanceId
        String processInstanceId = null;

        // Create a mock variables map
        Map<String, Object> variables = new HashMap<>();
        // Add necessary variables to the map
        variables.put("initiator", "dingxiaoyan");

        // Call the method to be tested
        List<String> assignees = roleAssigneeServiceImpl.buildAssignee(roleAssigneeRule, processInstanceId, variables);

        System.out.println(assignees);
        // Verify the result
        // For example, assert that the assignees list is not empty
        assertNotNull(assignees);
        // Add more assertions based on the expected behavior
        // For example, assertTrue(assignees.contains("expectedAssignee"));

    }
}
