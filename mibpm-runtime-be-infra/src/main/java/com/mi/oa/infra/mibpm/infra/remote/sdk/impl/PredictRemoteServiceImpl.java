package com.mi.oa.infra.mibpm.infra.remote.sdk.impl;

import com.google.common.collect.Lists;
import com.mi.id.oaucf.feign.BaseResp;
import com.mi.oa.infra.mibpm.common.event.PredictEvent;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.infra.remote.converter.PredictTaskConverter;
import com.mi.oa.infra.mibpm.infra.remote.entity.PredictTaskDto;
import com.mi.oa.infra.mibpm.infra.remote.sdk.MessageRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.PredictRemoteSdk;
import com.mi.oa.infra.mibpm.infra.remote.sdk.PredictRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.runtime.Execution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/1 15:27
 **/
@Service
@Slf4j
public class PredictRemoteServiceImpl implements PredictRemoteService {
    @Autowired
    private PredictRemoteSdk predictRemoteSdk;
    @Autowired
    private PredictTaskConverter predictTaskConverter;
    @Autowired
    private MessageRemoteService messageRemoteService;
    @Autowired
    private RuntimeService runtimeService;
    @Value("${rocketmq.task-predict-topic}")
    private String taskPredictTopic;

    @Override
    public List<TaskDo> getPredictTasks(String procInstId, String taskKeys, String processDefId, String lang, boolean sync) {
        BaseResp<List<PredictTaskDto>> resp = predictRemoteSdk.getPredictTasks(procInstId, taskKeys, lang, sync);
        if (null != resp && resp.getCode() == 0 && null != resp.getData()) {
            for (PredictTaskDto item : resp.getData()) {
                item.setProcessDefinitionId(processDefId);
            }
            return predictTaskConverter.dtoToDo(resp.getData());
        }
        return Lists.newArrayList();
    }

    @Override
    public void sendPredictMessage(TaskDo taskDo) {
        // 发送流程预测事件
        try {
            List<Execution> list = runtimeService.createExecutionQuery()
                    .processInstanceId(taskDo.getProcessInstanceId())
                    .list();
            if (CollectionUtils.isNotEmpty(list)) {
                List<String> taskDefKeys = list.stream().map(Execution::getActivityId)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());
                messageRemoteService.sendMessage(taskPredictTopic, taskDo.getProcessInstanceId(),
                        new PredictEvent(taskDo.getProcessInstanceId(), taskDo.getTaskVariables(),
                                taskDefKeys, taskDo.getProcessDefinitionId()));
            }
        } catch (Exception e) {
            log.error("send message error taskId={}", taskDo.getTaskId(), e);
        }
    }

    @Override
    public void sendPredictMessage(ProcessInstanceDo processInstanceDo) {
        // 发送流程预测事件
        try {
            List<Execution> list = runtimeService.createExecutionQuery()
                    .processInstanceId(processInstanceDo.getProcessInstanceId()).list();
            List<String> taskDefKeys = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(list)) {
                taskDefKeys = list.stream().map(Execution::getActivityId).filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());
            }
            messageRemoteService.sendMessage(taskPredictTopic, processInstanceDo.getProcessInstanceId(),
                    new PredictEvent(processInstanceDo.getProcessInstanceId(), processInstanceDo.getProcessVariables(),
                            taskDefKeys, processInstanceDo.getProcessDefinitionId()));
        } catch (Exception e) {
            log.error("send message error procInstId={}", processInstanceDo.getProcessInstanceId(), e);
        }
    }
}
