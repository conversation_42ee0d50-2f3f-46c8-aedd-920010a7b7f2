package com.mi.oa.infra.mibpm.infra.config;

import com.mi.flowable.external.api.ExternalHistoryService;
import com.mi.flowable.external.api.ExternalService;
import com.mi.flowable.external.impl.ExternalServiceConfiguration;
import com.mi.flowable.info.spring.SpringMiEngineConfiguration;
import com.mi.flowable.rpc.radar.RemoteRadarConfiguration;
import com.mi.ucf.mdd.MddEngineClientConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @description
 * @date 2022/4/7 4:34 PM
 **/
@Configuration
public class MiExternalServiceConfiguration {

    @Autowired
    private DataSource dataSource;
    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    @Bean
    public RemoteRadarConfiguration getRemoteRadarConfiguration() {
        return new RemoteRadarConfiguration("https://radar.mioffice.cn", "bpm", "dc40078438e31c66c1e63692eca0ca53");
    }

    @Bean
    public SpringMiEngineConfiguration getSpringMiEngineConfiguration() {
        SpringMiEngineConfiguration springMiEngineConfiguration = new SpringMiEngineConfiguration();
        springMiEngineConfiguration.setDataSource(dataSource);
        springMiEngineConfiguration.setTransactionManager(platformTransactionManager);
        springMiEngineConfiguration.setRemoteRadarConfiguration(getRemoteRadarConfiguration());
        springMiEngineConfiguration.setMddEngineClientConfiguration(new MddEngineClientConfiguration("https://t.mioffice.cn/mdd"));
        springMiEngineConfiguration.buildMiEngine();
        return springMiEngineConfiguration;
    }

    @Bean
    public ExternalServiceConfiguration getExternalServiceConfiguration() {
        return getSpringMiEngineConfiguration().getExternalServiceConfiguration();
    }

    @Bean
    public ExternalService getExternalService() {
        return getExternalServiceConfiguration().getExternalService();
    }

    @Bean
    public ExternalHistoryService getExternalHistoryService() {
        return getExternalServiceConfiguration().getHistoricService();
    }
}
