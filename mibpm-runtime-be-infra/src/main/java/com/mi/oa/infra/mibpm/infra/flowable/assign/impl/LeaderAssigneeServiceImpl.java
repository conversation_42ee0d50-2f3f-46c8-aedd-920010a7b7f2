package com.mi.oa.infra.mibpm.infra.flowable.assign.impl;

import com.mi.oa.infra.mibpm.common.model.BpmOrg;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.flowable.extension.model.AssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.AssigneeRuleType;
import com.mi.oa.infra.mibpm.flowable.extension.model.DeptTypeEnum;
import com.mi.oa.infra.mibpm.flowable.extension.model.LeaderAssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.LeaderConfigRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.LeaderTypeEnum;
import com.mi.oa.infra.mibpm.flowable.extension.model.RelativeLevelEnum;
import com.mi.oa.infra.mibpm.flowable.extension.model.SpecifiedLeaderTypeEnum;
import com.mi.oa.infra.mibpm.infra.flowable.assign.AbstractAssigneeService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.OrgRemoteService;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.MiErpProcessAssignee;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import com.mi.oa.infra.organization.rep.OrgVO;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.flowable.engine.delegate.DelegateExecution;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 部门负责人
 *
 * <AUTHOR>
 * @description
 * @date 2022/3/9 5:30 PM
 **/
@Service
public class LeaderAssigneeServiceImpl extends AbstractAssigneeService {
    @Override
    public AssigneeRuleType getAssigneeServiceKey() {
        return AssigneeRuleType.LEADER;
    }

    @Autowired
    private AccountRemoteService accountRemoteService;
    @Autowired
    private OrgRemoteService orgRemoteService;

    @Override
    public List<String> buildAssignee(AssigneeRule assigneeRule, DelegateExecution execution) {
        return getAssigneeList((LeaderAssigneeRule) assigneeRule, execution.getVariables());
    }

    @Override
    public List<String> buildAssignee(AssigneeRule assigneeRule, String processDefinitionId, Map<String, Object> variables) {
        return getAssigneeList((LeaderAssigneeRule) assigneeRule, variables);
    }

    @NotNull
    public List<String> getAssigneeList(LeaderAssigneeRule leaderAssigneeRule, Map<String, Object> variables) {
        String json = GsonUtils.toJsonWtihNullField(leaderAssigneeRule);
        logger.info("单个领导审批规则 assigneeRule={}", json);
        String passvalue = leaderAssigneeRule.getPassvalue();
        List<String> users = extractVariable(variables, passvalue);
        LeaderConfigRule leaderConfig = leaderAssigneeRule.getLeaderConfig();
        List<String> result = new ArrayList<>();
        for (String user : users) {
            List<BpmUser> reportLineUsers = accountRemoteService.listUserReportLine(user);
            List<String> collect = buildReportLineAssignee(reportLineUsers, leaderConfig, user);
            result.addAll(collect);
        }
        logger.info("单个领导审批规则 result={}", result);
        return result;
    }

    private List<String> buildReportLineAssignee(List<BpmUser> reportLineUsers, LeaderConfigRule leaderConfig, String initiator) {
        ArrayList<String> result = Lists.newArrayList();
        List<BpmUser> reportLineUserList = filterReportLine(reportLineUsers, initiator);
        if (Objects.isNull(leaderConfig)) {
            return result;
        }
        LeaderTypeEnum leaderType = LeaderTypeEnum.findByCode(leaderConfig.getLeaderType());
        RelativeLevelEnum levelEnum = RelativeLevelEnum.findByCode(leaderConfig.getLevel());
        SpecifiedLeaderTypeEnum specifiedLeaderType = SpecifiedLeaderTypeEnum.findByCode(leaderConfig.getSpecifiedLeaderType());
        int cursor = levelEnum == null ? 0 : levelEnum.getLevel();
        int baseLevel = 0;
        switch (leaderType) {
            case DIRECT:
                baseLevel = 1;
                result.add(findSupperManagerUser(reportLineUserList, baseLevel + cursor));
                break;
            case HIGHEST:
                // 最高上级指雷总下一级
                baseLevel = reportLineUsers.size() - 2;
                result.add(findSupperManagerUser(reportLineUserList, baseLevel + cursor));
                break;
            case SPECIFIED:
                result.add(findSpecifiedLeader(reportLineUserList, levelEnum, specifiedLeaderType, initiator));
                break;
        }
        return result.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    @NotNull
    private String findSpecifiedLeader(List<BpmUser> users, RelativeLevelEnum levelEnum,
                                       SpecifiedLeaderTypeEnum specifiedLeaderType, String user) {
        if (null == specifiedLeaderType) {
            return "";
        }
        BpmUser userVo = accountRemoteService.getUser(user);
        BpmOrg org = userVo.getOrg();
        List<OrgVO> orgVOS = orgRemoteService.listOrg(org.getOrgId());
        switch (specifiedLeaderType) {
            case LEADER_IN_CHARGE:
                if (users.size() > 1) {
                    return users.get(users.size() - 1).getUserName();
                }
                break;
            case LEVEL_ONE_DEPT_MANAGER:
                return getOrgOwner(orgVOS, DeptTypeEnum.LEVEL1_DEPT, levelEnum, users);
            case LEVEL_TWO_DEPT_MANAGER:
                return getOrgOwner(orgVOS, DeptTypeEnum.LEVEL2_DEPT, levelEnum, users);
            case LEVEL_THREE_DEPT_MANAGER:
                return getOrgOwner(orgVOS, DeptTypeEnum.LEVEL3_DEPT, levelEnum, users);
            case LEVEL_FOUR_DEPT_MANAGER:
                return getOrgOwner(orgVOS, DeptTypeEnum.LEVEL4_DEPT, levelEnum, users);
            case LEVEL_FIVE_DEPT_MANAGER:
                return getOrgOwner(orgVOS, DeptTypeEnum.LEVEL5_DEPT, levelEnum, users);
        }
        return "";
    }

    private String getOrgOwner(List<OrgVO> orgVOS, DeptTypeEnum deptTypeEnum, RelativeLevelEnum levelEnum, List<BpmUser> users) {
        int orgIndex = getOrgOwnerIndex(orgVOS, deptTypeEnum, levelEnum, users);
        if (orgIndex > -1 && orgIndex < users.size()) {
            BpmUser bpmUser = users.get(orgIndex);
            return bpmUser.getUserName();
        }
        return "";
    }

    @Override
    public AssigneeRule convert(MiErpProcessAssignee miErpProcessAssignee) {
        return new LeaderAssigneeRule();
    }
}
