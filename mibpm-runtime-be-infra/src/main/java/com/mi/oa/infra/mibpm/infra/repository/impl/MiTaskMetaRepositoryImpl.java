package com.mi.oa.infra.mibpm.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mi.flowable.external.api.ExtProcessItem;
import com.mi.flowable.external.api.ExternalService;
import com.mi.oa.infra.mibpm.domain.mitask.model.MiTaskMetaDo;
import com.mi.oa.infra.mibpm.domain.mitask.model.ExternalAppInfoDo;
import com.mi.oa.infra.mibpm.infra.mitask.repository.MiTaskMetaRepository;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.converter.MiTaskMetaConverter;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.CategoryMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.ExternalAppInfoMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.ModelMetaMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.CategoryPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ExternalAppInfoPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ModelMetaPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Objects;
import java.util.Random;

@Repository
public class MiTaskMetaRepositoryImpl implements MiTaskMetaRepository {
    @Autowired
    protected ModelMetaMapper modelMetaMapper;
    @Autowired
    protected MiTaskMetaConverter miTaskMetaConverter;
    @Autowired
    private ExternalAppInfoMapper externalAppInfoMapper;
    @Autowired
    private CategoryMapper categoryMapper;
    @Autowired
    private ExternalService externalService;

    private static final String PREFIX = "ext_";

    @Override
    public void createMiTaskMeta(MiTaskMetaDo miTaskMetaDo) {

        String categoryCode = createCategory(miTaskMetaDo);

        ModelMetaPo modelMetaPo = miTaskMetaConverter.convert(miTaskMetaDo);
        modelMetaPo.setModelCode(getModelCode(miTaskMetaDo.getProcessKey()));
        ModelMetaPo inDb = modelMetaMapper.queryByModelId(modelMetaPo.getModelCode());
        modelMetaPo.setCategoryCode(categoryCode);
        if (inDb != null) {
            modelMetaPo.setId(inDb.getId());
            modelMetaMapper.updateById(modelMetaPo);
        } else {
            modelMetaMapper.insert(modelMetaPo);
        }
    }

    private String createCategory(MiTaskMetaDo miTaskMetaDo) {
        String categoryCode = "";
        // 处理分类
        CategoryPo categoryInDb = categoryMapper.selectOne(new QueryWrapper<CategoryPo>().lambda()
                .eq(CategoryPo::getExtKey, miTaskMetaDo.getCategoryKey()));
        if (Objects.isNull(categoryInDb)) {
            CategoryPo parent = miTaskMetaConverter.convertToCategory(miTaskMetaDo);
            parent.setCode(PREFIX.concat(generateRandomNumberString()));
            parent.setExtKey("");
            categoryMapper.insert(parent);

            CategoryPo categoryPo = miTaskMetaConverter.convertToCategory(miTaskMetaDo);
            categoryPo.setCode(getCategoryCode(miTaskMetaDo.getCategoryKey()));
            categoryPo.setParentCode(parent.getCode());
            categoryMapper.insert(categoryPo);
            categoryCode = categoryPo.getCode();
        } else {
            categoryCode = categoryInDb.getCode();
        }
        return categoryCode;
    }

    @Override
    public MiTaskMetaDo findByModelCodeAndAppCode(String modelCode, String appCode) {
        ModelMetaPo modelMetaPo =
                modelMetaMapper.selectOne(new QueryWrapper<ModelMetaPo>().lambda().eq(ModelMetaPo::getModelCode, modelCode)
                        .eq(ModelMetaPo::getAppCode, appCode)
                        .eq(ModelMetaPo::getIsDeleted, 0));
        return miTaskMetaConverter.convert(modelMetaPo);
    }

    @Override
    public ExternalAppInfoDo findAppInfoByAppId(String appId) {
        // externalAppInfoMapper 根据appId查询一个结果
        ExternalAppInfoPo externalAppInfoPo = externalAppInfoMapper.selectOne(new QueryWrapper<ExternalAppInfoPo>().lambda()
                .eq(ExternalAppInfoPo::getCreateAppId, appId));
        return miTaskMetaConverter.convert(externalAppInfoPo);
    }

    @Override
    public ExternalAppInfoDo findAppInfoByAppCode(String appCode) {
        ExternalAppInfoPo externalAppInfoPo = externalAppInfoMapper.selectOne(new QueryWrapper<ExternalAppInfoPo>().lambda()
                .eq(ExternalAppInfoPo::getAppCode, appCode));
        return miTaskMetaConverter.convert(externalAppInfoPo);
    }

    private String getModelCode(String processKey) {
        ExtProcessItem extProcessItem = externalService.createProcessItemQuery()
                .defKey(processKey).singleResult();
        if (extProcessItem != null) {
            return extProcessItem.getProcessKey();
        }
        return processKey;
    }

    private String getCategoryCode(String categoryKey) {
        ExtProcessItem extProcessItem = externalService.createProcessItemQuery()
                .defKey(categoryKey).singleResult();
        if (extProcessItem != null) {
            return extProcessItem.getProcessKey();
        }
        return PREFIX.concat(generateRandomNumberString());
    }

    private String generateRandomNumberString() {
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        sb.append(random.nextInt(9) + 1);
        for (int i = 1; i < 20; i++) {
            sb.append(random.nextInt(10));
        }
        return sb.toString();
    }
}
