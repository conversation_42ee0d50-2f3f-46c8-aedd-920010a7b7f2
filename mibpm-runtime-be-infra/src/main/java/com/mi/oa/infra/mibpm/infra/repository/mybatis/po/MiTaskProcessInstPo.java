package com.mi.oa.infra.mibpm.infra.repository.mybatis.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/9/20
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName(value = "mibpm_process_inst_task", autoResultMap = true)
public class MiTaskProcessInstPo {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 流程实例ID
     */
    private String procInstId;
    /**
     * 流程实例名称
     */
    private String procInstName;
    /**
     * 流程定义ID
     */
    private String procDefId;
    /**
     * 流程定义编码
     */
    private String modelCode;
    /**
     * 流程业务编码
     */
    private String businessKey;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 节点定义ID
     */
    private String taskDefKey;
    /**
     * 执行实例ID
     */
    private String executionId;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 父节点实例ID
     */
    private String parentTaskId;
    /**
     * 流程发起人ID
     */
    private String procInstStarter;
    /**
     * 流程状态
     */
    private String processInstanceStatus;
    /**
     * 是否是快捷审批
     */
    private Boolean isFastApproval;
    /**
     * 签收人（默认为空，只有在委托时才有值）
     */
    private String owner;
    /**
     * 签收人
     */
    private String assignee;
    /**
     * 开始时间（毫秒级Unix时间戳）
     */
    private Long startTime;
    /**
     * 认领时间（毫秒级Unix时间戳）
     */
    private Long claimTime;
    /**
     * 结束时间（毫秒级Unix时间戳）
     */
    private Long endTime;
    /**
     * 过期时间，表明任务应在多长时间内完成（毫秒级Unix时间戳）
     */
    private Long dueDate;
    /**
     * 耗时（毫秒）
     */
    private Long duration;
    /**
     * 用户操作 通过/驳回等
     */
    private String operation;
    /**
     * 节点审批方式
     */
    private String signType;
    /**
     * 是否为开始任务
     */
    private Boolean isStartTask;
    /**
     * 用户操作客户端 web/小程序
     */
    private String client;
    /**
     * 数据来源 百特搭/统一待办/BPM流程
     */
    private String source;
    /**
     * 创建时间（毫秒级Unix时间戳）
     */
    private Long createTime;
    /**
     * 最后更新人
     */
    private String updateUser;
    /**
     * 最后更新时间（毫秒级Unix时间戳）
     */
    private Long updateTime;
    /**
     * 版本号
     */
    private Long version;
}


