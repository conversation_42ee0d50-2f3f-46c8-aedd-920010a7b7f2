package com.mi.oa.infra.mibpm.infra.flowable.behavior;

import com.mi.oa.infra.mibpm.infra.flowable.assign.AssigneeFactory;
import com.mi.oa.infra.mibpm.flowable.extension.BpmnExtensionHelper;
import org.flowable.bpmn.model.Activity;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.impl.bpmn.behavior.AbstractBpmnActivityBehavior;
import org.flowable.engine.impl.bpmn.behavior.ParallelMultiInstanceBehavior;
import org.flowable.engine.impl.bpmn.behavior.SequentialMultiInstanceBehavior;
import org.flowable.engine.impl.bpmn.behavior.UserTaskActivityBehavior;
import org.flowable.engine.impl.bpmn.helper.ClassDelegateFactory;
import org.flowable.engine.impl.bpmn.parser.factory.DefaultActivityBehaviorFactory;

/**
 * 注册自定义活动行为解析器
 *
 * <AUTHOR>
 * @date 2021/11/18 20:12
 */
public class CustomActivityBehaviorFactory extends DefaultActivityBehaviorFactory {

    protected AssigneeFactory assigneeFactory;
    protected BpmnExtensionHelper bpmnExtensionHelper;

    public CustomActivityBehaviorFactory(ClassDelegateFactory classDelegateFactory,
                                         AssigneeFactory assigneeFactory,
                                         BpmnExtensionHelper bpmnExtensionHelper) {
        super(classDelegateFactory);
        this.assigneeFactory = assigneeFactory;
        this.bpmnExtensionHelper = bpmnExtensionHelper;
    }

    public CustomActivityBehaviorFactory(AssigneeFactory assigneeFactory,
                                         BpmnExtensionHelper bpmnExtensionHelper) {
        this.assigneeFactory = assigneeFactory;
        this.bpmnExtensionHelper = bpmnExtensionHelper;
    }

    /**
     * 重写单实例用户任务行为
     */
    @Override
    public UserTaskActivityBehavior createUserTaskActivityBehavior(UserTask userTask) {
        return new CustomUserTaskActivityBehavior(userTask, assigneeFactory, bpmnExtensionHelper);
    }

    /**
     * 重写多实例串行用户任务行为
     */
    @Override
    public SequentialMultiInstanceBehavior createSequentialMultiInstanceBehavior(Activity activity,
                                                                                 AbstractBpmnActivityBehavior innerActivityBehavior) {
        CustomMultiInstanceBehaviorHelper customMultiInstanceBehaviorHelper = new CustomMultiInstanceBehaviorHelper(
                activity, assigneeFactory, bpmnExtensionHelper);
        return new CustomSequentialMultiInstanceBehavior(activity, innerActivityBehavior, customMultiInstanceBehaviorHelper);
    }

    /**
     * 重写多实例并行任务行为
     */
    @Override
    public ParallelMultiInstanceBehavior createParallelMultiInstanceBehavior(Activity activity,
                                                                             AbstractBpmnActivityBehavior innerActivityBehavior) {
        CustomMultiInstanceBehaviorHelper customMultiInstanceBehaviorHelper = new CustomMultiInstanceBehaviorHelper(
                activity, assigneeFactory, bpmnExtensionHelper);
        return new CustomParallelMultiInstanceBehavior(activity, innerActivityBehavior, customMultiInstanceBehaviorHelper);
    }

    public AssigneeFactory getAssigneeFactory() {
        return assigneeFactory;
    }

    public BpmnExtensionHelper getBpmnExtensionHelper() {
        return bpmnExtensionHelper;
    }
}
