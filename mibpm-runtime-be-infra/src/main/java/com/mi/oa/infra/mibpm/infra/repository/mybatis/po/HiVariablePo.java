package com.mi.oa.infra.mibpm.infra.repository.mybatis.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 变量表
 *
 * <AUTHOR>
 * @date 2024/10/22 10:52
 */
@Data
@TableName(value = "act_hi_taskinst", autoResultMap = true)
public class HiVariablePo implements Serializable {
    @TableId("ID_")
    private String id;

    @TableField("PROC_DEF_ID_")
    private String procDefId;

    @TableField("TASK_DEF_KEY_")
    private String taskDefKey;

    @TableField("PROC_INST_ID_")
    private String procInstId;

    @TableField("EXECUTION_ID_")
    private String executionId;

    @TableField("NAME_")
    private String name;

    @TableField("PARENT_TASK_ID_")
    private String parentTaskId;

    @TableField("DESCRIPTION_")
    private String description;

    @TableField("OWNER_")
    private String owner;

    @TableField("ASSIGNEE_")
    private String assignee;

    @TableField("START_TIME_")
    private Date startTime;

    @TableField("CLAIM_TIME_")
    private Date claimTime;

    @TableField("END_TIME_")
    private Date endTime;

    @TableField("DURATION_")
    private Long duration;

    @TableField("DELETE_REASON_")
    private String deleteReason;

    @TableField("PRIORITY_")
    private Integer priority;

    @TableField("DUE_DATE_")
    private Date dueDate;

    @TableField("FORM_KEY_")
    private String formKey;

    @TableField("CATEGORY_")
    private String category;

    @TableField("TENANT_ID_")
    private String tenantId;

    @TableField("FANWEI_SYNC_STATUS_")
    private Integer fanweiSyncStatus;

    @TableField("FANWEI_DATAID_")
    private String fanweiDataId;

    @TableField("mim_event_id_")
    private String mimEventId;

    @TableField("mim_event_state")
    private Integer mimEventState;

    @TableField("REV_")
    private Integer rev;

    @TableField("LAST_UPDATED_TIME_")
    private Date lastUpdatedTime;

    @TableField("SCOPE_ID_")
    private String scopeId;

    @TableField("SUB_SCOPE_ID_")
    private String subScopeId;

    @TableField("SCOPE_TYPE_")
    private String scopeType;

    @TableField("SCOPE_DEFINITION_ID_")
    private String scopeDefinitionId;

    @TableField("TASK_DEF_ID_")
    private String taskDefId;

    @TableField("PROPAGATED_STAGE_INST_ID_")
    private String propagatedStageInstId;
}

