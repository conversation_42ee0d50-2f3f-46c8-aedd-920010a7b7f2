package com.mi.oa.infra.mibpm.infra.repository.impl;

import com.google.common.collect.Lists;
import com.mi.oa.infra.mibpm.common.constant.BpmVariablesConstants;
import com.mi.oa.infra.mibpm.common.enums.ActivityTypeEnum;
import com.mi.oa.infra.mibpm.common.enums.DelegationStateEnum;
import com.mi.oa.infra.mibpm.common.exception.InfraException;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.UserTaskActivity;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.flowable.bpmn.BpmnModelService;
import com.mi.oa.infra.mibpm.flowable.extension.model.RollbackStrategy;
import com.mi.oa.infra.mibpm.flowable.extension.model.SignAddTypeEnum;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskSignType;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskWrapper;
import com.mi.oa.infra.mibpm.infra.flowable.sign.SignTaskContext;
import com.mi.oa.infra.mibpm.infra.flowable.variable.ProcessVariableFactory;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.converter.TaskInstPoConverter;
import com.mi.oa.infra.mibpm.infra.task.errorcode.TaskInfraErrorCodeEnum;
import com.mi.oa.infra.mibpm.infra.task.repository.HistoricTaskRepository;
import com.mi.oa.infra.mibpm.infra.task.repository.TaskRepository;
import com.mi.oa.infra.mibpm.utils.IdHelper;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.FlowNode;
import org.flowable.bpmn.model.InclusiveGateway;
import org.flowable.bpmn.model.ParallelGateway;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.ReceiveTask;
import org.flowable.bpmn.model.SequenceFlow;
import org.flowable.bpmn.model.StartEvent;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.HistoryService;
import org.flowable.engine.ManagementService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.runtime.ActivityInstance;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.identitylink.api.IdentityLinkInfo;
import org.flowable.identitylink.api.IdentityLinkType;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskInfo;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.service.HistoricTaskService;
import org.flowable.task.service.impl.persistence.entity.HistoricTaskInstanceEntity;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.flowable.task.service.impl.persistence.entity.TaskEntityImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.Deque;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/2/15 3:29 PM
 **/
@Repository
@Slf4j
public class TaskRepositoryImpl implements TaskRepository {

    @Autowired
    private TaskService taskService;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private ProcessVariableFactory processVariableFactory;
    @Autowired
    private BpmnModelService bpmnModelService;
    @Autowired
    private TaskInstPoConverter taskInstPoConverter;
    @Autowired
    private ManagementService managementService;
    @Autowired
    private HistoricTaskRepository historicTaskRepository;
    @Autowired
    private AccountRemoteService accountRemoteService;
    @Autowired
    private SignTaskContext signTaskContext;

    @Override
    public TaskDo queryTask(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            throw new InfraException(TaskInfraErrorCodeEnum.PARAM_TASK_ID_IS_EMPTY);
        }
        Task task = taskService.createTaskQuery()
                .taskId(taskId)
                .locale(LocaleContextHolder.getLocale().getLanguage())
                .singleResult();

        TaskDo taskDo = taskInstPoConverter.poToDo((TaskEntityImpl) task);
        if (null != taskDo) {
            log.info("query task taskId={} assignee={}", taskDo.getTaskId(), taskDo.getAssignee());
        }
        taskInstPoConverter.fillTaskDo(taskDo);
        return taskDo;
    }

    @Override
    public List<TaskDo> queryTaskList(String procInstId) {
        if (StringUtils.isBlank(procInstId)) {
            throw new InfraException(TaskInfraErrorCodeEnum.PARAM_PROC_INST_ID_IS_EMPTY);
        }
        List<Task> list = taskService.createTaskQuery()
                .processInstanceId(procInstId)
                .locale(LocaleContextHolder.getLocale().getLanguage())
                .list();

        List<TaskEntityImpl> taskEntities = new ArrayList<>();
        for (Task task : list) {
            taskEntities.add((TaskEntityImpl) task);
        }

        return taskInstPoConverter.poToDo(taskEntities);
    }

    @Override
    public List<TaskDo> queryReceiveTaskList(String procInstId) {
        if (StringUtils.isBlank(procInstId)) {
            throw new InfraException(TaskInfraErrorCodeEnum.PARAM_PROC_INST_ID_IS_EMPTY);
        }
        List<Execution> runningActivityInstanceList = runtimeService.createExecutionQuery()
                .processInstanceId(procInstId).list();
        List<String> runningActivityIdList = new ArrayList<>();
        for (Execution execution : runningActivityInstanceList) {
            if (StringUtils.isNotEmpty(execution.getActivityId())) {
                runningActivityIdList.add(execution.getActivityId());
            }
        }
        List<TaskDo> taskDos = historicTaskRepository.queryHistoricTasksByProcInstId(procInstId);
        taskDos.forEach(this::loadActivityType);
        return taskDos.stream().filter(i -> runningActivityIdList.contains(i.getTaskDefinitionKey()) &&
                        ActivityTypeEnum.RECEIVE_TASK.equals(i.getActivityType()))
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> queryTaskVariables(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            throw new InfraException(TaskInfraErrorCodeEnum.PARAM_TASK_ID_IS_EMPTY);
        }
        return taskService.getVariables(taskId);
    }

    @Override
    public Map<String, Object> queryTaskVariablesLocal(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            throw new InfraException(TaskInfraErrorCodeEnum.PARAM_TASK_ID_IS_EMPTY);
        }
        return taskService.getVariablesLocal(taskId);
    }

    @Override
    public List<UserTaskActivity> queryReturnActivities(TaskDo taskDo) {
        if (taskDo == null || StringUtils.isBlank(taskDo.getProcessInstanceId())) {
            throw new InfraException(TaskInfraErrorCodeEnum.PARAM_PROC_INST_ID_IS_EMPTY);
        }
        String procInstId = taskDo.getProcessInstanceId();
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(procInstId)
                .singleResult();
        if (Objects.isNull(processInstance)) {
            throw new InfraException(TaskInfraErrorCodeEnum.PROC_INST_NOT_EXISTS, procInstId);
        }

        List<HistoricTaskInstance> historicTasks = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(procInstId)
                .finished()
                .orderByTaskCreateTime().asc()
                .orderByHistoricTaskInstanceEndTime().asc()
                .list();

        Iterator<HistoricTaskInstance> iterator = historicTasks.iterator();
        boolean find = false;
        while (iterator.hasNext()) {
            HistoricTaskInstance next = iterator.next();
            if (next.getTaskDefinitionKey().equals(taskDo.getTaskDefinitionKey())) {
                find = true;
            }
            if (find) {
                iterator.remove();
            }
        }

        List<UserTaskActivity> userTaskList = new ArrayList<>();
        if (CollectionUtils.isEmpty(historicTasks)) {
            return userTaskList;
        }

        // 提取所有历史任务节点
        List<String> taskDefKeys = historicTasks.stream()
                .map(TaskInfo::getTaskDefinitionKey)
                .distinct()
                .collect(Collectors.toList());
        Process process = bpmnModelService.findMainProcess(processInstance.getProcessDefinitionId());

        for (String taskDefKey : taskDefKeys) {
            FlowElement flowElement = process.getFlowElement(taskDefKey);
            UserTaskActivity userTaskActivity = new UserTaskActivity(flowElement.getId(), flowElement.getName());
            userTaskList.add(userTaskActivity);
        }
        return userTaskList;
    }

    /**
     * 从用户节点到开始节点之间不经过包容或并行网关即称为简单任务节点
     * stack为空则视为简单任务节点
     */
    private void checkSimpleUserTask(FlowNode flowNode, Deque<FlowNode> deque) {
        deque.push(flowNode);
        List<SequenceFlow> incomingFlows = flowNode.getIncomingFlows();
        for (SequenceFlow incomingFlow : incomingFlows) {
            FlowElement sourceFlowElement = incomingFlow.getSourceFlowElement();
            if (sourceFlowElement instanceof InclusiveGateway || sourceFlowElement instanceof ParallelGateway) {
                return;
            } else if (sourceFlowElement instanceof StartEvent) {
                if (deque.isEmpty()) {
                    return;
                }
                deque.pop();
            } else {
                if (deque.isEmpty()) {
                    return;
                }
                deque.pop();
                checkSimpleUserTask((FlowNode) sourceFlowElement, deque);
            }

        }
    }

    @Override
    public Map<String, Object> buildTaskVariables(TaskDo taskDo, Map<String, Object> taskVariables, boolean updateSummary) {
        if (StringUtils.isBlank(taskDo.getProcessDefinitionId())) {
            throw new InfraException(TaskInfraErrorCodeEnum.PARAM_PROC_DEF_ID_IS_EMPTY);
        }
        if (Objects.isNull(taskVariables)) {
            return new LinkedHashMap<>();
        }
        return processVariableFactory.buildVariable(taskDo.getProcessDefinitionId(), taskVariables, updateSummary);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void completeTask(TaskDo taskDo) {
        this.saveTask(taskDo);
        if (StringUtils.isBlank(taskDo.getExecutionId())) {
            // 子任务完成时清空变量
            taskDo.setTaskVariables(null);
        }
        taskService.complete(taskDo.getTaskId(), taskDo.getTaskVariables(), taskDo.getTransientVariables());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void skipTask(TaskDo taskDo, String targetActivityId) {
        if (StringUtils.isBlank(targetActivityId)) {
            throw new InfraException(TaskInfraErrorCodeEnum.PARAM_TASK_ASSIGNEE_IS_EMPTY);
        }
        if (StringUtils.isBlank(taskDo.getProcessInstanceId())) {
            throw new InfraException(TaskInfraErrorCodeEnum.PARAM_PROC_INST_ID_IS_EMPTY);
        }
        List<ActivityInstance> activityInstances = runtimeService.createActivityInstanceQuery()
                .processInstanceId(taskDo.getProcessInstanceId()).list();
        List<Execution> executions = runtimeService.createExecutionQuery()
                .processInstanceId(taskDo.getProcessInstanceId()).list();
        List<Execution> childrenExecutions = executions.stream()
                .filter(e -> StringUtils.equals(taskDo.getProcessInstanceId(), e.getParentId())).collect(Collectors.toList());
        boolean reserveGateway = calculateReserveGateway(taskDo, activityInstances, executions);
        // 保存任务变量和参数
        this.saveTask(taskDo);
        if (reserveGateway) {
            rollbackTaskWithoutGateway(taskDo, targetActivityId, executions, activityInstances);
        } else {
            if (CollectionUtils.isEmpty(executions)) {
                return;
            }
            List<String> executionIds =
                    childrenExecutions.stream().filter(e -> null != e.getActivityId()).map(Execution::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(executionIds)) {
                return;
            }
            //执行退回操作
            runtimeService.createChangeActivityStateBuilder()
                    .moveExecutionsToSingleActivityId(executionIds, targetActivityId)
                    .changeState();
        }
    }

    /**
     * 计算是否需要保留网关，即判断是否包含并行
     *
     * @param taskDo
     * @return
     */
    private boolean calculateReserveGateway(TaskDo taskDo, List<ActivityInstance> activityInstances,
                                            List<Execution> executions) {
        boolean reserveGateway = false;
        if (null != taskDo.getUserTaskWrapper() && null != taskDo.getUserTaskWrapper().getRollbackStrategy()) {
            RollbackStrategy rollbackStrategy = taskDo.getUserTaskWrapper().getRollbackStrategy();
            if (StringUtils.equals(rollbackStrategy.getBackPath(), RollbackStrategy.DIRECT)) {
                List<String> activityIds = activityInstances.stream().filter(i -> null == i.getEndTime())
                        .map(ActivityInstance::getActivityId).collect(Collectors.toList());
                long count = executions.stream().map(Execution::getActivityId).filter(Objects::nonNull).distinct().count();
                // 超过一个活跃节点，表示当前任务是并行
                if (count > 1) {
                    reserveGateway = true;
                }
                Map<String, Object> taskVariables = taskDo.getTaskVariables();
                if (null == taskVariables) {
                    Map<String, Object> variablesMap = new LinkedHashMap<>();
                    variablesMap.put(BpmVariablesConstants.VARIABLE_SIGN_RETURN_ACTIVITY_ID, activityIds);
                    taskDo.setTaskVariables(variablesMap);
                } else {
                    taskVariables.put(BpmVariablesConstants.VARIABLE_SIGN_RETURN_ACTIVITY_ID, activityIds);
                }
            }
        }
        return reserveGateway;
    }

    /**
     * 退回节点但保留网关
     *
     * @param taskDo
     * @param targetActivityId
     * @param executions
     * @param activityInstances
     */
    private void rollbackTaskWithoutGateway(TaskDo taskDo, String targetActivityId, List<Execution> executions,
                                            List<ActivityInstance> activityInstances) {

        Map<String, Execution> executionMap = executions.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        if (CollectionUtils.isEmpty(activityInstances)) {
            return;
        }
        List<String> executionIds = new ArrayList<>();
        for (ActivityInstance activityInstance : activityInstances) {
            String executionId = activityInstance.getExecutionId();
            Execution execution = executionMap.get(executionId);
            if (null != activityInstance.getEndTime() || null == execution) {
                continue;
            }
            if (StringUtils.isNotBlank(execution.getParentId()) && !execution.getParentId().equals(taskDo.getProcessInstanceId())) {
                executionIds.add(execution.getParentId());
            } else {
                executionIds.add(executionId);
            }
        }
        if (CollectionUtils.isEmpty(executionIds)) {
            return;
        }
        //执行退回操作
        runtimeService.createChangeActivityStateBuilder()
                .moveExecutionsToSingleActivityId(executionIds, targetActivityId)
                .changeState();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delegateTask(TaskDo taskDo, String assignee) {
        if (StringUtils.isBlank(taskDo.getTaskId())) {
            throw new InfraException(TaskInfraErrorCodeEnum.PARAM_TASK_ID_IS_EMPTY);
        }
        if (Objects.isNull(taskDo.getAssignee())) {
            throw new InfraException(TaskInfraErrorCodeEnum.PARAM_TASK_ASSIGNEE_IS_EMPTY);
        }
        if (StringUtils.isBlank(assignee)) {
            throw new InfraException(TaskInfraErrorCodeEnum.PARAM_TASK_ASSIGNEE_IS_EMPTY);
        }

        // 不是委托状态的任务才能处理
        if (!DelegationStateEnum.PENDING.equals(taskDo.getDelegationState())) {
            this.addHistoryLogTask(taskDo, new Date());
            this.saveTask(taskDo);
            taskService.delegateTask(taskDo.getTaskId(), assignee);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void resolveTask(TaskDo taskDo) {
        if (StringUtils.isBlank(taskDo.getTaskId())) {
            throw new InfraException(TaskInfraErrorCodeEnum.PARAM_TASK_ID_IS_EMPTY);
        }

        // 处于委托状态的任务才能处理
        if (DelegationStateEnum.PENDING.equals(taskDo.getDelegationState())) {
            this.addHistoryLogTask(taskDo, new Date());
            this.saveTask(taskDo);
            taskService.resolveTask(taskDo.getTaskId());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void transferTask(TaskDo taskDo) {
        if (Objects.isNull(taskDo.getAssignee())) {
            throw new InfraException(TaskInfraErrorCodeEnum.PARAM_TASK_ASSIGNEE_IS_EMPTY);
        }
        if (StringUtils.isBlank(taskDo.getTaskId())) {
            throw new InfraException(TaskInfraErrorCodeEnum.PARAM_TASK_ID_IS_EMPTY);
        }
        // 生成转审记录
        this.addHistoryLogTask(taskDo, new Date());
        this.saveTask(taskDo);
        taskService.setAssignee(taskDo.getTaskId(), taskDo.getAssignee().getUid());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void signTask(TaskDo taskDo, List<BpmUser> assignee, SignAddTypeEnum signAddTypeEnum) {
        UserTaskWrapper userTaskWrapper = bpmnModelService.findUserTaskWrapper(taskDo.getProcessDefinitionId(), taskDo.getTaskDefinitionKey(), null);
        UserTaskSignType signType = userTaskWrapper.getSignType();
        this.saveTask(taskDo);
        signTaskContext.signTask(signType, signAddTypeEnum, taskDo, assignee);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void returnTaskToStartEvent(TaskDo taskDo) {
        if (StringUtils.isBlank(taskDo.getTaskId())) {
            throw new InfraException(TaskInfraErrorCodeEnum.PARAM_TASK_ID_IS_EMPTY);
        }
        if (StringUtils.isBlank(taskDo.getProcessDefinitionId())) {
            throw new InfraException(TaskInfraErrorCodeEnum.PARAM_PROC_DEF_ID_IS_EMPTY);
        }

        this.saveTask(taskDo);

        StartEvent startEvent = bpmnModelService.findStartEvent(taskDo.getProcessDefinitionId());
        if (Objects.isNull(startEvent)) {
            throw new InfraException(TaskInfraErrorCodeEnum.PROCESS_START_EVENT_NOT_EXISTS,
                    taskDo.getProcessDefinitionId());
        }

        // 提取所有执行实例的ID和活动节点ID
        List<String> executionIds = new ArrayList<>();
        List<String> activityIds = new ArrayList<>();
        List<Execution> executions = runtimeService.createExecutionQuery()
                .parentId(taskDo.getProcessInstanceId()).list();
        executions.forEach(execution -> {
            executionIds.add(execution.getId());
            activityIds.add(execution.getActivityId());
        });

        // 记录重新提交后要返回的活动节点ID
        taskService.setVariable(taskDo.getTaskId(), BpmVariablesConstants.VARIABLE_SIGN_RETURN_ACTIVITY_ID,
                activityIds);

        //执行退回操作
        runtimeService.createChangeActivityStateBuilder()
                .moveExecutionsToSingleActivityId(executionIds, startEvent.getId())
                .changeState();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void continueTaskFromStartEvent(TaskDo taskDo) {
        if (StringUtils.isBlank(taskDo.getTaskId())) {
            throw new InfraException(TaskInfraErrorCodeEnum.PARAM_TASK_ID_IS_EMPTY);
        }

        this.saveTask(taskDo);

        Task task = taskService.createTaskQuery().taskId(taskDo.getTaskId()).singleResult();
        if (Objects.isNull(task)) {
            throw new InfraException(TaskInfraErrorCodeEnum.TASK_NOT_EXISTS, taskDo.getTaskId());
        }

        // 获取重新提交后要返回的活动节点ID
        Object targetActivityIds = taskService.getVariable(taskDo.getTaskId(),
                BpmVariablesConstants.VARIABLE_SIGN_RETURN_ACTIVITY_ID);
        // 重新提交后要返回的活动节点ID为空时认为是执行了退回到发起节点，再次提交按照流程图运作
        if (Objects.isNull(targetActivityIds)) {
            taskService.complete(taskDo.getTaskId(), taskDo.getTaskVariables(), taskDo.getTransientVariables());
            return;
        }

        // 清空无效的流程变量
        taskService.removeVariable(taskDo.getTaskId(), BpmVariablesConstants.VARIABLE_SIGN_RETURN_ACTIVITY_ID);

        // 跳转到执行returnTaskToStartEvent之前的活动节点ID
        if (targetActivityIds instanceof List<?>) {
            List<String> activityIds = new ArrayList<>();
            for (Object targetActivityId : (List<?>) targetActivityIds) {
                if (targetActivityId instanceof String) {
                    activityIds.add((String) targetActivityId);
                }
            }

            runtimeService.createChangeActivityStateBuilder().processInstanceId(task.getProcessInstanceId())
                    .moveSingleActivityIdToActivityIds(task.getTaskDefinitionKey(), activityIds)
                    .changeState();
        } else if (targetActivityIds instanceof String) {
            String activityId = (String) targetActivityIds;
            runtimeService.createChangeActivityStateBuilder().processInstanceId(task.getProcessInstanceId())
                    .moveActivityIdsToSingleActivityId(Lists.newArrayList(task.getTaskDefinitionKey()), activityId)
                    .changeState();
        } else {
            throw new InfraException(TaskInfraErrorCodeEnum.COME_BACK_TARGET_ACTIVITY_TYPE_NOT_FOUND);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveTask(TaskDo taskDo) {
        if (Objects.nonNull(taskDo.getTaskVariables()) && !taskDo.getTaskVariables().isEmpty()) {
            this.setVariables(taskDo);
        }

        TaskEntityImpl taskEntity = taskInstPoConverter.doToPo(taskDo);
        TaskEntity task = (TaskEntity) taskService.createTaskQuery().taskId(taskDo.getTaskId()).singleResult();
        taskEntity.setRevision(task.getRevision());
        taskService.saveTask(taskEntity);
    }

    @Override
    public void loadUserTaskWrapper(TaskDo taskDo) {
        loadUserTaskWrapper(taskDo, null);
    }

    @Override
    public void loadUserTaskWrapper(TaskDo taskDo, BpmnModel bpmnModel) {
        if (StringUtils.isBlank(taskDo.getProcessDefinitionId())) {
            throw new InfraException(TaskInfraErrorCodeEnum.PARAM_PROC_DEF_ID_IS_EMPTY);
        }
        if (StringUtils.isBlank(taskDo.getTaskDefinitionKey())) {
            throw new InfraException(TaskInfraErrorCodeEnum.PARAM_TASK_DEF_KEY_IS_EMPTY);
        }
        UserTaskWrapper userTaskWrapper = bpmnModelService.findUserTaskWrapper(taskDo.getProcessDefinitionId(), taskDo.getTaskDefinitionKey(), bpmnModel);
        // 填充用户任务配置
        taskDo.setUserTaskWrapper(userTaskWrapper);
    }

    @Override
    public void loadActivityType(TaskDo taskDo) {
        if (Objects.nonNull(taskDo.getActivityType())) {
            return;
        }
        org.flowable.bpmn.model.Task task = bpmnModelService.findTask(taskDo.getProcessDefinitionId(),
                taskDo.getTaskDefinitionKey());
        if (task instanceof UserTask) {
            taskDo.setActivityType(ActivityTypeEnum.USER_TASK);
        } else if (task instanceof ReceiveTask) {
            taskDo.setActivityType(ActivityTypeEnum.RECEIVE_TASK);
        }
    }

    @Override
    public void loadActivityType(TaskDo taskDo, BpmnModel bpmnModel) {
        if (Objects.nonNull(taskDo.getActivityType())) {
            return;
        }
        org.flowable.bpmn.model.Task task = bpmnModelService.findTask(taskDo.getProcessDefinitionId(),
                taskDo.getTaskDefinitionKey());
        if (task instanceof UserTask) {
            taskDo.setActivityType(ActivityTypeEnum.USER_TASK);
        } else if (task instanceof ReceiveTask) {
            taskDo.setActivityType(ActivityTypeEnum.RECEIVE_TASK);
        }
    }

    @Override
    public void setVariables(TaskDo taskDo) {
        Map<String, Object> taskVariables = taskDo.getTaskVariables();
        if (Objects.nonNull(taskVariables)) {
            // 移除引擎使用的变量，这些变量不需要手工更新
            taskVariables.remove(BpmVariablesConstants.VARIABLE_NUMBER_ACTIVE_INSTANCES);
            taskVariables.remove(BpmVariablesConstants.VARIABLE_NUMBER_COMPLETED_INSTANCES);
            taskVariables.remove(BpmVariablesConstants.VARIABLE_NUMBER_TOTAL_INSTANCES);
            taskVariables.remove(BpmVariablesConstants.VARIABLE_LOOP_COUNTER);
            runtimeService.setVariables(taskDo.getProcessInstanceId(), taskVariables);
        }
    }

    @Override
    public void setAssignee(TaskDo taskDo) {
        if (Objects.nonNull(taskDo.getAssignee())) {
            taskService.setAssignee(taskDo.getTaskId(), taskDo.getAssignee().getUid());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setAssignees(List<TaskDo> taskDos) {
        if (CollectionUtils.isNotEmpty(taskDos)) {
            taskDos.forEach((taskDo -> {
                if (Objects.nonNull(taskDo.getAssignee())) {
                    taskService.setAssignee(taskDo.getTaskId(), taskDo.getAssignee().getUserName());
                }
            }));
        }
    }

    @Override
    public void receiveTask(TaskDo taskDo) {
        Execution execution = runtimeService.createExecutionQuery()
                .processInstanceId(taskDo.getProcessInstanceId())
                .activityId(taskDo.getTaskDefinitionKey())
                .singleResult();
        if (Objects.isNull(execution)) {
            throw new InfraException(TaskInfraErrorCodeEnum.RECEIVE_TASK_NOT_EXISTS);
        }
        runtimeService.trigger(execution.getId(), taskDo.getTaskVariables());
    }

    @Override
    public void addHistoryLogTask(TaskDo taskDo, Date endTime) {
        TaskEntityImpl taskEntity = taskInstPoConverter.doToPo(taskDo);

        HistoricTaskService historicTaskService = managementService.executeCommand(new HistoricTaskServiceGetCmd());
        HistoricTaskInstanceEntity newLogTask = historicTaskService.createHistoricTask(taskEntity);

        newLogTask.setAssignee(taskDo.getOperator() == null ? "" : taskDo.getOperator().getUid());
        newLogTask.setId(IdHelper.getNextId());
        newLogTask.setEndTime(endTime);
        newLogTask.setDurationInMillis(endTime == null ? 0 : endTime.getTime() - newLogTask
                .getCreateTime().getTime());
        newLogTask.setOwner(null);
        managementService.executeCommand(new HiTaskInsEntitySaveCmd(historicTaskService, newLogTask));
        // reset description
        if (StringUtils.isNotBlank(taskDo.getDescription())) {
            Map<String, Object> desc = GsonUtils.fromJson(taskDo.getDescription(), HashMap.class);
            desc.remove("status");
            desc.remove("comment");
            desc.remove("source");
            taskDo.setDescription(GsonUtils.toJsonFilterNullField(desc));
        }
    }

    @Override
    public void claimTask(TaskDo taskDo) {
        taskService.claim(taskDo.getTaskId(), taskDo.getOperator().getUid());
    }

    @Override
    public void loadTaskCandidates(TaskDo taskDo) {
        if (Objects.isNull(taskDo.getActivityType())) {
            loadActivityType(taskDo);
        }
        if (Objects.nonNull(taskDo.getEndTime()) || !taskDo.getActivityType().equals(ActivityTypeEnum.USER_TASK)) {
            return;
        }
        List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(taskDo.getTaskId());
        Set<String> userSets = identityLinks.stream()
                .filter(i -> IdentityLinkType.CANDIDATE.equals(i.getType()))
                .map(IdentityLinkInfo::getUserId)
                .collect(Collectors.toSet());
        List<BpmUser> bpmUsers = accountRemoteService.listUsers(userSets);
        taskDo.setCandidates(bpmUsers);
    }

    @Override
    public List<UserTaskActivity> queryRuntimeActivities(String procInstId) {
        return runtimeService.createExecutionQuery().processInstanceId(procInstId).list()
                .stream().filter(i -> StringUtils.isNotBlank(i.getActivityId()))
                .map(i -> new UserTaskActivity(i.getActivityId(), i.getName()))
                .collect(Collectors.toList());
    }
}
