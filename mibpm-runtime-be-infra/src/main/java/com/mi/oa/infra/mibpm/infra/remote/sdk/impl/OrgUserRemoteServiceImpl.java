package com.mi.oa.infra.mibpm.infra.remote.sdk.impl;

import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.infra.remote.converter.UserVoConverter;
import com.mi.oa.infra.mibpm.infra.remote.sdk.OrgUserRemoteService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import com.mi.oa.infra.organization.rep.UserVO;
import com.mi.oa.infra.organization.service.OrgUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/15 14:38
 */
@Slf4j
@Service
public class OrgUserRemoteServiceImpl implements OrgUserRemoteService {

    @Autowired
    private OrgUserService orgUserService;
    @Autowired
    private UserVoConverter userVoConverter;

    @Override
    public List<BpmUser> findUserByRoleAndOrg(String roleCoe, String orgCode, Boolean isExpand) {
        try {
            BaseResp<PageVO<UserVO>> resp = orgUserService.pageByRole(orgCode, roleCoe, null, isExpand.toString(), 1, 100);
            if (resp.getCode() == 0) {
                List<UserVO> userVOList = resp.getData().getList();
                log.info("调用根据组织和角色查询人员接口响应，roleCoe = {}, orgCode = {}, isExpand = {}，userVOList = {}",
                        roleCoe, orgCode, isExpand, userVOList);
                return userVoConverter.userVosToBpmUsers(userVOList);
            }
        } catch (Exception e) {
            log.error("调用根据组织和角色查询人员接口失败，roleCoe = {}, orgCode = {}, isExpand = {}",
                    roleCoe, orgCode, isExpand, e);
        }
        return Collections.emptyList();
    }
}
