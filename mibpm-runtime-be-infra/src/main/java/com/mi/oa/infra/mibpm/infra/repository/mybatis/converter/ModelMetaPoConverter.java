package com.mi.oa.infra.mibpm.infra.repository.mybatis.converter;

import com.mi.oa.infra.mibpm.common.model.ModelMeta;
import com.mi.oa.infra.mibpm.common.model.ProcessItem;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ModelMetaPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ProcessItemPo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @author: qiuzhipeng
 * @Date: 2022/1/17 16:18
 */

@Mapper(componentModel = "spring")
public interface ModelMetaPoConverter {

    ModelMeta toModelMeta(ModelMetaPo modelMetaPo);


    List<ModelMeta> toModelMetas(List<ModelMetaPo> modelMetaPos);

    ModelMetaPo toModelMetaPo(ModelMeta modelMeta);

    ProcessItem toProcessItem(ProcessItemPo processItemPo);
}
