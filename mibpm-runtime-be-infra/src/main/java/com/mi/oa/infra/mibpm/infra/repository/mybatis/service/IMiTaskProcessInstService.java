package com.mi.oa.infra.mibpm.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.MiTaskProcessInstPo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/24
 * @Description
 */
public interface IMiTaskProcessInstService extends IService<MiTaskProcessInstPo> {
    /**
     * @param procInstId 流程实例ID
     * @param taskId 任务ID
     * @param taskDefKey 任务定义Key
     * @return List<MiTaskProcessInstPo>
     * <AUTHOR>
     * @date 2024/10/12
     * @description 据流程实例ID和任务ID或任务定义Key查找主表内容
     **/
    List<MiTaskProcessInstPo> findByProcInstIdAndTaskIdOrTaskDefKey(String procInstId, String taskId,
                                                                    String taskDefKey);
    /**
     * @param procInstId 流程实例ID
     * @return List<MiTaskProcessInstPo>
     * @description 据流程实例ID查询任务列表
     **/
    List<MiTaskProcessInstPo> findTaskByProcInstId(String procInstId);

    /**
     * @param po 包含更新信息的MiTaskProcessInstPo对象
     * @return Boolean
     * <AUTHOR>
     * @date 2024/10/12
     * @description 根据流程实例ID和任务ID更新主表
     **/
    Boolean updateByProcInstIdAndTaskId(MiTaskProcessInstPo po);

    /**
     * @param miTaskProcessInstPoList 包含更新信息的MiTaskProcessInstPo列表
     * @return Boolean
     * <AUTHOR>
     * @date 2024/10/12
     * @description 根据流程任务ID批量更新主表
     **/
    List<MiTaskProcessInstPo> findProcessInstPoByProcInstIdAndTaskId(List<MiTaskProcessInstPo> miTaskProcessInstPoList);

    /**
     * @param procInstId 流程实例ID
     * @param taskId 任务ID
     * @param processInstanceStatus 新的流程实例状态
     * @return Boolean
     * <AUTHOR>
     * @date 2024/10/12
     * @description 根据流程实例ID和任务ID更新主表流程状态
     **/
    Boolean updateMiTaskInstStatus(String procInstId, String taskId, ProcessInstanceStatus processInstanceStatus);

    /**
     * @param procInstId 流程实例ID
     * @param taskId 任务ID
     * @return Boolean
     * <AUTHOR>
     * @date 2024/10/12
     * @description 根据流程实例ID和任务ID删除主表记录
     **/
    Boolean deleteByProcInstIdAndTaskId(String procInstId, String taskId);


    /**
     * @param procInstId 流程实例ID
     * @return Boolean
     * <AUTHOR>
     * @date 2024/10/12
     * @description 根据流程实例ID删除主表记录
     **/
    Boolean deleteByProcInstId(String procInstId);

    /**
     * @param procInstId 流程实例ID
     * @param isStartTask 是否为开始任务的标志
     * @return 流程实例的开始任务
     * <AUTHOR>
     * @date 2024/10/15
     * @description 根据标志查找流程实例的开始任务
     */
    MiTaskProcessInstPo findProcInstStartTaskByFlag(String procInstId, boolean isStartTask);

    /**
     * @param procInstId 流程实例ID
     * @return 流程实例的开始任务
     * <AUTHOR>
     * @date 2024/11/19
     * @description 查找流程维度信息
     */
    MiTaskProcessInstPo findProcInstInfo(String procInstId);

    /**
     * @param procInstIdList 流程实例ID列表
     * @param isStartTask 是否为开始任务的标志
     * @return 流程实例的开始任务
     * <AUTHOR>
     * @date 2024/10/17
     * @description 批量根据标志查找流程实例的开始任务
     */
    List<MiTaskProcessInstPo> findProcInstStartTaskByFlagBatch(List<String> procInstIdList, boolean isStartTask);

    /**
     * @param taskId 流程实例ID
     * @return 流程实例
     * <AUTHOR>
     * @date 2024/10/16
     * @description 根据任务Id查找任务
     */
    MiTaskProcessInstPo findProcInstPoByTaskId(String taskId);
}
