package com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ExtRuTaskInstPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface ExtRuTaskInstMapper extends BaseMapper<ExtRuTaskInstPo> {

    @Select("SELECT * FROM act_ext_ru_task WHERE task_key_ = #{taskKey}")
    List<ExtRuTaskInstPo> selectByTaskKey(@Param("taskKey") String taskKey);

}
