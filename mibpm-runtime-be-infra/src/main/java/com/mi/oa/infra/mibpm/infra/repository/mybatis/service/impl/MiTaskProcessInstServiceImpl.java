package com.mi.oa.infra.mibpm.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.MiTaskProcessInstMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.MiTaskProcessInstPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.service.IMiTaskProcessInstService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/24
 * @Description
 */
@Service
public class MiTaskProcessInstServiceImpl extends ServiceImpl<MiTaskProcessInstMapper, MiTaskProcessInstPo>
        implements IMiTaskProcessInstService {
    @Override
    public List<MiTaskProcessInstPo> findByProcInstIdAndTaskIdOrTaskDefKey(String procInstId, String taskId,
                                                                           String taskDefKey) {
        return lambdaQuery().eq(StringUtils.isNotBlank(procInstId), MiTaskProcessInstPo::getProcInstId, procInstId)
                .eq(StringUtils.isNotBlank(taskId), MiTaskProcessInstPo::getTaskId, taskId)
                .eq(StringUtils.isNotBlank(taskDefKey), MiTaskProcessInstPo::getTaskDefKey, taskDefKey)
                .list();
    }

    @Override
    public List<MiTaskProcessInstPo> findTaskByProcInstId(String procInstId) {
        return lambdaQuery().eq(MiTaskProcessInstPo::getProcInstId, procInstId)
                .ne(MiTaskProcessInstPo::getTaskId, "")
                .list();
    }

    @Override
    public MiTaskProcessInstPo findProcInstStartTaskByFlag(String procInstId, boolean isStartTask) {
        if (StringUtils.isBlank(procInstId)) {
            return null;
        }
        return lambdaQuery().eq(StringUtils.isNotBlank(procInstId), MiTaskProcessInstPo::getProcInstId, procInstId)
                .eq(isStartTask, MiTaskProcessInstPo::getIsStartTask, 1)
                .one();
    }

    @Override
    public MiTaskProcessInstPo findProcInstInfo(String procInstId) {
        if (StringUtils.isBlank(procInstId)) {
            return null;
        }
        return lambdaQuery().eq(StringUtils.isNotBlank(procInstId), MiTaskProcessInstPo::getProcInstId, procInstId)
                .in(StringUtils.isNotBlank(procInstId), MiTaskProcessInstPo::getTaskId, Arrays.asList("", null))
                .one();
    }

    @Override
    public List<MiTaskProcessInstPo> findProcInstStartTaskByFlagBatch(List<String> procInstIdList,
                                                                      boolean isStartTask) {
        return lambdaQuery().in(!CollectionUtils.isEmpty(procInstIdList), MiTaskProcessInstPo::getProcInstId,
                procInstIdList).eq(isStartTask, MiTaskProcessInstPo::getIsStartTask, 1).list();
    }

    @Override
    public MiTaskProcessInstPo findProcInstPoByTaskId(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            return null;
        }
        return lambdaQuery().eq(StringUtils.isNotBlank(taskId), MiTaskProcessInstPo::getTaskId, taskId).one();
    }

    @Override
    public Boolean updateByProcInstIdAndTaskId(MiTaskProcessInstPo miTaskProcessInstPo) {
        return update(miTaskProcessInstPo, new LambdaQueryWrapper<MiTaskProcessInstPo>()
                .eq(MiTaskProcessInstPo::getProcInstId, miTaskProcessInstPo.getProcInstId())
                .eq(MiTaskProcessInstPo::getTaskId, miTaskProcessInstPo.getTaskId()));
    }

    @Override
    public List<MiTaskProcessInstPo> findProcessInstPoByProcInstIdAndTaskId(List<MiTaskProcessInstPo> miTaskProcessInstPoList) {
        // 创建一个 QueryWrapper 实例
        LambdaQueryWrapper<MiTaskProcessInstPo> queryWrapper = new LambdaQueryWrapper<MiTaskProcessInstPo>();
        // 构建查询条件
        for (MiTaskProcessInstPo po : miTaskProcessInstPoList) {
            queryWrapper.or(w -> w.eq(MiTaskProcessInstPo::getProcInstId, po.getProcInstId()).eq(MiTaskProcessInstPo::getTaskId, po.getTaskId()));
        }
        List<MiTaskProcessInstPo> dbRecords = baseMapper.selectList(queryWrapper);
        // 执行查询并返回结果
        return dbRecords;
    }

    @Override
    public Boolean updateMiTaskInstStatus(String procInstId, String taskId,
                                          ProcessInstanceStatus processInstanceStatus) {
        // 创建一个LambdaUpdateWrapper对象
        LambdaUpdateWrapper<MiTaskProcessInstPo> updateWrapper = new LambdaUpdateWrapper<>();
        // 设置更新条件
        updateWrapper.eq(MiTaskProcessInstPo::getProcInstId, procInstId)
                .eq(MiTaskProcessInstPo::getTaskId, taskId)
                .set(MiTaskProcessInstPo::getProcessInstanceStatus, processInstanceStatus)
                .set(MiTaskProcessInstPo::getUpdateTime, ZonedDateTime.now().toInstant().toEpochMilli());
        // 执行更新操作
        return update(updateWrapper);
    }

    @Override
    public Boolean deleteByProcInstIdAndTaskId(String procInstId, String taskId) {
        if (procInstId == null || taskId == null) {
            return false;
        }
        return remove(new LambdaQueryWrapper<MiTaskProcessInstPo>()
                .eq(MiTaskProcessInstPo::getProcInstId, procInstId)
                .eq(MiTaskProcessInstPo::getTaskId, taskId));
    }

    @Override
    public Boolean deleteByProcInstId(String procInstId) {
        if (procInstId == null) {
            return false;
        }
        return remove(new LambdaQueryWrapper<MiTaskProcessInstPo>()
                .eq(MiTaskProcessInstPo::getProcInstId, procInstId));
    }

    public Boolean saveOrUpdateByProcInstIdAndTaskId(MiTaskProcessInstPo miTaskProcessInstPo) {
        return saveOrUpdate(miTaskProcessInstPo, new LambdaQueryWrapper<MiTaskProcessInstPo>()
                .eq(MiTaskProcessInstPo::getProcInstId, miTaskProcessInstPo.getProcInstId())
                .eq(MiTaskProcessInstPo::getTaskId, miTaskProcessInstPo.getTaskId()));
    }
}
