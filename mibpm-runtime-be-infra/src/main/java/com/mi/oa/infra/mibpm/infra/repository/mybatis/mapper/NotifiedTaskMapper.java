package com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.NotifiedTaskPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/28 3:12 PM
 **/
@Mapper
@Component
public interface NotifiedTaskMapper extends BaseMapper<NotifiedTaskPo> {

    @Select("select n.ID_           id, " +
            "       n.PROCESS_KEY_  processKey, " +
            "       n.TASK_DEF_KEY_ taskDefinitionKey, " +
            "       n.PROC_INST_ID_ processInstanceId, " +
            "       n.ASSIGNEE_     assignee, " +
            "       n.REVIEWED_     reviewed, " +
            "       n.CREATE_TIME_  createTime, " +
            "       n.UPDATE_TIME_  updateTime " +
            "from act_hi_notified_task n " +
            "         inner join act_hi_procinst p on p.ID_ = n.PROC_INST_ID_ " +
            " ${ew.customSqlSegment}")
    Page<NotifiedTaskPo> getPage(Page<NotifiedTaskPo> iPage, @Param(Constants.WRAPPER) Wrapper wrapper);

    @Select("select count(*)" +
            "from act_hi_notified_task n " +
            "         inner join act_hi_procinst p on p.ID_ = n.PROC_INST_ID_ " +
            " ${ew.customSqlSegment}")
    Long getCount(@Param(Constants.WRAPPER) Wrapper wrapper);

    @Select("select n.ID_           id, " +
            "       n.PROCESS_KEY_  processKey, " +
            "       n.TASK_DEF_KEY_ taskDefinitionKey, " +
            "       n.PROC_INST_ID_ processInstanceId, " +
            "       n.ASSIGNEE_     assignee, " +
            "       n.REVIEWED_     reviewed, " +
            "       n.CREATE_TIME_  createTime, " +
            "       n.UPDATE_TIME_  updateTime " +
            "from act_hi_notified_task n " +
            "         inner join act_hi_procinst p on p.ID_ = n.PROC_INST_ID_ " +
            " left join ACT_HI_VARINST v on n.PROC_INST_ID_ = v.PROC_INST_ID_ and v.TASK_ID_ is null and v.NAME_='status'" +
            " ${ew.customSqlSegment}")
    Page<NotifiedTaskPo> getPageWithVariable(Page<NotifiedTaskPo> iPage, @Param(Constants.WRAPPER) Wrapper wrapper);

    @Select("select count(distinct n.ID_)" +
            "from act_hi_notified_task n " +
            "         inner join act_hi_procinst p on p.ID_ = n.PROC_INST_ID_ " +
            " left join ACT_HI_VARINST v on n.PROC_INST_ID_ = v.PROC_INST_ID_ and v.TASK_ID_ is null and v.NAME_='status'" +
            " ${ew.customSqlSegment}")
    Long getCountWithVariable(@Param(Constants.WRAPPER) Wrapper wrapper);

}
