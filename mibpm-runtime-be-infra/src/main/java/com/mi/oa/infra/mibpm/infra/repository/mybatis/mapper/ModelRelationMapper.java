package com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ModelRelationPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/17 20:33
 */
@Mapper
public interface ModelRelationMapper extends BaseMapper<ModelRelationPo> {

    @Select("select id,parent_model_id,model_id,relation_type type from act_de_model_relation where parent_model_id = #{parentModelId} and relation_type=#{type}")
    List<ModelRelationPo> findByParentModelIdAndType(@Param("parentModelId") String parentModelId, @Param("type") String type);

    @Select("select id,parent_model_id,model_id,relation_type type from act_de_model_relation where parent_model_id = #{parentModelId}")
    List<ModelRelationPo> findByParentModelId(String parentModelId);

}

