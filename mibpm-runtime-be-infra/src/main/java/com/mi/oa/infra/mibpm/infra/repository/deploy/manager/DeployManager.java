package com.mi.oa.infra.mibpm.infra.repository.deploy.manager;

import com.mi.oa.infra.mibpm.common.enums.ModelType;
import com.mi.oa.infra.mibpm.infra.models.entity.DeployContext;

/**
 * <AUTHOR>
 * @date 2023/8/22 15:39
 */
public interface DeployManager {

    ModelType support();

    /**
     * Verify before deployment
     *
     * @param deployContext
     */
    void verify(DeployContext deployContext);

    /**
     * Execute before deployment.
     *
     * @param deployContext
     */
    void postProcessBeforeDeploy(DeployContext deployContext);

    /**
     * deploy
     *
     * @param deployContext
     */
    void deploy(DeployContext deployContext);

    /**
     * Execute after deployment.
     *
     * @param deployContext
     */
    void postProcessAfterDeploy(DeployContext deployContext);
}
