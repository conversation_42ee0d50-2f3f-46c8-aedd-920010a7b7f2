package com.mi.oa.infra.mibpm.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mi.oa.infra.mibpm.domain.task.model.HiTaskInstanceModelCode;
import com.mi.oa.infra.mibpm.infra.category.entity.ProcDefCountPo;
import com.mi.oa.infra.mibpm.infra.category.repository.CascadeCategoryRepository;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.MiTaskProcessInstMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.NotifiedTaskMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.ProcInstQueryMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.MiTaskProcessInstPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.NotifiedTaskPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ProcInstQueryPo;
import com.mi.oa.infra.mibpm.infra.task.repository.HiTaskModelCodeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/25 2:30 PM
 **/
@Service
public class CascadeCategoryRepositoryImpl implements CascadeCategoryRepository {
    @Autowired
    private MiTaskProcessInstMapper miTaskProcessInstMapper;
    @Autowired
    private NotifiedTaskMapper notifiedTaskMapper;
    @Autowired
    private ProcInstQueryMapper procInstQueryMapper;
    @Autowired
    private HiTaskModelCodeRepository hiTaskModelCodeRepository;

    @Override
    public List<ProcDefCountPo> todoTaskCategoryPo(String userId) {
        QueryWrapper<MiTaskProcessInstPo> queryWrapper = new QueryWrapper<MiTaskProcessInstPo>()
                .select("model_code as modelCode",
                        "count(*) as count")
                .eq("assignee", userId)
                .eq("end_time", 0)
                .groupBy("modelCode");
        List<Map<String, Object>> maps = miTaskProcessInstMapper.selectMaps(queryWrapper);
        return maps.stream().map(this::modelCodeMap).collect(Collectors.toList());
    }

    @Override
    public List<ProcDefCountPo> reviewedTaskCategoryPo(String userId) {
        List<HiTaskInstanceModelCode> list = hiTaskModelCodeRepository.listByUser(userId);
        List<HashMap<String, Object>> collect = list.stream().map(i -> {
            HashMap<String, Object> map = new HashMap<>();
            map.put("modelCode", i.getModelCode());
            map.put("count", 0L);
            return map;
        }).collect(Collectors.toList());
        return collect.stream().map(this::modelCodeMap).collect(Collectors.toList());
    }

    @Override
    public List<ProcDefCountPo> ccTaskCategoryPo(String userId, String userName) {
        QueryWrapper<NotifiedTaskPo> queryWrapper = new QueryWrapper<NotifiedTaskPo>().select(
                        "distinct PROCESS_KEY_  as modelCode")
                .in("ASSIGNEE_", userId, userName);
        List<Map<String, Object>> maps = notifiedTaskMapper.selectMaps(queryWrapper);
        return maps.stream().map(this::modelCodeMap).collect(Collectors.toList());
    }

    @Override
    public List<ProcDefCountPo> submitProcInstCategoryPo(String userId, String userName) {
        QueryWrapper<ProcInstQueryPo> queryWrapper = new QueryWrapper<ProcInstQueryPo>().select(
                        "distinct PROC_DEF_ID_ as procDefId")
                .in("START_USER_ID_", userId, userName);
        List<Map<String, Object>> maps = procInstQueryMapper.selectMaps(queryWrapper);

        return maps.stream().map(this::proDefMap).collect(Collectors.toList());
    }

    private ProcDefCountPo modelCodeMap(Map<String, Object> map) {
        String modelCode = (String) map.getOrDefault("modelCode", "");
        return new ProcDefCountPo(modelCode, (Long) map.getOrDefault("count", 0L));
    }

    private ProcDefCountPo proDefMap(Map<String, Object> map) {
        String procDefId = (String) map.getOrDefault("procDefId", "");
        String modelCode = procDefId.split(":")[0];
        return new ProcDefCountPo(modelCode, 0L);
    }
}
