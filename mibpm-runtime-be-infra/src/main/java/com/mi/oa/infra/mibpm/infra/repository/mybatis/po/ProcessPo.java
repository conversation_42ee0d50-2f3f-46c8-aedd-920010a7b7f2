package com.mi.oa.infra.mibpm.infra.repository.mybatis.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.mi.oa.infra.mibpm.domain.process.model.VisibleConfig;
import com.mi.oa.infra.oaucf.mybatis.common.BasePO;
import lombok.Data;

import java.util.List;

@Data
@TableName(value = "act_de_model_meta", autoResultMap = true)
public class ProcessPo extends BasePO<ProcessPo> {

    private String name;
    private String enName;
    private String categoryCode;
    private String appCode;
    private Boolean isWebEnabled;
    private Boolean isBusinessEnabled;
    private Boolean isAppEnabled;
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> owners;
    @TableField(typeHandler = JacksonTypeHandler.class)
    private VisibleConfig visibleConfig;
    private Integer sort;
    private Integer modelStatus;
    private Integer modelEnableStatus;
    private String modelCode;
    private String modelId;
    private Integer modelType;
    private Integer fromOld;
    private String description;
    private String descriptionEn;
    private Integer version;
    private Integer migrationProcDefVersion;
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> businessOwner;
    private String prodAppCode;
    protected String grade;
    protected String scope;
    protected Boolean releaseToProd;
    private String ownerDept;
}
