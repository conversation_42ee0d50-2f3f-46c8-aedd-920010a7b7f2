package com.mi.oa.infra.mibpm.infra.repository.mybatis.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;


/**
 *
 * <p>
 * 流程可见人员配置
 * </p>
 *
 * @author: qiuzhipeng
 * @Date: 2021/12/13 18:00
 */
@Data
@Accessors(chain = true)
@TableName(value = "mi_erp_process_view_auth", autoResultMap = true)
public class ProcessViewAuthPo implements Serializable {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 流程实例ID
     */
    private String procInstId;

    /**
     * 任务定义key
     */
    private String taskKey;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;
}
