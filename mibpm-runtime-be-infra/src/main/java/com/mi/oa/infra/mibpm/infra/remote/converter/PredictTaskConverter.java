package com.mi.oa.infra.mibpm.infra.remote.converter;

import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.infra.remote.entity.PredictTaskDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/1 15:29
 **/
@Mapper(componentModel = "spring")
public interface PredictTaskConverter {

    @Mappings(value = {
            @Mapping(target = "isPredict", constant = "true"),
            @Mapping(target = "taskDefinitionKey", source = "taskDefKey"),
    })
    TaskDo dtoToDo(PredictTaskDto dto);

    List<TaskDo> dtoToDo(List<PredictTaskDto> dto);
}
