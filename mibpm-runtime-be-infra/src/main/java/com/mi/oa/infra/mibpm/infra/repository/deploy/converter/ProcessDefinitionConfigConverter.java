package com.mi.oa.infra.mibpm.infra.repository.deploy.converter;

import com.mi.oa.infra.mibpm.flowable.extension.model.AssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.ReceiveTaskWrapper;
import com.mi.oa.infra.mibpm.flowable.extension.model.ServiceTaskWrapper;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskWrapper;
import com.mi.oa.infra.mibpm.infra.models.entity.ProcDefAssignRule;
import com.mi.oa.infra.mibpm.infra.models.entity.ProcDefNode;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.flowable.bpmn.model.FlowNode;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/12 19:21
 **/
@Mapper(componentModel = "spring")
public interface ProcessDefinitionConfigConverter {

    @Mapping(target = "nodeId", source = "wrapper.userTask.id")
    @Mapping(target = "nodeName", source = "wrapper.userTask.name")
    @Mapping(target = "nodeDomain", source = "wrapper.domain")
    @Mapping(target = "nodeType", expression = "java(wrapper.getUserTask().getClass().getSimpleName())")
    @Mapping(target = "createTime", expression = "java(java.time.ZonedDateTime.now())")
    @Mapping(ignore = true, target = "id")
    ProcDefNode taskToNode(UserTaskWrapper wrapper, String procDefId, Integer version);

    @Mapping(target = "nodeId", source = "wrapper.receiveTask.id")
    @Mapping(target = "nodeName", source = "wrapper.receiveTask.name")
    @Mapping(target = "nodeType", expression = "java(wrapper.getReceiveTask().getClass().getSimpleName())")
    @Mapping(target = "createTime", expression = "java(java.time.ZonedDateTime.now())")
    @Mapping(ignore = true, target = "id")
    ProcDefNode receiveToNode(ReceiveTaskWrapper wrapper, String procDefId, Integer version);

    @Mapping(target = "nodeId", source = "wrapper.serviceTask.id")
    @Mapping(target = "nodeName", source = "wrapper.serviceTask.name")
    @Mapping(target = "nodeType", expression = "java(wrapper.getServiceTask().getClass().getSimpleName())")
    @Mapping(target = "createTime", expression = "java(java.time.ZonedDateTime.now())")
    @Mapping(ignore = true, target = "id")
    ProcDefNode serviceToNode(ServiceTaskWrapper wrapper, String procDefId, Integer version);

    @Mapping(target = "nodeId", source = "flowNode.id")
    @Mapping(ignore = true, target = "id")
    @Mapping(target = "nodeName", source = "flowNode.name")
    @Mapping(target = "nodeType", expression = "java(flowNode.getClass().getSimpleName())")
    @Mapping(target = "createTime", expression = "java(java.time.ZonedDateTime.now())")
    ProcDefNode toNode(FlowNode flowNode, String procDefId, Integer version);

    default List<ProcDefAssignRule> toRules(List<AssigneeRule> assigneeRules, int ruleType, String procDefId,
                                            String nodeId) {
        List<ProcDefAssignRule> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(assigneeRules)) {
            return result;
        }
        for (AssigneeRule assigneeRule : assigneeRules) {
            result.add(ProcDefAssignRule.builder().procDefId(procDefId).nodeId(nodeId).ruleType(String.valueOf(ruleType))
                    .assignType(assigneeRule.type().getCode())
                    .ruleJson(GsonUtils.toJsonWtihNullField(assigneeRule))
                    .build());
        }
        return result;
    }

}
