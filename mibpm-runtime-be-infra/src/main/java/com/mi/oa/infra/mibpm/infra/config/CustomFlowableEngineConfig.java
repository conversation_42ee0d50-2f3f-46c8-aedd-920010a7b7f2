package com.mi.oa.infra.mibpm.infra.config;

import com.mi.oa.infra.mibpm.flowable.extension.BpmnExtensionHelper;
import com.mi.oa.infra.mibpm.infra.flowable.assign.AssigneeFactory;
import com.mi.oa.infra.mibpm.infra.flowable.assign.BpmAssigneeService;
import com.mi.oa.infra.mibpm.infra.flowable.behavior.CustomActivityBehaviorFactory;
import com.mi.oa.infra.mibpm.infra.flowable.bpmn.ProcessLocalizationManager;
import com.mi.oa.infra.mibpm.infra.flowable.delegate.ServiceCallDelegate;
import com.mi.oa.infra.mibpm.infra.flowable.el.GroovyExpressionManager;
import com.mi.oa.infra.mibpm.infra.flowable.interceptor.CustomCreateUserTaskInterceptor;
import org.activiti.compatibility.spring.SpringFlowable5CompatibilityHandlerFactory;
import org.flowable.common.engine.api.delegate.event.FlowableEventListener;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.flowable.spring.boot.EngineConfigurationConfigurer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 自定义flowable engine配置
 *
 * <AUTHOR>
 * @description
 * @date 2022/3/1 3:26 PM
 **/
@Configuration
public class CustomFlowableEngineConfig implements EngineConfigurationConfigurer<SpringProcessEngineConfiguration> {

    private final CustomActivityBehaviorFactory customActivityBehaviorFactory;
    private final ServiceCallDelegate serviceCallDelegate;
    private final BpmAssigneeService bpmAssigneeService;
    private final List<FlowableEventListener> eventListeners;
    private final BpmnExtensionHelper bpmnExtensionHelper;
    private final AssigneeFactory assigneeFactory;
    private final ApplicationContext applicationContext;

    @Autowired
    public CustomFlowableEngineConfig(CustomActivityBehaviorFactory customActivityBehaviorFactory,
            ServiceCallDelegate serviceCallDelegate,
            BpmAssigneeService bpmAssigneeService,
            List<FlowableEventListener> eventListeners,
            BpmnExtensionHelper bpmnExtensionHelper,
            AssigneeFactory assigneeFactory,
            ApplicationContext applicationContext) {
        this.customActivityBehaviorFactory = customActivityBehaviorFactory;
        this.serviceCallDelegate = serviceCallDelegate;
        this.bpmAssigneeService = bpmAssigneeService;
        this.eventListeners = eventListeners;
        this.bpmnExtensionHelper = bpmnExtensionHelper;
        this.assigneeFactory = assigneeFactory;
        this.applicationContext = applicationContext;
    }

    @ConditionalOnBean(value = {
            SpringProcessEngineConfiguration.class
    })
    @Override
    public void configure(SpringProcessEngineConfiguration springProcessEngineConfiguration) {
        // 注册自定义服务
        Map<Object, Object> beansMap = new HashMap<>();
        beansMap.put("bpmService", bpmAssigneeService);
        beansMap.put("serviceCallDelegate", serviceCallDelegate);
        beansMap.put("assigneeFactory", assigneeFactory);
        springProcessEngineConfiguration.setBeans(beansMap);

        // 注册自定义活动行为解析器
        springProcessEngineConfiguration.setActivityBehaviorFactory(customActivityBehaviorFactory);

        // 设置全局事件监听
        springProcessEngineConfiguration.setEventListeners(eventListeners);

        // 兼容v5版本的流程
        springProcessEngineConfiguration.setFlowable5CompatibilityEnabled(true);
        springProcessEngineConfiguration
                .setFlowable5CompatibilityHandlerFactory(new SpringFlowable5CompatibilityHandlerFactory());

        // 关闭liquibase
        springProcessEngineConfiguration.setDatabaseSchemaUpdate("none");
        // 流程定义缓存上限
        springProcessEngineConfiguration.setProcessDefinitionCacheLimit(400);
        // 设置流程实例国际化管理器
        springProcessEngineConfiguration.setInternalProcessLocalizationManager(new ProcessLocalizationManager());

        springProcessEngineConfiguration
                .setCreateUserTaskInterceptor(new CustomCreateUserTaskInterceptor(bpmnExtensionHelper));
        // 注册Groovy表达式解析器
        GroovyExpressionManager groovyExpressionManager = new GroovyExpressionManager(
                applicationContext,
                springProcessEngineConfiguration.getBeans());

        springProcessEngineConfiguration.setExpressionManager(groovyExpressionManager);

    }

}
