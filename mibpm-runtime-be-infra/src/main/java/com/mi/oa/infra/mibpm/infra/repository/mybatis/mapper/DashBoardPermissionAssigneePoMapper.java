package com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.DashBoardPermissionAssigneePo;
import org.apache.ibatis.annotations.Mapper;

/**
 * @FileName DashBoardPermissionAssigneePoMapper
 * @Description
 * <AUTHOR>
 * @date 2024-11-29
 **/
@Mapper
public interface DashBoardPermissionAssigneePoMapper extends BaseMapper<DashBoardPermissionAssigneePo> {
}
