package com.mi.oa.infra.mibpm.infra.flowable.sign;

import com.mi.oa.infra.mibpm.common.constant.BpmVariablesConstants;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.flowable.extension.model.SignAddTypeEnum;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.runtime.Execution;
import org.flowable.task.api.Task;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/24 16:08
 **/
public abstract class SignAfterStrategy extends SignStrategy {

    SignAfterStrategy(RuntimeService runtimeService, TaskService taskService) {
        super(runtimeService, taskService);
    }

    @Override
    SignAddTypeEnum getSignAddTypeEnum() {
        return SignAddTypeEnum.SIGN_AFTER;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addSign(TaskDo taskDo, List<BpmUser> assignees) {
        List<BpmUser> users = assignees.stream().distinct().collect(Collectors.toList());
        List<Execution> subExecutions = new ArrayList<>();
        for (BpmUser user : users) {
            subExecutions.add(addExecution(taskDo, user, SignAddTypeEnum.SIGN_AFTER));
        }
        Map<String, Object> variables = new HashMap<>();
        List<Task> list = taskService.createTaskQuery().taskDefinitionKey(taskDo.getTaskDefinitionKey())
                .processInstanceId(taskDo.getProcessInstanceId()).list();
        Set<String> executionIds = subExecutions.stream().map(Execution::getId).collect(Collectors.toSet());
        List<String> taskIds = list.stream().filter(t -> executionIds.contains(t.getExecutionId())).map(t -> t.getId())
                .collect(Collectors.toList());
        variables.put(BpmVariablesConstants.VARIABLE_SIGN_TYPE, SignAddTypeEnum.SIGN_AFTER);
        variables.put(BpmVariablesConstants.VARIABLE_SIGN_FLAG, true);
        variables.put(BpmVariablesConstants.VARIABLE_SIGN_SUB_TASK, taskIds);
        taskService.complete(taskDo.getTaskId(), variables, true);
    }
}
