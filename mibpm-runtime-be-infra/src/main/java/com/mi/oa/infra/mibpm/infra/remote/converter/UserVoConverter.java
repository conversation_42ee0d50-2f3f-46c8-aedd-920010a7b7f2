package com.mi.oa.infra.mibpm.infra.remote.converter;

import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.organization.rep.UserVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/15 14:44
 */
@Mapper(componentModel = "spring")
public interface UserVoConverter {

    @Mappings({
            @Mapping(source = "userId", target = "userName"),
            @Mapping(source = "userId", target = "uid")
    })
    BpmUser userVoToBpmUser(UserVO userVO);

    List<BpmUser> userVosToBpmUsers(List<UserVO> userVOList);
}
