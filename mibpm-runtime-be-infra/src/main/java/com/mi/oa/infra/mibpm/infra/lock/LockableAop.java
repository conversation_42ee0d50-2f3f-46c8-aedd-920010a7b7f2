package com.mi.oa.infra.mibpm.infra.lock;

import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.SimpleEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.locks.LockSupport;

@Component
@Aspect
@Slf4j
@Order(1)
public class LockableAop {

  // 默认锁Value
    public static final String AUTHORIZE_DEFAULT_VALUE = "AUTHORIZE:::VALUE";

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Around(value = "@annotation(com.mi.oa.infra.mibpm.infra.lock.Lockable)")
    @SneakyThrows
    public Object around(ProceedingJoinPoint point) {
        Class<?> aClass = point.getTarget().getClass();
        MethodSignature methodSignature = (MethodSignature) point.getSignature();
        Method method = aClass.getDeclaredMethod(methodSignature.getName(), methodSignature.getParameterTypes());

        Map<String, Object> paramMap = this.parseMethodParams(methodSignature, point.getArgs());

        Lockable annotation = AnnotationUtils.getAnnotation(method, Lockable.class);

        if (Objects.isNull(annotation)) {
            return point.proceed();
        }

        String key = parseExpress(paramMap, annotation.lockName());

        int retryCount = annotation.retryCount();

        Boolean isSuccess;

        isSuccess = redisTemplate.opsForValue()
                .setIfAbsent(key, AUTHORIZE_DEFAULT_VALUE, annotation.time(), annotation.unit());

        if (Boolean.FALSE.equals(isSuccess) && retryCount >= 1) {
            do {
                LockSupport.parkNanos(annotation.retryUnit().toNanos(annotation.retryTime()));

                isSuccess = redisTemplate.opsForValue()
                        .setIfAbsent(key, AUTHORIZE_DEFAULT_VALUE, annotation.time(), annotation.unit());
                if (Boolean.FALSE.equals(isSuccess)) {
                    retryCount--;
                } else {
                    break;
                }
            } while (retryCount > 0);
        }

        if (Boolean.FALSE.equals(isSuccess)) {
            throw new RuntimeException("lock retry error");
        }

        try {
            return point.proceed();
        } finally {
            redisTemplate.delete(key);
        }
    }

    public String parseExpress(Map<String, Object> args, String key) {
        try {
            SpelExpressionParser spelExpressionParser = new SpelExpressionParser();
            Expression expression = spelExpressionParser.parseExpression(key);
            SimpleEvaluationContext context = SimpleEvaluationContext.forReadOnlyDataBinding().build();
            args.forEach(context::setVariable);

            return expression.getValue(context, String.class);
        } catch (Exception e) {
            return "";
        }
    }

    @SneakyThrows
    public Map<String, Object> parseMethodParams(MethodSignature methodSignature, Object[] args) {

        String[] paramNames = methodSignature.getParameterNames();
        Map<String, Object> paramMap = Maps.newHashMap();
        for (int i = 0; i < paramNames.length; i++) {
            paramMap.put(paramNames[i], args[i]);
        }

        return paramMap;
    }
}
