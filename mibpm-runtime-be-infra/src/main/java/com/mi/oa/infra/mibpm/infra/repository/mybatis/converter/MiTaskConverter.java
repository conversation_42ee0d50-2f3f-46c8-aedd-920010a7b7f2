package com.mi.oa.infra.mibpm.infra.repository.mybatis.converter;

import com.google.gson.Gson;
import com.mi.oa.infra.mibpm.common.enums.ClientEnum;
import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import com.mi.oa.infra.mibpm.common.enums.SourceEnum;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.domain.mitask.model.MiTaskDo;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskSignType;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.MiTaskProcessExtPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.MiTaskProcessInstPo;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/24
 * @Description
 */
@Mapper(componentModel = "spring", builder = @Builder(disableBuilder = true))
public interface MiTaskConverter {

    @Mapping(target = "extProperties", expression = "java(mapExtProperties(miTaskProcessExtPos))")
    @Mapping(target = "taskId", expression = "java(getTaskId(miTaskProcessInstPo))")
    MiTaskDo instPoAndExtPosToDo(MiTaskProcessInstPo miTaskProcessInstPo, List<MiTaskProcessExtPo> miTaskProcessExtPos);

    MiTaskDo instPoToDo(MiTaskProcessInstPo miTaskProcessInstPo);

    MiTaskProcessInstPo doToInstPo(MiTaskDo miTaskDo);

    List<MiTaskProcessInstPo> doListToInstPoList(List<MiTaskDo> miTaskDos);

    default List<MiTaskProcessExtPo> doToExtPos(List<MiTaskDo> miTaskDos) {
        List<MiTaskProcessExtPo> extPos = new ArrayList<>();
        for (MiTaskDo miTaskDo : miTaskDos) {
            extPos.addAll(doToExtPos(miTaskDo));
        }
        return extPos;
    }

    default List<MiTaskProcessExtPo> doToExtPos(MiTaskDo miTaskDo) {
        Map<String, Object> extProperties = miTaskDo.getExtProperties();
        Gson gson = new Gson();
        return extProperties.entrySet().stream()
                .filter(entry -> !entry.getKey().equals("tenantId")) // 由于BPM的tenantId没用到，但是是空串，所以过滤掉key为tenantId 的条目
                .map(entry -> {
                    String type;
                    String textValue = null;
                    Double doubleValue = null;
                    Long longValue = null;
                    if (entry.getValue() instanceof String) {
                        type = "text";
                        textValue = (String) entry.getValue();
                    } else if (entry.getValue() instanceof Double) {
                        type = "double";
                        doubleValue = (Double) entry.getValue();
                    } else if (entry.getValue() instanceof Long) {
                        type = "long";
                        longValue = (Long) entry.getValue();
                    } else if (entry.getValue() instanceof Boolean) {
                        type = "boolean";
                        textValue = entry.getValue().toString();
                    } else if (entry.getValue() instanceof List) { // List类型序列化
                        type = "list";
                        textValue = gson.toJson(entry.getValue());
                    } else if (entry.getValue() instanceof Enum) { // 枚举类型直接toString
                        type = "enum";
                        textValue = ((Enum<?>) entry.getValue()).name();
                    } else {  // 其他类型直接序列化
                        type = "object";
                        textValue = gson.toJson(entry.getValue());
                    }
                    return MiTaskProcessExtPo.builder()
                            .procInstId(miTaskDo.getProcInstId())
                            .taskId(miTaskDo.getTaskId())
                            .name(entry.getKey())
                            .type(type)
                            .textValue(textValue)
                            .doubleValue(doubleValue)
                            .longValue(longValue)
                            .createTime(zoneTimeToLong(miTaskDo.getCreateTime()))
                            .updateTime(zoneTimeToLong(miTaskDo.getUpdateTime()))
                            .build();
                })
                .collect(Collectors.toList());
    }

    default String getTaskId(MiTaskProcessInstPo miTaskProcessInstPo) {
        if (UserTaskSignType.COMPETITION.name().equals(miTaskProcessInstPo.getSignType())
                && StringUtils.isNotBlank(miTaskProcessInstPo.getParentTaskId())) {
            return miTaskProcessInstPo.getParentTaskId();
        }
        return miTaskProcessInstPo.getTaskId();
    }

    default Map<String, Object> mapExtProperties(List<MiTaskProcessExtPo> miTaskProcessExtPos) {
        // 根据procInstId、taskId和name去重，三者才能保证miTaskDo对应的唯一属性
        miTaskProcessExtPos = miTaskProcessExtPos.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(()
                        -> new TreeSet<>(Comparator.comparing(o -> o.getProcInstId() + ";" + o.getTaskId() + ";" + o.getName()))),
                ArrayList::new));
        return miTaskProcessExtPos.stream().collect(Collectors.toMap(
                MiTaskProcessExtPo::getName,
                extValue -> {
                    switch (extValue.getType()) {
                        case "double":
                            return extValue.getDoubleValue();
                        case "long":
                            return extValue.getLongValue();
                        default:
                            return extValue.getTextValue();
                    }
                }, (k1, k2) -> k1));
    }

    default Long zoneTimeToLong(ZonedDateTime zonedDateTime) {
        return (zonedDateTime == null || zonedDateTime.equals(ZonedDateTime.ofInstant(Instant.EPOCH, ZoneOffset.UTC)))
                ? null : zonedDateTime.toInstant().toEpochMilli();
    }

    default ZonedDateTime longToZoneTime(Long epochMilli) {
        return epochMilli == 0 ? null : ZonedDateTime.ofInstant(Instant.ofEpochMilli(epochMilli), ZoneOffset.UTC);
    }

    default String bpmUserToString(BpmUser bpmUser) {
        return bpmUser == null ? "" : bpmUser.getUserName();
    }

    default BpmUser stringToBpmUser(String bpmUserString) {
        return BpmUser.builder().userName(bpmUserString).build();
    }

    default UserTaskOperation convertStringToOperation(String value) {
        if (StringUtils.isBlank(value)) {
            return null; // 或者返回一个默认值
        }
        UserTaskOperation userTaskOperation = UserTaskOperation.findByCode(value.toLowerCase());
        if (userTaskOperation == null) {
            userTaskOperation = UserTaskOperation.AGREE;
        }
        return userTaskOperation;
    }

    default UserTaskSignType convertStringToSignType(String value) {
        if (StringUtils.isBlank(value)) {
            return null; // 或者返回一个默认值
        }
        UserTaskSignType userTaskSignType = UserTaskSignType.findByCode(value);
        if (userTaskSignType == null) {
            userTaskSignType = UserTaskSignType.valueOf(value);
        }
        return userTaskSignType;
    }

    default ProcessInstanceStatus convertStringToProcessInstanceStatus(String value) {
        if (StringUtils.isBlank(value)) {
            return null; // 或者返回一个默认值
        }
        return ProcessInstanceStatus.valueOf(value);
    }

    default ClientEnum convertStringToClient(String value) {
        if (StringUtils.isBlank(value)) {
            return null; // 或者返回一个默认值
        }
        ClientEnum clientEnum = ClientEnum.findByCode(value);
        if (clientEnum == null) {
            clientEnum = ClientEnum.valueOf(value);
        }
        return clientEnum;
    }

    default SourceEnum convertStringToSource(String value) {
        if (StringUtils.isBlank(value)) {
            return null; // 或者返回一个默认值
        }
        return SourceEnum.valueOf(value);
    }
}
