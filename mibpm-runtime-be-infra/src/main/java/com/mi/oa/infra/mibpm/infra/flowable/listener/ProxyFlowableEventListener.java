package com.mi.oa.infra.mibpm.infra.flowable.listener;

import com.google.common.collect.Lists;
import com.mi.oa.infra.mibpm.common.constant.BpmCommonConstants;
import com.mi.oa.infra.mibpm.common.constant.BpmVariablesConstants;
import com.mi.oa.infra.mibpm.common.enums.ActivityTypeEnum;
import com.mi.oa.infra.mibpm.common.enums.AutoOperationTypeEnum;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.ActivityCompletedEvent;
import com.mi.oa.infra.mibpm.common.event.ActivityStartedEvent;
import com.mi.oa.infra.mibpm.common.event.AssigneeChangeEvent;
import com.mi.oa.infra.mibpm.common.event.ProcInstEndEvent;
import com.mi.oa.infra.mibpm.common.event.ProcInstStartedEvent;
import com.mi.oa.infra.mibpm.common.event.ProcInstStartedEvent.ProcInstStartedEventBuilder;
import com.mi.oa.infra.mibpm.common.event.TaskAutoCompleteEvent;
import com.mi.oa.infra.mibpm.common.event.TaskCompletedEvent;
import com.mi.oa.infra.mibpm.common.event.TaskCreateEvent;
import com.mi.oa.infra.mibpm.common.event.TodoTaskCreateEvent;
import com.mi.oa.infra.mibpm.common.event.UserTaskDeletedEvent;
import com.mi.oa.infra.mibpm.common.event.UserTaskUpdateEvent;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.domain.mitask.factory.MiTaskDoFactory;
import com.mi.oa.infra.mibpm.domain.mitask.model.MiTaskDo;
import com.mi.oa.infra.mibpm.domain.mitask.service.MiTaskDomainService;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.domain.userconfig.service.UserConfigDomainService;
import com.mi.oa.infra.mibpm.eventbus.EventPublisher;
import com.mi.oa.infra.mibpm.infra.flowable.assign.ApproveStrategyService;
import com.mi.oa.infra.mibpm.infra.flowable.behavior.CustomMultiInstanceBehaviorHelper;
import com.mi.oa.infra.mibpm.infra.flowable.behavior.CustomParallelMultiInstanceBehavior;
import com.mi.oa.infra.mibpm.infra.flowable.behavior.CustomSequentialMultiInstanceBehavior;
import com.mi.oa.infra.mibpm.infra.procinst.remote.ProcessDefinitionRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.infra.repository.impl.HiTaskInsEntityMarkEndedCmd;
import com.mi.oa.infra.mibpm.infra.repository.impl.HistoricTaskServiceGetCmd;
import com.mi.oa.infra.mibpm.infra.task.repository.TaskRepository;
import com.mi.oa.infra.mibpm.utils.ApprovalContextSynchronizationManager;
import com.mi.oa.infra.mibpm.utils.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.flowable.common.engine.api.delegate.event.FlowableEngineEntityEvent;
import org.flowable.common.engine.api.delegate.event.FlowableEvent;
import org.flowable.common.engine.impl.cfg.TransactionState;
import org.flowable.engine.HistoryService;
import org.flowable.engine.ManagementService;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.event.AbstractFlowableEngineEventListener;
import org.flowable.engine.delegate.event.FlowableActivityEvent;
import org.flowable.engine.delegate.event.FlowableMultiInstanceActivityCompletedEvent;
import org.flowable.engine.delegate.event.FlowableMultiInstanceActivityEvent;
import org.flowable.engine.delegate.event.FlowableProcessStartedEvent;
import org.flowable.engine.delegate.event.impl.FlowableEntityEventImpl;
import org.flowable.engine.delegate.event.impl.FlowableProcessStartedEventImpl;
import org.flowable.engine.impl.persistence.entity.ExecutionEntityImpl;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.identitylink.api.IdentityLinkInfo;
import org.flowable.identitylink.api.IdentityLinkType;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.service.HistoricTaskService;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.flowable.task.service.impl.persistence.entity.TaskEntityImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * flowable event 事件代理
 *
 * @author: qiuzhipeng
 * @Date: 2022/2/24 20:41
 */

@Slf4j
public class ProxyFlowableEventListener extends AbstractFlowableEngineEventListener {

    private EventPublisher eventPublisher;

    @Autowired
    private MiTaskDomainService miTaskDomainService;

    @Autowired
    private MiTaskDoFactory miTaskDoFactory;

    public ProxyFlowableEventListener(EventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    @Override
    public void onEvent(FlowableEvent flowableEvent) {
        super.onEvent(flowableEvent);
    }

    @Override
    public boolean isFailOnException() {
        return super.isFailOnException();
    }

    @Override
    public boolean isFireOnTransactionLifecycleEvent() {
        return true;
    }

    @Override
    public String getOnTransaction() {
        //事务提交后触发
        return TransactionState.COMMITTED.name();
    }

    /**
     * 任务实例创建
     *
     * @param event
     */
    @Override
    protected void taskCreated(FlowableEngineEntityEvent event) {
        Object entity = event.getEntity();
        if (!(entity instanceof TaskEntityImpl)) {
            return;
        }
        boolean autoSkip = false;
        try {
            TaskEntityImpl taskEntity = (TaskEntityImpl) entity;
            ProcessDefinitionRemoteService processDefinitionRemoteService = SpringContextUtil.getBean(
                    ProcessDefinitionRemoteService.class);
            boolean isStartTask = processDefinitionRemoteService.listFirstNodeDef(taskEntity.getProcessDefinitionId())
                    .contains(taskEntity.getTaskDefinitionKey());

            // 处理自动跳过
            autoSkipOnRobot(entity, isStartTask);

            // 处理委托关系
            replaceAssigneeOnDelegation(entity, isStartTask);

            // 处理审批人重复
            autoSkip = skipOnAssigneeDuplicate(entity);
        } catch (Exception e) {
            log.error("任务创建监听器处理异常", e);
        }

        TaskEntityImpl taskEntity = (TaskEntityImpl) entity;
        List<String> candidates = loadTaskCandidates(taskEntity);

        // MiTask3.0对接
        try {
            // 构建并写入MiTaskDo
            MiTaskDo miTaskDo = miTaskDoFactory.buildMiTaskDoByTaskEvent(taskEntity, true, false);
            miTaskDomainService.createMiTask(miTaskDo);
            // 构建竞签任务
            if (!CollectionUtils.isEmpty(candidates)) {
                List<MiTaskDo> miTaskDos = miTaskDoFactory.buildCandidateMiTaskDos(miTaskDo, taskEntity, candidates);
                miTaskDomainService.createMiTaskBatch(miTaskDos);
            }
        } catch (Exception e) {
            log.error("MiTaskException taskCreated entity taskId:{}, prcInstId :{}", taskEntity.getId(),
                    taskEntity.getProcessInstanceId(), e);
            // 抛出异常：throw new InfraException(e, TaskInfraErrorCodeEnum.MITASK_CREATE_ERROR);
        }

        // 构建事件对象
        TaskCreateEvent taskCreateEvent = TaskCreateEvent.builder()
                .assignee(taskEntity.getAssignee())
                .candidates(candidates)
                .taskId(taskEntity.getId())
                .taskName(taskEntity.getName())
                .taskDefinitionKey(taskEntity.getTaskDefinitionKey())
                .processInstanceId(taskEntity.getProcessInstanceId())
                .processDefinitionId(taskEntity.getProcessDefinitionId())
                .formData(ApprovalContextSynchronizationManager.getFormData())
                // 自动跳过时不发送飞书消息
                .notSendLarkMessage(autoSkip)
                .build();

        // 设置领域事件基础信息
        taskCreateEvent.setId(taskEntity.getId());
        taskCreateEvent.setIdentifier(EventIdentify.TASK_CREATED.name());
        taskCreateEvent.setTimestamp(System.currentTimeMillis());
        // publish task create event
        log.info("发送任务创建事件，event = {}", taskCreateEvent);
        eventPublisher.publish(taskCreateEvent);

        TodoTaskCreateEvent todoTaskCreateEvent = new TodoTaskCreateEvent();
        BeanUtils.copyProperties(taskCreateEvent, todoTaskCreateEvent);
        todoTaskCreateEvent.setIdentifier(EventIdentify.TODO_TASK_CREATE.name());
        eventPublisher.publish(todoTaskCreateEvent);
    }

    /**
     * 任务完成
     *
     * @param event
     */
    @Override
    protected void taskCompleted(FlowableEngineEntityEvent event) {
        Object entity = event.getEntity();

        if (entity instanceof TaskEntityImpl) {
            TaskEntityImpl taskEntity = (TaskEntityImpl) entity;
            // 构建事件对象
            TaskCompletedEvent taskCompletedEvent = TaskCompletedEvent.builder()
                    .assignee(taskEntity.getAssignee())
                    .taskId(taskEntity.getId())
                    .taskName(taskEntity.getName())
                    .taskDefinitionKey(taskEntity.getTaskDefinitionKey())
                    .processInstanceId(taskEntity.getProcessInstanceId())
                    .processDefinitionId(taskEntity.getProcessDefinitionId())
                    .formData(ApprovalContextSynchronizationManager.getFormData())
                    .build();

            // 设置领域事件基础信息
            taskCompletedEvent.setId(taskEntity.getId());
            taskCompletedEvent.setIdentifier(EventIdentify.TASK_COMPLETED.name());
            taskCompletedEvent.setTimestamp(System.currentTimeMillis());
            // publish task completed event
            log.info("发送任务完成事件，event = {}", taskCompletedEvent);
            eventPublisher.publish(taskCompletedEvent);
        }
    }

    /**
     * 流程开始
     *
     * @param event
     */
    @Override
    protected void processStarted(FlowableProcessStartedEvent event) {

        if (event instanceof FlowableProcessStartedEventImpl) {
            FlowableProcessStartedEventImpl flowableProcessStartedEvent = (FlowableProcessStartedEventImpl) event;

            // MiTask3.0对接
            try {
                // 构建并更新MiTaskDo
                MiTaskDo miTaskDo = miTaskDoFactory.buildProcessMiTaskDoByProcessStartEvent(flowableProcessStartedEvent);
                miTaskDomainService.createMiTask(miTaskDo);
            } catch (Exception e) {
                log.error("MiTaskException processStarted entity procInstId:{}",
                        flowableProcessStartedEvent.getProcessInstanceId(), e);
                // 抛出异常：throw new InfraException(e, TaskInfraErrorCodeEnum.MITASK_CREATE_ERROR);
            }

            ProcInstStartedEventBuilder builder = ProcInstStartedEvent.builder();
            builder.processInstanceId(flowableProcessStartedEvent.getProcessInstanceId());
            builder.processDefId(flowableProcessStartedEvent.getProcessDefinitionId());
            // 获取提交节点任务信息
            if (event.getEntity() instanceof ExecutionEntityImpl) {
                ExecutionEntityImpl executionEntity = (ExecutionEntityImpl) event.getEntity();
                List<TaskEntity> tasks = executionEntity.getTasks();
                if (CollectionUtils.isNotEmpty(tasks)) {
                    TaskEntity taskEntity = tasks.get(0);
                    builder.startTaskId(taskEntity.getId());
                    builder.startTaskName(taskEntity.getName());
                    builder.startTaskDefinitionKey(taskEntity.getTaskDefinitionKey());
                }
            }
            ProcInstStartedEvent procInstStartedEvent = builder.build();

            // 设置领域事件基础信息
            procInstStartedEvent.setId(flowableProcessStartedEvent.getProcessInstanceId());
            procInstStartedEvent.setIdentifier(EventIdentify.PROCESS_STARTED.name());
            procInstStartedEvent.setTimestamp(System.currentTimeMillis());
            // publish process started event
            log.info("发送流程开始事件，event = {}", procInstStartedEvent);
            eventPublisher.publish(procInstStartedEvent);
        }
    }

    /**
     * 流程结束
     *
     * @param event
     */
    @Override
    protected void processCompleted(FlowableEngineEntityEvent event) {
        if (event instanceof FlowableEntityEventImpl) {
            FlowableEntityEventImpl flowableEntityEvent = (FlowableEntityEventImpl) event;

            // MiTask3.0对接
            try {
                // 更新流程下所有task
                List<MiTaskDo> miTaskDoList = miTaskDoFactory.buildMiTaskDoByProcessEvent(flowableEntityEvent);
                miTaskDomainService.updateMiTaskInstPoBatch(miTaskDoList);
            } catch (Exception e) {
                log.error("MiTaskException processCompleted event procInstId:{}", event.getProcessInstanceId(), e);
                // 抛出异常：throw new InfraException(e, TaskInfraErrorCodeEnum.MITASK_PROC_COMPLETED_UPTATE_ERROR);
            }

            ProcInstEndEvent procInstEndEvent = ProcInstEndEvent.builder()
                    .processInstanceId(flowableEntityEvent.getProcessInstanceId())
                    .processDefinitionId(flowableEntityEvent.getProcessDefinitionId())
                    .build();

            // 设置领域事件基础信息
            procInstEndEvent.setId(flowableEntityEvent.getProcessInstanceId());
            procInstEndEvent.setIdentifier(EventIdentify.PROCESS_COMPLETED.name());
            procInstEndEvent.setTimestamp(System.currentTimeMillis());
            // publish process completed event
            log.info("发送流程结束事件，event = {}", procInstEndEvent);
            eventPublisher.publish(procInstEndEvent);

        }
    }

    /**
     * 活动开始
     *
     * @param event
     */
    @Override
    protected void activityStarted(FlowableActivityEvent event) {

        if (isMultiInstanceBehavior(event)) {
            return;
        }
        // 插入历史表记录
        insertHisLogOnReceiveTask(event);

        ActivityStartedEvent activityStartedEvent = ActivityStartedEvent.builder()
                .taskName(event.getActivityName())
                .taskDefinitionKey(event.getActivityId())
                .processDefinitionId(event.getProcessDefinitionId())
                .processInstanceId(event.getProcessInstanceId())
                .activityType(event.getActivityType())
                .formData(ApprovalContextSynchronizationManager.getFormData())
                .build();

        activityStartedEvent.setId(UUID.randomUUID().toString());
        activityStartedEvent.setIdentifier(EventIdentify.ACTIVITY_STARTED.name());
        activityStartedEvent.setTimestamp(System.currentTimeMillis());

        eventPublisher.publish(activityStartedEvent);

        log.info("activity started");
    }

    /**
     * 多实例活动开始
     *
     * @param event
     */
    @Override
    protected void multiInstanceActivityStarted(FlowableMultiInstanceActivityEvent event) {

        ActivityStartedEvent activityStartedEvent = ActivityStartedEvent.builder()
                .taskName(event.getActivityName())
                .taskDefinitionKey(event.getActivityId())
                .processDefinitionId(event.getProcessDefinitionId())
                .processInstanceId(event.getProcessInstanceId())
                .activityType(event.getActivityType())
                .formData(ApprovalContextSynchronizationManager.getFormData())
                .build();

        activityStartedEvent.setId(UUID.randomUUID().toString());
        activityStartedEvent.setIdentifier(EventIdentify.ACTIVITY_STARTED.name());
        activityStartedEvent.setTimestamp(System.currentTimeMillis());

        eventPublisher.publish(activityStartedEvent);
        log.info("multi activity started");
    }

    /**
     * 多实例活动结束
     *
     * @param event
     */
    @Override
    protected void multiInstanceActivityCompletedWithCondition(FlowableMultiInstanceActivityCompletedEvent event) {
        ActivityCompletedEvent activityCompletedEvent = ActivityCompletedEvent.builder()
                .taskName(event.getActivityName())
                .taskDefinitionKey(event.getActivityId())
                .processDefinitionId(event.getProcessDefinitionId())
                .processInstanceId(event.getProcessInstanceId())
                .activityType(event.getActivityType())
                .formData(ApprovalContextSynchronizationManager.getFormData())
                .build();

        activityCompletedEvent.setId(UUID.randomUUID().toString());
        activityCompletedEvent.setIdentifier(EventIdentify.ACTIVITY_COMPLETED.name());
        activityCompletedEvent.setTimestamp(System.currentTimeMillis());

        eventPublisher.publish(activityCompletedEvent);
        log.info("multi activity completed");
    }

    /**
     * 活动结束
     *
     * @param event
     */
    @Override
    protected void activityCompleted(FlowableActivityEvent event) {
        if (isMultiInstanceBehavior(event)) {
            return;
        }
        // 插入历史表记录
        updateHisLogOnReceiveTask(event);

        ActivityCompletedEvent activityCompletedEvent = ActivityCompletedEvent.builder()
                .taskName(event.getActivityName())
                .taskDefinitionKey(event.getActivityId())
                .processDefinitionId(event.getProcessDefinitionId())
                .processInstanceId(event.getProcessInstanceId())
                .activityType(event.getActivityType())
                .formData(ApprovalContextSynchronizationManager.getFormData())
                .build();

        activityCompletedEvent.setId(UUID.randomUUID().toString());
        activityCompletedEvent.setIdentifier(EventIdentify.ACTIVITY_COMPLETED.name());
        activityCompletedEvent.setTimestamp(System.currentTimeMillis());

        eventPublisher.publish(activityCompletedEvent);
        log.info("activity completed");
    }

    /**
     * 实体已经删除
     *
     * @param event
     */
    @Override
    protected void entityDeleted(FlowableEngineEntityEvent event) {
        Object entity = event.getEntity();
        if (entity instanceof TaskEntityImpl) {
            TaskEntityImpl taskEntity = (TaskEntityImpl) entity;
            // 构建事件对象
            UserTaskDeletedEvent userTaskDeletedEvent = UserTaskDeletedEvent.builder()
                    .assignee(taskEntity.getAssignee())
                    .taskId(taskEntity.getId())
                    .taskName(taskEntity.getName())
                    .taskDefinitionKey(taskEntity.getTaskDefinitionKey())
                    .processInstanceId(taskEntity.getProcessInstanceId())
                    .processDefinitionId(taskEntity.getProcessDefinitionId())
                    .build();

            try {
                // 构建并更新MiTaskDo
                MiTaskDo miTaskDo = miTaskDoFactory.buildMiTaskDoByTaskEvent(taskEntity, false, false);
                miTaskDomainService.updateMiTask(miTaskDo);
            } catch (Exception e) {
                log.error("MiTaskException entityDeleted entity taskId:{}", taskEntity.getId(), e);
                // 抛出异常：throw new InfraException(e, TaskInfraErrorCodeEnum.MITASK_COMPLETED_UPTATE_ERROR);
            }

            // 设置领域事件基础信息
            userTaskDeletedEvent.setId(taskEntity.getId());
            userTaskDeletedEvent.setIdentifier(EventIdentify.TASK_DELETED.name());
            userTaskDeletedEvent.setTimestamp(System.currentTimeMillis());
            // publish task deleted event
            log.info("发送任务删除事件，event = {}", userTaskDeletedEvent);
            eventPublisher.publish(userTaskDeletedEvent);
        }
    }

    @Override
    protected void entityUpdated(FlowableEngineEntityEvent event) {
        Object entity = event.getEntity();
        if (entity instanceof TaskEntityImpl) {
            TaskEntityImpl taskEntity = (TaskEntityImpl) entity;

            // 仅监听更新事件
            if (taskEntity.isUpdated()
                    && !taskEntity.isInserted()
                    && !taskEntity.isDeleted()) {
                // MiTask3.0对接
                try {
                    // 更新MiTask
                    MiTaskDo miTaskDo = miTaskDoFactory.buildMiTaskDoByTaskEvent(taskEntity, false, true);
                    miTaskDomainService.updateMiTask(miTaskDo);

                    if (taskEntity.getClaimTime() != null) {
                        miTaskDomainService.deleteCandidateTask(miTaskDo);
                    }
                } catch (Exception e) {
                    log.error("MiTaskException entityUpdated entity taskId:{}", taskEntity.getId(), e);
                    // 抛出异常：throw new InfraException(e, TaskInfraErrorCodeEnum.MITASK_ENTITY_UPDATE_ERROR);
                }

                // 构建事件对象
                UserTaskUpdateEvent userTaskUpdateEvent = UserTaskUpdateEvent.builder()
                        .assignee(taskEntity.getAssignee())
                        .taskId(taskEntity.getId())
                        .taskName(taskEntity.getName())
                        .taskDefinitionKey(taskEntity.getTaskDefinitionKey())
                        .processInstanceId(taskEntity.getProcessInstanceId())
                        .processDefinitionId(taskEntity.getProcessDefinitionId())
                        .build();

                // 设置领域事件基础信息
                userTaskUpdateEvent.setId(taskEntity.getId());
                userTaskUpdateEvent.setIdentifier(EventIdentify.TASK_UPDATED.name());
                userTaskUpdateEvent.setTimestamp(System.currentTimeMillis());
                // publish task update event
                log.info("发送任务更新事件，event = {}", userTaskUpdateEvent);
                eventPublisher.publish(userTaskUpdateEvent);
            }
        }
    }

    private boolean isMultiInstanceBehavior(FlowableActivityEvent event) {
        String behaviorClass = event.getBehaviorClass();

        return CustomMultiInstanceBehaviorHelper.class.getName().equals(behaviorClass)
                || CustomParallelMultiInstanceBehavior.class.getName().equals(behaviorClass)
                || CustomSequentialMultiInstanceBehavior.class.getName().equals(behaviorClass);
    }

    /**
     * 存在委托人时
     * 替换task中的assignee 为委托人
     * 将原审批人保存至 description
     */
    private void replaceAssigneeOnDelegation(Object entity, boolean isStartTask) {
        UserConfigDomainService userConfigDomainService = SpringContextUtil.getBean(UserConfigDomainService.class);
        AccountRemoteService accountRemoteService = SpringContextUtil.getBean(AccountRemoteService.class);
        TaskService taskService = SpringContextUtil.getBean(TaskService.class);

        if (entity instanceof TaskEntityImpl) {
            TaskEntityImpl taskEntity = (TaskEntityImpl) entity;
            String assignee = taskEntity.getAssignee();
            String processKey = taskEntity.getProcessDefinitionId().split(":")[0];
            String initiatorOrgId = (String) taskService.getVariable(taskEntity.getId(),
                    BpmVariablesConstants.VARIABLE_INITIATOR_ORG_ID);
            BpmUser user = accountRemoteService.getUser(assignee);
            boolean isValidAccount = Objects.nonNull(user) && "A".equals(user.getHrStatus());

            //发起节点任务不考虑委托
            if (isStartTask && isValidAccount) {
                return;
            }

            //检查是否存在委托
            String delegationUser = userConfigDomainService.queryDelegationUser(assignee, processKey, initiatorOrgId);

            if (StringUtils.isBlank(delegationUser)) {
                return;
            }
            BpmUser delegationUserInfo = accountRemoteService.getUser(delegationUser);

            if (!StringUtils.equals(assignee, delegationUser)) {
                log.info(
                        "replace assignee on delegation. assignee: [" + assignee + "], delegation:[" + delegationUser +
                                "]");
                TaskEntityImpl task = (TaskEntityImpl) entity;

                // 先代批人替换（防止触发乐观锁）
                task.setAssignee(delegationUser);
                taskService.saveTask(task);
                // 再保存原审批人至Variable
                taskService.setVariableLocal(task.getId(), BpmVariablesConstants.VARIABLE_ORIGINAL_ASSIGNEE, assignee);
                BpmUser robot = accountRemoteService.getUser(BpmCommonConstants.ROBOT);
                String comment = "审批人委托";
                String commentEn = "Approver delegates";
                if (Objects.nonNull(user)) {
                    comment = String.format("%s(%s)委托%s(%s)代批", user.getDisplayName(), user.getUserName(),
                            delegationUserInfo.getDisplayName(), delegationUserInfo.getUserName());
                    commentEn = String.format("%s delegates %s to handle the approval", user.getUserName(),
                            delegationUserInfo.getUserName());
                }
                AssigneeChangeEvent assigneeChangeEvent = AssigneeChangeEvent.builder()
                        .taskId(taskEntity.getId())
                        .taskName(taskEntity.getName())
                        .processInstanceId(taskEntity.getProcessInstanceId())
                        .processDefinitionId(taskEntity.getProcessDefinitionId())
                        .assignee(robot)
                        .transferTo(BpmUser.builder().uid(delegationUser).build())
                        .notSendLarkMessage(true)
                        .comment(comment)
                        .commentEn(commentEn)
                        .build();

                // 设置领域事件基础信息
                assigneeChangeEvent.setId(UUID.randomUUID().toString());
                assigneeChangeEvent.setIdentifier(EventIdentify.ASSIGNEE_CHANGE.name());
                assigneeChangeEvent.setTimestamp(System.currentTimeMillis());
                // publish event
                log.info("发送委托事件，event = {}", assigneeChangeEvent);
                eventPublisher.publish(assigneeChangeEvent);
            }
        }
    }

    /**
     * 处理自动跳过 当审批人为robot时
     *
     * @param entity
     */
    private void autoSkipOnRobot(Object entity, boolean isStartTask) {
        if (isStartTask) {
            return;
        }
        TaskEntityImpl taskEntity = (TaskEntityImpl) entity;
        String assignee = taskEntity.getAssignee();
        AccountRemoteService accountRemoteService = SpringContextUtil.getBean(AccountRemoteService.class);

        BpmUser user = accountRemoteService.getUser(assignee);
        if (Objects.isNull(user) || !BpmCommonConstants.ROBOT.equals(user.getUserName())) {
            return;
        }
        // 当前事务提交后发送事件
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCompletion(int status) {
                if (TransactionSynchronization.STATUS_COMMITTED == status) {
                    // 构建事件对象
                    TaskAutoCompleteEvent taskAutoCompleteEvent = TaskAutoCompleteEvent.builder()
                            .taskId(taskEntity.getId())
                            .processInstanceId(taskEntity.getProcessInstanceId())
                            .processDefinitionId(taskEntity.getProcessDefinitionId())
                            .comment("按该流程规则，未找到审批人，已自动跳过")
                            .commentEn(
                                    "According to the process rules, the approver was not found and was automatically skipped.")
                            .formData(ApprovalContextSynchronizationManager.getFormData())
                            .autoOperationType(AutoOperationTypeEnum.EMPTY)
                            .build();

                    // 设置领域事件基础信息
                    taskAutoCompleteEvent.setId(UUID.randomUUID().toString());
                    taskAutoCompleteEvent.setIdentifier(EventIdentify.TASK_AUTO_COMPLETE.name());
                    taskAutoCompleteEvent.setTimestamp(System.currentTimeMillis());
                    // publish event
                    log.info("发送任务自动跳过事件，event = {}", taskAutoCompleteEvent);
                    eventPublisher.publish(taskAutoCompleteEvent);
                }
            }
        });

    }

    private boolean skipOnAssigneeDuplicate(Object entity) {
        if (entity instanceof TaskEntityImpl) {
            try {
                TaskEntityImpl taskEntity = (TaskEntityImpl) entity;
                ApproveStrategyService approveStrategyService = SpringContextUtil.getBean(ApproveStrategyService.class);
                return approveStrategyService.skipOnAssigneeDuplicate(taskEntity);
            } catch (Exception e) {
                log.error("auto skipped error!", e);
            }
        }
        return false;
    }

    /**
     * 插入接收任务历史记录
     *
     * @param event
     */
    private void insertHisLogOnReceiveTask(FlowableActivityEvent event) {
        if (ActivityTypeEnum.RECEIVE_TASK.getCode().equals(event.getActivityType())) {
            // 插入记录
            TaskRepository taskRepository = SpringContextUtil.getBean(TaskRepository.class);

            TaskDo taskDo = TaskDo.builder()
                    .taskDefinitionKey(event.getActivityId())
                    .taskName(event.getActivityName())
                    .processInstanceId(event.getProcessInstanceId())
                    .activityType(ActivityTypeEnum.RECEIVE_TASK)
                    .processDefinitionId(event.getProcessDefinitionId())
                    .createTime(ZonedDateTime.now())
                    .endTime(null)
                    .build();
            taskRepository.addHistoryLogTask(taskDo, null);
        }
    }

    /**
     * 插入接收任务历史记录
     *
     * @param event
     */
    private void updateHisLogOnReceiveTask(FlowableActivityEvent event) {
        if (ActivityTypeEnum.RECEIVE_TASK.getCode().equals(event.getActivityType())) {
            HistoryService historyService = SpringContextUtil.getBean(HistoryService.class);

            List<HistoricTaskInstance> historicTaskInstances = historyService.createHistoricTaskInstanceQuery()
                    .taskDefinitionKey(event.getActivityId())
                    .processInstanceId(event.getProcessInstanceId())
                    .unfinished().list();

            ManagementService managementService = SpringContextUtil.getBean(ManagementService.class);
            historicTaskInstances.forEach(i -> {
                TaskEntityImpl taskEntity = new TaskEntityImpl();
                taskEntity.setId(i.getId());
                HistoricTaskService historicTaskService = managementService.executeCommand(
                        new HistoricTaskServiceGetCmd());
                managementService.executeCommand(new HiTaskInsEntityMarkEndedCmd(historicTaskService, taskEntity));
            });
        }
    }

    private List<String> loadTaskCandidates(TaskEntityImpl task) {
        if (StringUtils.isBlank(task.getAssignee())) {
            TaskService taskService = SpringContextUtil.getBean(TaskService.class);
            List<IdentityLink> identityLinksForTask = taskService.getIdentityLinksForTask(task.getId());
            Set<String> userSets = identityLinksForTask.stream()
                    .filter(i -> IdentityLinkType.CANDIDATE.equals(i.getType()))
                    .map(IdentityLinkInfo::getUserId)
                    .collect(Collectors.toSet());
            return Lists.newArrayList(userSets);
        }
        return new ArrayList<>();
    }

}
