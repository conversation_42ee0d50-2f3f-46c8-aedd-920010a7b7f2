package com.mi.oa.infra.mibpm.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.MiErpProcessInstanceAssigneeMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.MiErpProcessInstanceAssignee;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.service.MiErpProcessInstanceAssigneeService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/3/9 18:57
 */
@Service
public class MiErpProcessInstanceAssigneeServiceImpl extends ServiceImpl<MiErpProcessInstanceAssigneeMapper, MiErpProcessInstanceAssignee>
        implements MiErpProcessInstanceAssigneeService {
}
