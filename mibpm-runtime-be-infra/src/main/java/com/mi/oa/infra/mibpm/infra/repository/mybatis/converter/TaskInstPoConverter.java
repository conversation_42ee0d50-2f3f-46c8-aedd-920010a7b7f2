package com.mi.oa.infra.mibpm.infra.repository.mybatis.converter;

import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskLinkResp;
import com.mi.oa.infra.mibpm.common.constant.BpmVariablesConstants;
import com.mi.oa.infra.mibpm.common.enums.ActivityTypeEnum;
import com.mi.oa.infra.mibpm.common.enums.AutoOperationTypeEnum;
import com.mi.oa.infra.mibpm.common.enums.ClientEnum;
import com.mi.oa.infra.mibpm.common.enums.DelegationStateEnum;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.TaskLink;
import com.mi.oa.infra.mibpm.domain.mitask.model.MiTaskDo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskAttribute;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.flowable.extension.model.SignAddTypeEnum;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.utils.SpringContextUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.task.api.DelegationState;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.service.impl.persistence.entity.TaskEntityImpl;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/2/17 10:25 AM
 **/
@Mapper(componentModel = "spring")
public interface TaskInstPoConverter {

    DelegationState delegationStateToDelegationStateEnum(DelegationStateEnum delegationStateEnum);

    DelegationStateEnum delegationStateEnumToDelegationState(DelegationState delegationState);

    @Mappings({
            @Mapping(source = "id", target = "taskId"),
            @Mapping(source = "name", target = "taskName"),
            @Mapping(source = "description", target = "taskAttribute"),
            @Mapping(ignore = true, target = "candidates"),
            @Mapping(ignore = true, target = "transientVariables")
    })
    TaskDo poToDo(TaskEntityImpl task);

    default TaskDo miTaskToDo(MiTaskDo miTaskDo) {
        if (miTaskDo == null) {
            return null;
        }
        TaskDo.TaskDoBuilder taskDo = TaskDo.builder();
        taskDo.processDefinitionId(miTaskDo.getProcDefId());
        taskDo.taskDefinitionKey(miTaskDo.getTaskDefKey());
        taskDo.taskId(miTaskDo.getTaskId());
        taskDo.processInstanceId(miTaskDo.getProcInstId());
        taskDo.taskName(miTaskDo.getTaskName());
        taskDo.owner(miTaskDo.getOwner());
        taskDo.assignee(miTaskDo.getAssignee());
        taskDo.executionId(miTaskDo.getExecutionId());
        taskDo.createTime(miTaskDo.getCreateTime());
        taskDo.endTime(miTaskDo.getEndTime());
        taskDo.dueDate(miTaskDo.getDueDate());
        taskDo.parentTaskId(miTaskDo.getParentTaskId());
        taskDo.signType(miTaskDo.getSignType());
        taskDo.activityType(ActivityTypeEnum.USER_TASK);
        taskDo.isVetoUser(miTaskDo.getIsVetoUser());
        taskDo.taskAttribute(map(miTaskDo));
        taskDo.miTaskDo(miTaskDo);
        taskDo.priority(50);
        return taskDo.build();
    }

    List<TaskDo> miTaskToDoList(List<MiTaskDo> miTaskDos);

    @Mapping(source = "activityName", target = "taskName")
    @Mapping(source = "activityId", target = "taskDefinitionKey")
    @Mapping(ignore = true, target = "candidates")
    @Mapping(expression = "java(com.mi.oa.infra.mibpm.common.enums.ActivityTypeEnum.findByCode(task.getActivityType()))", target = "activityType")
    TaskDo poToDo(HistoricActivityInstance task);

    @Mappings({
            @Mapping(source = "id", target = "taskId"),
            @Mapping(source = "name", target = "taskName"),
            @Mapping(source = "description", target = "taskAttribute"),
    })
    TaskDo taskPoToDo(Task task);

    @Mappings({
            @Mapping(source = "id", target = "taskId"),
            @Mapping(source = "name", target = "taskName"),
            @Mapping(source = "description", target = "taskAttribute"),
    })
    TaskDo taskPoToDo(HistoricTaskInstance task);

    List<TaskDo> poToDo(List<TaskEntityImpl> task);

    @Mappings({
            @Mapping(source = "taskId", target = "id"),
            @Mapping(source = "taskName", target = "name"),
            @Mapping(source = "taskLocalVariables", target = "variablesLocal"),
            @Mapping(ignore = true, target = "candidates"),
            @Mapping(ignore = true, target = "transientVariables")
    })
    TaskEntityImpl doToPo(TaskDo taskDo);

    default TaskAttribute map(String description) {
        Map map = new Gson().fromJson(description, Map.class);
        TaskAttribute taskAttribute = new TaskAttribute();
        if (Objects.nonNull(map)) {
            if (map.containsKey("signAddType")) {
                taskAttribute.setSignAddType(SignAddTypeEnum.valueOf((String) map.get("signAddType")));
            }
            if (Objects.nonNull(map.get("source"))) {
                taskAttribute.setClient(ClientEnum.findByCode((String) map.get("source")));
            }
            if (Objects.nonNull(map.get("status"))) {
                taskAttribute.setOperation(UserTaskOperation.findByCode((String) map.get("status")));
            }
            if (Objects.nonNull(map.get("comment"))) {
                taskAttribute.setComment((String) map.get("comment"));
            }
            if (Objects.nonNull(map.get("modelCode"))) {
                taskAttribute.setModelCode((String) map.get("modelCode"));
            }
            if (Objects.nonNull(map.get("businessKey"))) {
                taskAttribute.setBusinessKey((String) map.get("businessKey"));
            }
            if (Objects.nonNull(map.get("processInstanceName"))) {
                taskAttribute.setProcessInstanceName((String) map.get("processInstanceName"));
            }
            if (Objects.nonNull(map.get("startUserId"))) {
                taskAttribute.setStartUserId((String) map.get("startUserId"));
            }
            if (Objects.nonNull(map.get("formKey"))) {
                taskAttribute.setFormKey((Map<String, String>) map.get("formKey"));
            }
            if (Objects.nonNull(map.get("commentEn"))) {
                taskAttribute.setCommentEn((String) map.get("commentEn"));
            }
            if (Objects.nonNull(map.get("autoOperationType"))) {
                taskAttribute.setAutoOperationType(
                        AutoOperationTypeEnum.findByCode((String) map.get("autoOperationType")));
            }
            if (Objects.nonNull(map.get("isVetoTask"))) {
                taskAttribute.setIsVetoTask((Boolean) map.get("isVetoTask"));
            }
        }
        return taskAttribute;
    }

    default TaskAttribute map(MiTaskDo miTaskDo) {
        Map<String, String> map = new HashMap<>();
        TaskAttribute taskAttribute = new TaskAttribute();
        if (Objects.nonNull(map)) {
            if (map.containsKey("signAddType")) {
                taskAttribute.setSignAddType(SignAddTypeEnum.valueOf((String) map.get("signAddType")));
            }
            if (Objects.nonNull(map.get("source"))) {
                taskAttribute.setClient(ClientEnum.findByCode((String) map.get("source")));
            }
            if (Objects.nonNull(map.get("status"))) {
                taskAttribute.setOperation(UserTaskOperation.findByCode((String) map.get("status")));
            }
            if (Objects.nonNull(map.get("comment"))) {
                taskAttribute.setComment((String) map.get("comment"));
            }
            if (Objects.nonNull(map.get("modelCode"))) {
                taskAttribute.setModelCode((String) map.get("modelCode"));
            }
            if (Objects.nonNull(map.get("businessKey"))) {
                taskAttribute.setBusinessKey((String) map.get("businessKey"));
            }
            if (Objects.nonNull(map.get("processInstanceName"))) {
                taskAttribute.setProcessInstanceName((String) map.get("processInstanceName"));
            }
            if (Objects.nonNull(map.get("startUserId"))) {
                taskAttribute.setStartUserId((String) map.get("startUserId"));
            }
            if (Objects.nonNull(map.get("formKey"))) {
                //taskAttribute.setFormKey((Map<String, String>) map.get("formKey"));
            }
            if (Objects.nonNull(map.get("commentEn"))) {
                taskAttribute.setCommentEn((String) map.get("commentEn"));
            }
            if (Objects.nonNull(map.get("autoOperationType"))) {
                taskAttribute.setAutoOperationType(
                        AutoOperationTypeEnum.findByCode((String) map.get("autoOperationType")));
            }
            if (Objects.nonNull(map.get("isVetoTask"))) {
                //taskAttribute.setIsVetoTask((Boolean) map.get("isVetoTask"));
            }
        }
        return taskAttribute;
    }

    default String mapBpmUser(BpmUser user) {
        if (user == null) {
            return null;
        }
        return user.getUid();
    }

    default BpmUser mapBpmUser(String s) {
        if (null == s) {
            return BpmUser.builder().build();
        }
        return BpmUser.builder().uid(s.trim()).build();
    }

    default void fillTaskDo(List<TaskDo> taskDoList, boolean needVariable) {
        Set<String> userIds = Sets.newHashSet();
        Set<String> taskIds = Sets.newHashSet();
        if (CollectionUtils.isEmpty(taskDoList)) {
            return;
        }
        for (TaskDo taskDo : taskDoList) {
            if (null != taskDo.getAssignee()) {
                userIds.add(taskDo.getAssignee().getUid());
                userIds.add(taskDo.getAssignee().getUserName());
            }
            if (null != taskDo.getOperator()) {
                userIds.add(taskDo.getOperator().getUid());
                userIds.add(taskDo.getOperator().getUserName());
            }
            if (null != taskDo.getOwner()) {
                userIds.add(taskDo.getOwner().getUid());
                userIds.add(taskDo.getOwner().getUid());
            }
            if (null != taskDo.getCreateUser()) {
                userIds.add(taskDo.getCreateUser().getUid());
                userIds.add(taskDo.getCreateUser().getUserName());
            }
            if (taskDo.getTaskLocalVariables() != null && !taskDo.getTaskLocalVariables().isEmpty()) {
                if (taskDo.getTaskLocalVariables().containsKey(BpmVariablesConstants.VARIABLE_ORIGINAL_ASSIGNEE)) {
                    userIds.add(taskDo.getTaskLocalVariables().get(BpmVariablesConstants.VARIABLE_ORIGINAL_ASSIGNEE)
                            .toString());
                }
            }
            taskIds.add(taskDo.getTaskId());
        }
        List<BpmUser> bpmUsers = ((AccountRemoteService) SpringContextUtil.getBean(
                AccountRemoteService.class)).listUsers(userIds);
        Map<String, BpmUser> userMap = new HashMap<>();
        for (BpmUser bpmUser : bpmUsers) {
            userMap.putIfAbsent(bpmUser.getUid(), bpmUser);
            userMap.putIfAbsent(bpmUser.getUserName(), bpmUser);
        }
        HistoryService bean = SpringContextUtil.getBean(HistoryService.class);
        Map<String, List<HistoricVariableInstance>> taskVariableMap = new HashMap<>();
        if (needVariable) {
            List<HistoricVariableInstance> variableInstanceList = bean.createHistoricVariableInstanceQuery()
                    .taskIds(taskIds).list();
            taskVariableMap = variableInstanceList.stream()
                    .collect(Collectors.groupingBy(HistoricVariableInstance::getTaskId));
        }
        for (TaskDo taskDo : taskDoList) {
            if (null != taskDo.getAssignee()) {
                BpmUser user;
                if (StringUtils.isNotBlank(taskDo.getAssignee().getUid())) {
                    user = userMap.getOrDefault(taskDo.getAssignee().getUid(), taskDo.getAssignee());
                } else {
                    user = userMap.getOrDefault(taskDo.getAssignee().getUserName(), taskDo.getAssignee());
                }
                taskDo.setAssignee(user);
            }
            if (null != taskDo.getOperator()) {
                BpmUser user = userMap.getOrDefault(taskDo.getOperator().getUid(), taskDo.getOperator());
                taskDo.setOperator(user);
            }
            if (null != taskDo.getOwner()) {
                BpmUser user = userMap.getOrDefault(taskDo.getOwner().getUid(), taskDo.getOwner());
                taskDo.setOwner(user);
            }
            if (null != taskDo.getCreateUser()) {
                BpmUser user = userMap.getOrDefault(taskDo.getCreateUser().getUid(), taskDo.getCreateUser());
                taskDo.setCreateUser(user);
            }
            if (Objects.isNull(taskDo.getTaskVariables())) {
                taskDo.setTaskVariables(new LinkedHashMap<>());
            }
            if (needVariable) {
                List<HistoricVariableInstance> taskVariables = taskVariableMap.get(taskDo.getTaskId());
                if (null != taskVariables) {
                    Map<String, Object> taskVariable = taskVariables.stream()
                            .collect(Collectors.toMap(HistoricVariableInstance::getVariableName,
                                    v -> v.getValue() == null ? "" : v.getValue(),
                                    (k1, k2) -> k1));
                    taskDo.setTaskLocalVariables(taskVariable);
                }
            }
            if (taskDo.getTaskLocalVariables() != null && !taskDo.getTaskLocalVariables().isEmpty()) {
                if (taskDo.getTaskLocalVariables().containsKey(BpmVariablesConstants.VARIABLE_ORIGINAL_ASSIGNEE)) {
                    BpmUser originalAssignee =
                            userMap.getOrDefault(taskDo.getTaskLocalVariables()
                                            .get(BpmVariablesConstants.VARIABLE_ORIGINAL_ASSIGNEE),
                                    taskDo.getOriginalAssignee());
                    taskDo.setOriginalAssignee(originalAssignee);
                }
            }

        }
    }

    default void fillTaskDo(TaskDo taskDo) {
        if (null == taskDo) {
            return;
        }
        Set<String> userIds = Sets.newHashSet();
        if (null != taskDo.getAssignee()) {
            userIds.add(taskDo.getAssignee().getUid());
        }
        if (null != taskDo.getOperator()) {
            userIds.add(taskDo.getOperator().getUid());
        }
        if (null != taskDo.getOwner()) {
            userIds.add(taskDo.getOwner().getUid());
        }
        List<BpmUser> bpmUsers = ((AccountRemoteService) SpringContextUtil.getBean(
                AccountRemoteService.class)).listUsers(userIds);
        Map<String, BpmUser> userMap = new HashMap<>();
        for (BpmUser bpmUser : bpmUsers) {
            userMap.putIfAbsent(bpmUser.getUid(), bpmUser);
            userMap.putIfAbsent(bpmUser.getUserName(), bpmUser);
        }
        if (null != taskDo.getAssignee()) {
            BpmUser user = userMap.getOrDefault(taskDo.getAssignee().getUid(), taskDo.getAssignee());
            taskDo.setAssignee(user);
        }
        if (null != taskDo.getOperator()) {
            BpmUser user = userMap.getOrDefault(taskDo.getOperator().getUid(), taskDo.getOperator());
            taskDo.setOperator(user);
        }
        if (null != taskDo.getOwner()) {
            BpmUser user = userMap.getOrDefault(taskDo.getOwner().getUid(), taskDo.getOwner());
            taskDo.setOwner(user);
        }
        if (Objects.isNull(taskDo.getTaskVariables())) {
            taskDo.setTaskVariables(new LinkedHashMap<>());
        }
    }

    TaskLinkResp convert(TaskLink taskLink);
}
