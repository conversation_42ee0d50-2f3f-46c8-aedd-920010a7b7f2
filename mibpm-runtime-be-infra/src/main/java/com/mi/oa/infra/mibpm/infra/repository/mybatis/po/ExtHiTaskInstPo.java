package com.mi.oa.infra.mibpm.infra.repository.mybatis.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/10/30 17:32
 */
@Data
@TableName("act_ext_hi_taskinst")
public class ExtHiTaskInstPo implements Serializable {

    @TableId("id_")
    private String id;

    @TableField("proc_def_id_")
    private String procDefId;

    @TableField("proc_inst_id_")
    private String procInstId;

    @TableField("task_key_")
    private String taskKey;

    @TableField("name_")
    private String name;

    @TableField("description_")
    private String description;

    @TableField("owner_")
    private String owner;

    @TableField("assignee_")
    private String assignee;

    @TableField("duration_")
    private Long duration;

    @TableField("priority_")
    private Integer priority;

    @TableField("due_date_")
    private java.util.Date dueDate;

    @TableField("category_")
    private String category;

    @TableField("pc_link_")
    private String pcLink;

    @TableField("mobile_link_")
    private String mobileLink;

    @TableField("tenant_id_")
    private String tenantId;

    @TableField("create_time_")
    private Long createTime;

    @TableField("update_time_")
    private Long updateTime;

    @TableField("end_time_")
    private Long endTime;

    @TableField("status_")
    private String status;

    @TableField("rev_")
    private Integer rev;
}