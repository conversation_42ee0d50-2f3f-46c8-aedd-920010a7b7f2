package com.mi.oa.infra.mibpm.infra.remote.converter;

import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.infra.remote.entity.PosDto;
import com.mi.oa.infra.mibpm.infra.remote.entity.PosUserDto;
import com.mi.oa.infra.mibpm.infra.remote.entity.PositionAuthDto;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.PostUserResp;
import com.mi.oa.infra.organization.rep.PosVO;
import com.mi.oa.infra.organization.rep.UserAccountDto;
import com.mi.oa.infra.uc.common.model.PostAuthorityDetailDto;
import com.mi.oa.infra.uc.common.model.UserDto;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/11/23 17:34
 **/
@Mapper(componentModel = "spring")
public interface PosAuthConverter {

    @Mapping(source = "postInfo.orgType", target = "orgType")
    @Mapping(source = "postInfo.orgName", target = "orgName")
    @Mapping(source = "postInfo.postName", target = "postName")
    PositionAuthDto dto(PostAuthorityDetailDto dto);

    List<PositionAuthDto> list(List<PostAuthorityDetailDto> list);

    @Mapping(source = "identityCode", target = "uid")
    BpmUser userConvert(UserDto userDto);

    PosUserDto dto(PostUserResp.PostUser postUser);

    List<PosUserDto> postUserDto(List<PostUserResp.PostUser> postUser);

    default List<PosDto> toPosDtos(List<PosVO> posVOs) {
        if (posVOs == null) {
            return null;
        }

        List<PosDto> list = new ArrayList<PosDto>(posVOs.size());
        for (PosVO posVO : posVOs) {
            if (posVO == null) {
                continue;
            }
            PosDto posDto = new PosDto();
            posDto.setPosName(posVO.getPosName());
            posDto.setPosNameEn(posVO.getPosNameEn());
            posDto.setPosCode(posVO.getPosCode());
            posDto.setOrgCode(posVO.getOrgCode());
            posDto.setOrgTreeCode(posVO.getOrgTreeCode());
            posDto.setJobCode(posVO.getJobCode());
            posDto.setDescription(posVO.getDescription());
            posDto.setExpandInfo(posVO.getExpandInfo());
            posDto.setOrgVO(posVO.getOrgVO());
            posDto.setJobInfo(posVO.getJobInfo());
            posDto.setOrgTreeInfo(posVO.getOrgTreeInfo());
            posDto.setUids(new ArrayList<>());

            List<UserAccountDto> accountInfoList = posVO.getAccountInfoList();
            if (CollectionUtils.isNotEmpty(accountInfoList)) {
                List<String> uids = accountInfoList.stream().map(UserAccountDto::getUid).collect(Collectors.toList());
                posDto.setUids(uids);
            }
            list.add(posDto);
        }
        return list;
    }
}
