package com.mi.oa.infra.mibpm.infra.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
* 对数据库的mapper加载
* <AUTHOR>
* @date          2021/6/10 11:35
*/
@Configuration
@ComponentScan
@MapperScan(value = "com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper")
public class InfraCoreConfig {
}
