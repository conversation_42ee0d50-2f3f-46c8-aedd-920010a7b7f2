package com.mi.oa.infra.mibpm.infra.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.oa.infra.mibpm.domain.operation.model.OperationHistoryDo;
import com.mi.oa.infra.mibpm.infra.operation.entity.OperateHistoryPo;
import com.mi.oa.infra.mibpm.infra.operation.repository.OperationHistoryRepository;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.converter.OperationHistoryConverter;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.OperationHistoryMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/17 19:26
 **/
@Repository
public class OperationHistoryRepositoryImpl implements OperationHistoryRepository {

    @Autowired
    private OperationHistoryConverter operationHistoryConverter;
    @Autowired
    private OperationHistoryMapper operationHistoryMapper;

    @Override
    public void save(OperationHistoryDo operationHistoryDo) {
        OperateHistoryPo operateHistoryPo = operationHistoryConverter.doToPo(operationHistoryDo);
        operationHistoryMapper.insert(operateHistoryPo);
    }

    @Override
    public List<OperationHistoryDo> queryByProcInst(String procInstId) {
        if (StringUtils.isBlank(procInstId)) {
            return new ArrayList<>();
        }
        List<OperateHistoryPo> operateHistoryPos = operationHistoryMapper.selectList(
                Wrappers.<OperateHistoryPo>lambdaQuery().eq(OperateHistoryPo::getProcessInstId, procInstId)
                        .ne(OperateHistoryPo::getOperation, "").orderByAsc(OperateHistoryPo::getCreateTime));
        List<OperationHistoryDo> historyDos = operationHistoryConverter.poToDoList(operateHistoryPos);
        operationHistoryConverter.fillUsers(historyDos);
        return historyDos;
    }
}
