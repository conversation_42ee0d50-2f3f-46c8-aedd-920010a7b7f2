package com.mi.oa.infra.mibpm.infra.repository.deploy.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 表单类型
 *
 * <AUTHOR>
 * @date 2023/7/17 14:55
 */
@Getter
@AllArgsConstructor
public enum FormTypes {

    /**
     * 流程
     */
    BPM_FORM("bpm_form"),

    /**
     * 流程表单
     */
    EXT_FORM("ext_form");

    /**
     * code
     */
    private final String code;

    public static FormTypes findByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (FormTypes value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
