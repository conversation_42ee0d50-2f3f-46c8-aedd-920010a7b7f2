package com.mi.oa.infra.mibpm.infra.repository.mybatis.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/9/20
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName(value = "mibpm_task_ext", autoResultMap = true)
public class MiTaskProcessExtPo {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 流程实例ID
     */
    private String procInstId;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 变量名
     */
    private String name;
    /**
     * 变量类型
     */
    private String type;
    /**
     * 文本类型值
     */
    private String textValue;
    /**
     * 浮点类型值
     */
    private Double doubleValue;
    /**
     * 整型值
     */
    private Long longValue;
    /**
     * 创建时间（毫秒级Unix时间戳）
     */
    private Long createTime;
    /**
     * 更新时间（毫秒级Unix时间戳）
     */
    private Long updateTime;

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof MiTaskProcessExtPo)) {
            return false;
        }
        return this.identity().equals(((MiTaskProcessExtPo) obj).identity());
    }

    public String identity() {
        return procInstId + taskId + name + type + textValue + doubleValue + longValue;
    }

    public String keyIdentity() {
        return procInstId + taskId + name + type;
    }
}
