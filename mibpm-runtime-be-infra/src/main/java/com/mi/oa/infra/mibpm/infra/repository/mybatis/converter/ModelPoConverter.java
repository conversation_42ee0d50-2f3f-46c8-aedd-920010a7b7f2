package com.mi.oa.infra.mibpm.infra.repository.mybatis.converter;

import com.mi.oa.infra.mibpm.common.enums.ModelType;
import com.mi.oa.infra.mibpm.infra.models.entity.ModelDo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ModelHistoryPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ModelMetaPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ModelPo;
import com.mi.oa.infra.mibpm.sdk.dto.ModelEnableStatus;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Mappings;
import org.mapstruct.ValueMapping;

import java.util.Objects;

@Mapper(componentModel = "spring")
public interface ModelPoConverter {

    default ModelEnableStatus mapEnableStatus(Integer status) {
        for (ModelEnableStatus value : ModelEnableStatus.values()) {
            if (Objects.equals(value.getCode(), status)) {
                return value;
            }
        }
        return null;
    }

    default Integer mapEnableStatus(ModelEnableStatus modelEnableStatus) {
        if (null == modelEnableStatus) {
            return null;
        }
        return modelEnableStatus.getCode();
    }

    default Integer map(ModelType value) {
        return value.getId();
    }

    default ModelType map(int value) {
        for (ModelType type : ModelType.values()) {
            if (Objects.equals(type.getId(), value)) {
                return type;
            }
        }
        return null;
    }

    @ValueMapping(source = MappingConstants.ANY_REMAINING, target = MappingConstants.NULL)
    @Mappings({
            @Mapping(source = "modelDo.usableConfig.webEnabled", target = "isWebEnabled"),
            @Mapping(source = "modelDo.usableConfig.businessEnabled", target = "isBusinessEnabled")
    })
    ModelMetaPo modelPoToModelMatePo(ModelDo modelDo);

    @ValueMapping(source = MappingConstants.ANY_REMAINING, target = MappingConstants.NULL)
    @Mappings({
            @Mapping(source = "modelCode", target = "modelKey"),
            @Mapping(source = "modelId", target = "id"),
            @Mapping(source = "modelEditorContent", target = "modelEditorJson"),
    })
    ModelPo doToPo(ModelDo modelDo);

    @Mappings({
            @Mapping(source = "id", target = "modelId"),
            @Mapping(target = "id", ignore = true)
    })
    ModelHistoryPo toModelHistory(ModelPo model);

    @ValueMapping(source = MappingConstants.ANY_REMAINING, target = MappingConstants.NULL)
    @Mappings({
            @Mapping(source = "po.modelKey", target = "modelCode"),
            @Mapping(source = "po.modelEditorJson", target = "modelEditorContent"),
            @Mapping(source = "po.id", target = "modelId"),
            @Mapping(source = "meta.name", target = "name"),
            @Mapping(source = "meta.description", target = "description"),
            @Mapping(source = "meta.modelType", target = "modelType"),
            @Mapping(source = "meta.isWebEnabled", target = "usableConfig.webEnabled"),
            @Mapping(source = "meta.isBusinessEnabled", target = "usableConfig.businessEnabled"),
            @Mapping(source = "meta.isAppEnabled", target = "usableConfig.appEnabled"),
            @Mapping(source = "meta.appUrl", target = "usableConfig.appUrl"),
            @Mapping(source = "meta.webUrl", target = "usableConfig.webUrl"),
            @Mapping(source = "meta.version", target = "metaVersion"),
            @Mapping(source = "meta.businessOwner", target = "businessOwners"),
            @Mapping(source = "po.version", target = "version"),
    })
    ModelDo poToDo(ModelPo po, ModelMetaPo meta);
}
