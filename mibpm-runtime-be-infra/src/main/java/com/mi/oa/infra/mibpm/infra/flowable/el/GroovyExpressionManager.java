package com.mi.oa.infra.mibpm.infra.flowable.el;

import java.util.Map;
import org.flowable.common.engine.api.delegate.Expression;
import org.flowable.spring.SpringExpressionManager;
import org.springframework.context.ApplicationContext;

public class GroovyExpressionManager extends SpringExpressionManager {

    // Groovy脚本前缀
    public static final String GROOVY_PREFIX = "groovy{";
    // Groovy脚本后缀
    public static final String GROOVY_SUFFIX = "}";

    public GroovyExpressionManager(ApplicationContext applicationContext, Map<Object, Object> beans) {
        super(applicationContext, beans);
    }

    /**
     * 同时支持Groovy和Juel表达式，如果是Groovy表达式，返回GroovyExpression，否则返回JuelExpression。
     */
    @Override
    public Expression createExpression(String text) {
        // 判断是否是Groovy表达式
        if (!isGroovyExpression(text)) {
            return super.createExpression(text);
        }

        // text去除groovy:前缀
        String expression = extractExpression(text);
        GroovyExpression groovyExpression = new GroovyExpression(expression);

        if (isCacheEnabled(text)) {
            expressionCache.add(expression, groovyExpression);
        }

        return groovyExpression;

    }

    public boolean isGroovyExpression(String text) {
        return text.startsWith(GROOVY_PREFIX) && text.endsWith(GROOVY_SUFFIX);
    }

    public String extractExpression(String text) {
        if (text.startsWith(GROOVY_PREFIX) && text.endsWith(GROOVY_SUFFIX)) {
            return text.substring(GROOVY_PREFIX.length(), text.length() - GROOVY_SUFFIX.length());
        } else {
            return text;
        }
    }
}
