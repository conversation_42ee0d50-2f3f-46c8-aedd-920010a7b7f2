package com.mi.oa.infra.mibpm.infra.flowable.sign;

import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskSignType;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/14 15:26
 **/
@Service
public class ParallelOneSignBeforeStrategy extends SignBeforeStrategy {
    ParallelOneSignBeforeStrategy(RuntimeService runtimeService, TaskService taskService) {
        super(runtimeService, taskService);
    }

    @Override
    UserTaskSignType getUserTaskSignType() {
        return UserTaskSignType.PARALLEL_ONE;
    }

}
