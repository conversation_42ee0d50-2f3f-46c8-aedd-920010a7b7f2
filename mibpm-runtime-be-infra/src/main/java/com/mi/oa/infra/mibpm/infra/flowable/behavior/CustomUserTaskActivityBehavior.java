package com.mi.oa.infra.mibpm.infra.flowable.behavior;

import com.google.common.collect.Lists;
import com.mi.oa.infra.mibpm.common.constant.BpmVariablesConstants;
import com.mi.oa.infra.mibpm.flowable.extension.BpmnExtensionHelper;
import com.mi.oa.infra.mibpm.flowable.extension.model.AssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskSignType;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskWrapper;
import com.mi.oa.infra.mibpm.infra.flowable.assign.AssigneeFactory;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.UserTask;
import org.flowable.common.engine.impl.el.ExpressionManager;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.impl.bpmn.behavior.UserTaskActivityBehavior;
import org.flowable.engine.impl.cfg.ProcessEngineConfigurationImpl;
import org.flowable.task.service.TaskService;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.larksuite.appframework.sdk.client.message.CardMessage;
import com.larksuite.appframework.sdk.client.message.card.Card;
import com.larksuite.appframework.sdk.client.message.card.objects.I18n;
import com.larksuite.appframework.sdk.core.protocol.common.I18nText;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.TaskLink;
import com.mi.oa.infra.mibpm.domain.message.model.Content;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo;
import com.mi.oa.infra.mibpm.domain.message.service.LarkMessageDomainService;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.procinst.service.ProcessInstanceDomainService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.LarkAppRemoteService;

/**
 * 自定义用户任务节点行为解析器
 * 1、扩充任务指派方式
 *
 * <AUTHOR>
 * @date 2021/11/18 20:03
 */
@Slf4j
public class CustomUserTaskActivityBehavior extends UserTaskActivityBehavior {

    protected UserTaskWrapper userTaskExtension;
    protected AssigneeFactory assigneeFactory;
    protected BpmnExtensionHelper bpmnExtensionHelper;

    @Autowired
    protected ProcessInstanceDomainService processInstanceDomainService;
    @Autowired
    protected LarkMessageDomainService larkMessageDomainService;
    @Autowired
    protected LarkAppRemoteService larkAppRemoteService;
    @Autowired
    protected AccountRemoteService accountRemoteService;

    public CustomUserTaskActivityBehavior(UserTask userTask,
                                          AssigneeFactory assigneeFactory,
                                          BpmnExtensionHelper bpmnExtensionHelper) {
        super(userTask);
        this.userTaskExtension = bpmnExtensionHelper.getUserTaskWrapper(userTask);
        this.assigneeFactory = assigneeFactory;
    }

    @Override
    protected void handleAssignments(TaskService taskService, String assignee, String owner, List<String> candidateUsers, List<String> candidateGroups, TaskEntity task, ExpressionManager expressionManager, DelegateExecution execution, ProcessEngineConfigurationImpl processEngineConfiguration) {
        if (userTaskExtension == null) {
            super.handleAssignments(taskService, assignee, owner, candidateUsers, candidateGroups,
                    task, expressionManager, execution, processEngineConfiguration);
            return;
        }

        List<AssigneeRule> assigneeRules = userTaskExtension.getAssigneeRules();
        UserTaskSignType signType = userTaskExtension.getSignType();
        if (assigneeRules == null) {
            super.handleAssignments(taskService, assignee, owner, candidateUsers, candidateGroups,
                    task, expressionManager, execution, processEngineConfiguration);
            return;
        }
        Map<String, Object> variablesLocal = execution.getVariablesLocal();
        if (null != variablesLocal && variablesLocal.containsKey(BpmVariablesConstants.VARIABLE_SIGN_TASK)) {
            assignee = (String) variablesLocal.get(BpmVariablesConstants.VARIABLE_ASSIGNEE);
        } else if (signType.equals(UserTaskSignType.SINGLE)) {
            for (AssigneeRule assigneeRule : assigneeRules) {
                List<String> miErpProcessAssignees = assigneeFactory.buildAssignee(assigneeRule, execution);
                //当出现以下问题时
                //在AbstractAssigneeService中的getOrgOwnerIndex方法出现：
                //这个users就是七个汇报线上所有的人 上到雷总 下到自己
                //正常情况下 汇报线中一般是会有部门负责人的 所以这里直接找就行了
                //但是特殊情况下 某个的汇报线中没有部门负责人 比如 部长助理 直接汇报给部长
                //那么这个users中就不包含部门负责人 所以这里就找不到人了，返回null
                //再返回null的情况下 就要立刻给流程管理员通过小米审批机器人 发送消息
                //这种情况时，根据调用链，返回的结果result为null，那么就直接返回空列表
                //那么assignees即为空列表，现在检测为空 我就要通知流程管理员
                //给流程管理员发送飞书消息：
                //1 标题：【异常】{发起人名称} 发起 {审批单名称} 审批节点异常，请及时处理
                //2 内容：{原审批单内容}+{异常原因}
                //3 快捷按钮：【查看详情】
                //其中流程管理员的获取方式可以根据参数task中的processInstanceId获取到对应的流程实例，然后根据流程实例的发起人获取到流程管理员


                if (!CollectionUtils.isEmpty(miErpProcessAssignees)) {
                    assignee = miErpProcessAssignees.get(0);
                } else {
                    // AIGC START
                    // 当审批人为空时，给流程管理员发送飞书消息
                    try {
                        // 获取流程实例信息
                        ProcessInstanceDo processInstanceDo = processInstanceDomainService.queryProcessInstance(execution.getProcessInstanceId());
                        if (processInstanceDo == null) {
                            log.warn("未能找到流程实例：{}", execution.getProcessInstanceId());
                            return;
                        }
                        processInstanceDomainService.loadProcessStartUser(processInstanceDo);
                        BpmUser initiator = processInstanceDo.getStartUser();

                        if (initiator == null) {
                            log.warn("未能找到流程发起人：流程实例ID {}", execution.getProcessInstanceId());
                            return;
                        }

                        // 定义流程管理员的用户ID (此处的获取方式可能需要根据实际业务逻辑进行替换)
                        // TODO: 替换为实际获取流程管理员的逻辑，例如从配置中心或特定服务获取
                        String processAdminUserId = "PLACEHOLDER_PROCESS_ADMIN_USER_ID"; // 假设这是流程管理员的userId

                        // 获取流程管理员的用户信息
                        BpmUser processAdmin = accountRemoteService.getUser(processAdminUserId);
                        if (processAdmin == null) {
                            log.error("流程管理员用户信息获取失败，ID: {}", processAdminUserId);
                            // 如果流程管理员获取失败，可以考虑发送给流程发起人或者直接返回
                            return;
                        }

                        // 构建飞书消息卡片
                        LarkMessageDo larkMessageDo = new LarkMessageDo();

                        // 标题：【异常】{发起人名称} 发起 {审批单名称} 审批节点异常，请及时处理
                        String titleZh = "【异常】" + initiator.getDisplayName() + " 发起 " + processInstanceDo.getProcessInstanceName() + " 审批节点异常，请及时处理";
                        String titleEn = "[Exception] " + initiator.getDisplayName() + " initiated " + processInstanceDo.getProcessInstanceName() + " approval node exception, please deal with it in time";
                        larkMessageDo.setTitle(new I18n(titleZh, titleEn, titleEn));
                        // 内容：{原审批单内容}+{异常原因}
                        // TODO:  originalContent此处返回的是当前流程的节点Id
                        String originalContent = (String) execution.getVariable("originalApprovalContent");
                        //异常原因 找不到审批人
                        String exceptionReason = "审批环节找不到审批人，请检查审批配置。";
                        Content content = new Content();
                        List<Map<String, String>> summaries = new ArrayList<>();
                        Map<String, String> originalSummary = new LinkedHashMap<>();
                        originalSummary.put("原审批单内容", originalContent != null ? originalContent : "N/A");
                        summaries.add(originalSummary);
                        Map<String, String> reasonSummary = new LinkedHashMap<>();
                        reasonSummary.put("异常原因", exceptionReason);
                        summaries.add(reasonSummary);
                        content.setSummaries(content); // Should be content.setSummaries(summaries);
                        larkMessageDo.setContent(content);
                        // 快捷按钮：【查看详情】
                        // 需要构建一个TaskLink或者直接构建URL
                        // 由于LarkMessageBuildAbilityImpl.buildDetailUrl需要LarkMessageDo，我将使用LarkMessageDomainService来构建Card。
                        // TaskId对于这种异常消息可能不适用，可以传递流程实例ID
                        TaskLink taskLink = TaskLink.builder()
                                .instanceId(execution.getProcessInstanceId())
                                .modelCode(processInstanceDo.getModelCode()) // 假设modelCode可以从processInstanceDo获取
                                .webView(Boolean.FALSE)
                                .build();
                        larkMessageDo.setTaskLink(taskLink); // 设置TaskLink，LarkMessageBuildAbilityImpl会使用它来构建详情URL
                        // 这里设置一个假的taskId，因为是异常通知，不对应某个具体任务
                        larkMessageDo.setTaskId(task.getId());
                        // 构建消息卡片
                        Card card = larkMessageDomainService.buildLarkMessageCard(larkMessageDo);

                        // 创建一个只包含流程管理员的临时群聊，然后发送卡片消息
                        // 由于没有直接发送卡片消息给单个用户的API，通过创建临时群组的方式发送
                        I18nText groupName = new I18nText();
                        groupName.setZhCn("【流程异常通知】" + processInstanceDo.getProcessInstanceName());
                        groupName.setEnUs("[Process Exception Notification]" + processInstanceDo.getProcessInstanceName());

                        List<String> userIds = Collections.singletonList(processAdmin.getUserName());
                        String chatId = larkAppRemoteService.createLarkGroup(groupName, userIds, initiator.getUserName()); // 群主设为发起人

                        if (StringUtils.hasText(chatId)) {
                            larkAppRemoteService.sendMessageToGroup(chatId, new CardMessage(card.toObjectForJson()));
                            log.info("已向流程管理员 {} 发送流程异常通知：流程实例ID {}", processAdmin.getUserName(), execution.getProcessInstanceId());
                        } else {
                            log.error("创建飞书群组失败，无法发送流程异常通知给流程管理员: {}", processAdmin.getUserName());
                        }
                    } catch (Exception e) {
                        log.error("发送流程异常飞书消息失败，流程实例ID: {}", execution.getProcessInstanceId(), e);
                    }
                    // AIGC END
                }
            }
        } else if (UserTaskSignType.COMPETITION.equals(signType)) {
            // 多人竞签，处理候选人而不是审批人
            Set<String> candidatesSet = new HashSet<>();
            for (AssigneeRule assigneeRule : assigneeRules) {
                List<String> candidates = assigneeFactory.buildAssignee(assigneeRule, execution);
                candidatesSet.addAll(candidates);
            }
            candidateUsers = Lists.newArrayList(candidatesSet);
            // 执行审批策略
            candidateUsers = assigneeFactory.approveStrategyHandler(candidateUsers, execution, userTaskExtension);
            assignee = null;
        }
        // 执行审批策略
        assignee = approveStrategyHandler(assignee, execution, userTaskExtension);
        super.handleAssignments(taskService, assignee, owner, candidateUsers, candidateGroups, task,
                expressionManager, execution, processEngineConfiguration);
        handleVoteAssignees(execution, userTaskExtension, task);
    }

    private String approveStrategyHandler(String assignee, DelegateExecution execution, UserTaskWrapper userTaskWrapper) {
        // 处理审批人排除策略
        assignee = executeExcludeStrategyIfNecessary(assignee, execution, userTaskWrapper);
        // 处理审批人为空策略
        assignee = executeEmptyStrategyIfNecessary(assignee, execution, userTaskWrapper);
        return assignee;
    }

    private String executeExcludeStrategyIfNecessary(String assignee, DelegateExecution execution, UserTaskWrapper userTaskWrapper) {
        List<String> assignees
                = assigneeFactory.executeExcludeStrategyIfNecessary(new ArrayList<>(Collections.singletonList(assignee)), execution, userTaskWrapper);
        if (CollectionUtils.isEmpty(assignees)) {
            return null;
        }
        // 直接返回原审批人即可
        return assignee;
    }

    private String executeEmptyStrategyIfNecessary(String assignee, DelegateExecution execution, UserTaskWrapper userTaskWrapper) {
        if (!StringUtils.isEmpty(assignee)) {
            return assignee;
        }
        // 执行审批人为空策略
        List<String> strategyAssignees
                = assigneeFactory.executeEmptyStrategyIfNecessary(new ArrayList<>(), execution, userTaskWrapper);

        if (!CollectionUtils.isEmpty(strategyAssignees)) {
            return strategyAssignees.get(0);
        }
        return assignee;
    }

    private void handleVoteAssignees(DelegateExecution execution, UserTaskWrapper userTaskWrapper,
                                     TaskEntity task) {
        UserTaskSignType signType = userTaskWrapper.getSignType();
        if (!UserTaskSignType.VOTE.equals(signType) && !UserTaskSignType.ANONYMOUS_VOTE.equals(signType)) {
            return;
        }
        List<String> vetoAssignees =
                (List<String>) execution.getVariable(BpmVariablesConstants.VARIABLE_SYS_VETO_ASSIGNEES);
        if (!CollectionUtils.isEmpty(vetoAssignees) && vetoAssignees.contains(task.getAssignee())) {
            String description = task.getDescription();
            Map<String, Object> map;
            if (StringUtils.isEmpty(description)) {
                map = new LinkedHashMap<>();
            } else {
                map = GsonUtils.fromJson(description, LinkedHashMap.class);
            }
            map.put("isVetoTask", true);
            task.setDescription(GsonUtils.toJsonFilterNullField(map));
            task.setVariableLocal(BpmVariablesConstants.VARIABLE_VOTE_IS_VETO_TASK, true);
            List<String> variable = (List<String>) execution.getVariable(BpmVariablesConstants.VARIABLE_VOTE_VETO_EXECUTION_IDS);
            if (CollectionUtils.isEmpty(variable)) {
                ArrayList<String> list = new ArrayList<>();
                list.add(task.getExecutionId());
                execution.setVariableLocal(BpmVariablesConstants.VARIABLE_VOTE_VETO_EXECUTION_IDS, list);
            } else {
                variable.add(task.getExecutionId());
                execution.setVariableLocal(BpmVariablesConstants.VARIABLE_VOTE_VETO_EXECUTION_IDS, variable);
            }
        }
    }
}
