package com.mi.oa.infra.mibpm.infra.repository.mybatis.po;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("mi_erp_process_assignee_group")
public class MiErpProcessAssigneeGroup implements java.io.Serializable {

    private Integer id;
    private String name;
    private String addTime;
    private String addUser;
    private String updateTime;
    private String updateUser;

}
