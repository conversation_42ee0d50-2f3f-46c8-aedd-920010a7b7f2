package com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.oa.infra.mibpm.common.enums.ModelType;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ModelMetaPo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/22 5:01 PM
 **/
@Mapper
@Component
public interface ModelMetaMapper extends BaseMapper<ModelMetaPo> {

    default ModelMetaPo queryByModelId(String modelCode) {
        return this.selectList(
                        new QueryWrapper<ModelMetaPo>().lambda().eq(ModelMetaPo::getModelCode, modelCode))
                .stream().max(Comparator.comparingLong(ModelMetaPo::getId)).orElse(null);
    }

    default List<ModelMetaPo> queryByStatus(boolean queryAll) {
        return this.selectList(
                new QueryWrapper<ModelMetaPo>().lambda().eq(!queryAll, ModelMetaPo::getModelStatus, 1));
    }

    default List<ModelMetaPo> queryByCategoryCode(List<String> categoryCodes) {

        return this.selectList(
                new QueryWrapper<ModelMetaPo>().lambda().in(ModelMetaPo::getCategoryCode, categoryCodes)
                        .eq(ModelMetaPo::getModelType, ModelType.MODEL_TYPE_BPMN.getId()));
    }

    /**
     * 根据name，modelCode，categoryCode分页查询ModelMetaPo，需要判断条件是否为空，并且查询分页
     */
    default Page<ModelMetaPo> queryByPage(List<String> modelCodes, String name, String modelCode, String categoryCode,
                                          int pageNum,
                                          int pageSize) {
        return this.selectPage(new Page<>(pageNum, pageSize), new QueryWrapper<ModelMetaPo>().lambda()
                .eq(StringUtils.isNotBlank(modelCode), ModelMetaPo::getModelCode, modelCode)
                .eq(StringUtils.isNotBlank(categoryCode) && !"all".equals(categoryCode), ModelMetaPo::getCategoryCode, categoryCode)
                .like(StringUtils.isNotBlank(name), ModelMetaPo::getName, name)
                .in(CollectionUtils.isNotEmpty(modelCodes), ModelMetaPo::getModelCode, modelCodes)
                .eq(ModelMetaPo::getModelType, ModelType.MODEL_TYPE_BPMN.getId()));
    }

    default List<ModelMetaPo> queryByList(String name, String modelCode, String categoryCode) {
        return this.selectList(new QueryWrapper<ModelMetaPo>().lambda()
                .eq(StringUtils.isNotBlank(modelCode), ModelMetaPo::getModelCode, modelCode)
                .eq(StringUtils.isNotBlank(categoryCode) && !"all".equals(categoryCode), ModelMetaPo::getCategoryCode, categoryCode)
                .like(StringUtils.isNotBlank(name), ModelMetaPo::getName, name)
                .eq(ModelMetaPo::getModelType, ModelType.MODEL_TYPE_BPMN.getId()));
    }

    /**
     * 根据modelCodes批量查询modelMetaPo
     */
    default List<ModelMetaPo> queryByModelCodes(List<String> modelCodes) {
        return this.selectList(new QueryWrapper<ModelMetaPo>().lambda()
                .in(ModelMetaPo::getModelCode, modelCodes)
                .eq(ModelMetaPo::getModelType, ModelType.MODEL_TYPE_BPMN.getId()));
    }

    /**
     * 根据modelCodes分页查询
     */
    default Page<ModelMetaPo> queryByModelCodesPage(List<String> modelCodes, int pageNum, int pageSize) {
        return this.selectPage(new Page<>(pageNum, pageSize), new QueryWrapper<ModelMetaPo>().lambda()
                .in(ModelMetaPo::getModelCode, modelCodes)
                .eq(ModelMetaPo::getModelType, ModelType.MODEL_TYPE_BPMN.getId()));
    }
}
