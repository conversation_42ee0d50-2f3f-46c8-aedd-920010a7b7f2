package com.mi.oa.infra.mibpm.infra.repository.mybatis.converter;

import com.mi.oa.infra.mibpm.common.constant.BpmVariablesConstants;
import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.domain.mitask.model.MiTaskDo;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.utils.SpringContextUtil;
import org.apache.commons.collections4.ListUtils;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.ProcessInstance;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/2/16 7:44 PM
 **/
@Mapper(componentModel = "spring")
public interface ProcessInstPoConverter {

    @Mappings({
            @Mapping(source = "processDefinitionKey", target = "modelCode"),
            @Mapping(source = "name", target = "processInstanceName"),
            @Mapping(source = "startUserId", target = "startUser")
    })
    ProcessInstanceDo poToDo(ProcessInstance processInstance);

    default ProcessInstanceDo poToDo(MiTaskDo miTaskDo) {
        if (miTaskDo == null) {
            return null;
        }

        ProcessInstanceDo.ProcessInstanceDoBuilder processInstanceDo = ProcessInstanceDo.builder();

        processInstanceDo.modelCode(miTaskDo.getModelCode());
        processInstanceDo.processDefinitionId(miTaskDo.getProcDefId());
        //processInstanceDo.processDefinitionName("");
        //processInstanceDo.processDefinitionVersion("");
        processInstanceDo.processInstanceId(miTaskDo.getProcInstId());
        processInstanceDo.processInstanceName(miTaskDo.getProcInstName());
        processInstanceDo.startUserId(miTaskDo.getProcInstStarter().getUserName());
        processInstanceDo.startUser(miTaskDo.getProcInstStarter());
        processInstanceDo.businessKey(miTaskDo.getBusinessKey());
        processInstanceDo.processInstanceStatus(miTaskDo.getProcessInstanceStatus());
        processInstanceDo.startTime(miTaskDo.getStartTime());
        processInstanceDo.endTime(miTaskDo.getEndTime());

        return processInstanceDo.build();
    }

    List<ProcessInstanceDo> poToDoList(List<MiTaskDo> miTaskDos);

    @Mappings({
            @Mapping(source = "processDefinitionKey", target = "modelCode"),
            @Mapping(source = "name", target = "processInstanceName"),
            @Mapping(source = "id", target = "processInstanceId"),
            @Mapping(source = "startUserId", target = "startUser"),
            @Mapping(target = "processInstanceStatus", expression = "java(getStatus(processInstance))")
    })
    ProcessInstanceDo poToDo(HistoricProcessInstance processInstance);

    default ProcessInstanceDo poToDo(HistoricProcessInstance processInstance, List<BpmUser> users) {
        if (processInstance == null) {
            return null;
        }

        ProcessInstanceDo.ProcessInstanceDoBuilder processInstanceDo = ProcessInstanceDo.builder();

        processInstanceDo.modelCode(processInstance.getProcessDefinitionKey());
        processInstanceDo.processInstanceName(processInstance.getName());
        processInstanceDo.processInstanceId(processInstance.getId());
        processInstanceDo.processDefinitionId(processInstance.getProcessDefinitionId());
        processInstanceDo.processDefinitionName(processInstance.getProcessDefinitionName());
        processInstanceDo.processDefinitionVersion(processInstance.getProcessDefinitionVersion());
        processInstanceDo.businessKey(processInstance.getBusinessKey());
        processInstanceDo.description(processInstance.getDescription());
        processInstanceDo.startTime(map(processInstance.getStartTime()));
        processInstanceDo.endTime(map(processInstance.getEndTime()));
        processInstanceDo.startUserId(processInstance.getStartUserId());
        Map<String, Object> map = processInstance.getProcessVariables();
        if (map != null) {
            processInstanceDo.processVariables(new HashMap<String, Object>(map));
        }
        processInstanceDo.processInstanceStatus(getStatus(processInstance));

        Map<String, BpmUser> startUserMap = new HashMap<>();
        startUserMap.putAll(ListUtils.emptyIfNull(users).stream().collect(Collectors.toMap(BpmUser::getUid, Function.identity(), (k1, k2) -> k1)));
        startUserMap.putAll(ListUtils.emptyIfNull(users).stream().collect(Collectors.toMap(BpmUser::getUserName, Function.identity(), (k1, k2) -> k1)));
        processInstanceDo.startUser(startUserMap.get(processInstance.getStartUserId()));

        return processInstanceDo.build();
    }

    default List<ProcessInstanceDo> poToDo(List<HistoricProcessInstance> processInstance, List<BpmUser> users) {
        if (processInstance == null) {
            return null;
        }

        List<ProcessInstanceDo> list = new ArrayList<ProcessInstanceDo>(processInstance.size());
        for (HistoricProcessInstance historicProcessInstance : processInstance) {
            list.add(poToDo(historicProcessInstance, users));
        }

        return list;
    }

    default BpmUser map(String s) {
        return ((AccountRemoteService) SpringContextUtil.getBean(AccountRemoteService.class)).getUser(s);
    }

    default ProcessInstanceStatus getStatus(HistoricProcessInstance processInstance) {
        if (null == processInstance) {
            return null;
        }
        Map<String, Object> processVariables = processInstance.getProcessVariables();
        if (null == processVariables) {
            return null;
        }
        String status = (String) processVariables.get(BpmVariablesConstants.VARIABLE_PROC_INST_STATUS);
        return ProcessInstanceStatus.getStatus(status, map(processInstance.getEndTime()));
    }

    default ZonedDateTime map(Date value) {
        if (null == value) {
            return null;
        }
        return ZonedDateTime.ofInstant(value.toInstant(), ZoneId.systemDefault());
    }
}
