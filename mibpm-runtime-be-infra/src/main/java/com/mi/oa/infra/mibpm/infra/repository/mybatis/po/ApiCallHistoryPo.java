package com.mi.oa.infra.mibpm.infra.repository.mybatis.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

/**
 * <p>
 * api服务调用记录
 * </p>
 *
 * @author: qiuzhipeng
 * @Date: 2021/12/13 18:00
 */

@Data
@Accessors(chain = true)
@TableName(value = "act_hi_api_history", autoResultMap = true)
public class ApiCallHistoryPo {

    @TableId(
            value = "id",
            type = IdType.AUTO
    )
    private Long id;

    /**
     * 所属应用
     */
    protected String appCode;

    /**
     * api id
     */
    protected String apiId;

    /**
     * 回调ID 回调唯一标识 用于幂等判断
     */
    protected String sequenceId;

    /**
     * 流程实例ID
     */
    protected String instanceId;

    /**
     * 模型code
     */
    protected String modelCode;

    /**
     * 请求地址
     */
    protected String url;

    /**
     * 请求体
     */
    protected String request;

    /**
     * 响应体
     */
    protected String response;

    /**
     * 耗时
     */
    protected Long cost;

    /**
     * 请求状态
     */
    protected Byte status;

    /**
     * 请求上下文信息
     */
    protected String callContext;

    @TableField(typeHandler = ZonedDateTimeBigIntTypeHandler.class)
    private ZonedDateTime createTime;
    @TableField(typeHandler = ZonedDateTimeBigIntTypeHandler.class)
    private ZonedDateTime updateTime;
}
