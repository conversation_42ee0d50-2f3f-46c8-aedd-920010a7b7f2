package com.mi.oa.infra.mibpm.infra.repository.deploy.manager;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import com.mi.oa.infra.mibpm.common.enums.DmnExtTypeEnum;
import com.mi.oa.infra.mibpm.common.enums.ModelType;
import com.mi.oa.infra.mibpm.common.exception.InfraException;
import com.mi.oa.infra.mibpm.infra.errorcode.ModelsErrorCodeEnum;
import com.mi.oa.infra.mibpm.infra.models.entity.DeployContext;
import com.mi.oa.infra.mibpm.infra.models.entity.ModelDo;
import lombok.extern.slf4j.Slf4j;
import org.flowable.dmn.api.DmnRepositoryService;
import org.flowable.dmn.model.DmnDefinition;
import org.flowable.dmn.xml.converter.DmnXMLConverter;
import org.flowable.editor.dmn.converter.DmnJsonConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/8/22 15:57
 */
@Slf4j
@Service
public class DmnDeployManager extends AbstractDeployManager {

    @Autowired
    private DmnRepositoryService dmnRepositoryService;

    /**
     * jackson object mapper
     * thread-safe
     */
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private DmnXMLConverter dmnXmlConverter = new DmnXMLConverter();
    private DmnJsonConverter dmnJsonConverter = new DmnJsonConverter();

    static {
        OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.ALWAYS);
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, Boolean.FALSE);
    }

    @Override
    public ModelType support() {
        return ModelType.MODEL_TYPE_DECISION_TABLE;
    }

    @Override
    public void verify(DeployContext deployContext) {

    }

    @Override
    public void postProcessBeforeDeploy(DeployContext deployContext) {

        ModelDo model = deployContext.getModel();
        // obtain xml
        JsonNode editorJsonNode = null;
        try {
            editorJsonNode = OBJECT_MAPPER.readTree(model.getModelEditorContent());
            // 将模型ID回写到dmn json 中
            ((ObjectNode) editorJsonNode).set("id", new TextNode(model.getModelId()));

            preHandDmnJson(editorJsonNode);

            deployContext.setDmnJson(editorJsonNode);
        } catch (Exception e) {
            throw new InfraException(ModelsErrorCodeEnum.DEPLOY_DMN_MODEL_ERROR,
                    model.getModelCode());
        }
    }

    @Override
    public void deploy(DeployContext deployContext) {
        ModelDo model = deployContext.getModel();
        try {
            DmnDefinition dmnDefinition = dmnJsonConverter.convertToDmn(deployContext.getDmnJson(),
                    model.getModelCode(),
                    model.getVersion(), new Date());

            byte[] xmlBytes = dmnXmlConverter.convertToXML(dmnDefinition);
            // deploy
            dmnRepositoryService.createDeployment()
                    .addDmnBytes(String.join(".", deployContext.getModel().getModelCode(),
                            ModelType.MODEL_TYPE_DECISION_TABLE.getPrefix()), xmlBytes)
                    .name(model.getName())
                    .deploy();
        } catch (Exception e) {
            log.error("deploy dmn model error", e);
            throw new InfraException(ModelsErrorCodeEnum.DEPLOY_DMN_MODEL_ERROR,
                    model.getModelCode());
        }
    }

    public static void preHandDmnJson(JsonNode editorJsonNode) {
        Map<String, Set<String>> inputTypeMap = new HashMap<>();
        // 兼容输入扩展类型
        JsonNode inputExpressions = editorJsonNode.path("inputExpressions");
        if (inputExpressions instanceof ArrayNode) {
            inputExpressions.forEach((inputExpression) -> {
                String type = inputExpression.path("type").asText();
                String id = inputExpression.path("id").asText();
                if (DmnExtTypeEnum.USER.getCode().equals(type) && inputExpression instanceof ObjectNode) {
                    ((ObjectNode) inputExpression).set("type", new TextNode("string"));
                }
                Set<String> keys = inputTypeMap.getOrDefault(type, new HashSet<>());
                keys.add(id);
                inputTypeMap.put(type, keys);
            });
        }
        // 兼容输出扩展类型
        JsonNode outputExpressions = editorJsonNode.path("outputExpressions");
        if (outputExpressions instanceof ArrayNode) {
            outputExpressions.forEach((outputExpression) -> {
                String type = outputExpression.path("type").asText();
                if (DmnExtTypeEnum.codes().contains(type) && outputExpression instanceof ObjectNode) {
                    ((ObjectNode) outputExpression).set("type", new TextNode("string"));
                }
            });
        }
        // 处理规则
        JsonNode rules = editorJsonNode.path("rules");
        if (rules instanceof ArrayNode) {
            final Set<String> collectionSet = inputTypeMap.get("collection");
            rules.forEach((rule) -> {
                rule.fields().forEachRemaining((entry) -> {
                    final String key = entry.getKey();
                    final JsonNode node = entry.getValue();

                    if (node instanceof TextNode) {
                        String value = node.asText().trim();

                        // 处理集合类型数据 补充,
                        if (CollectionUtils.isNotEmpty(collectionSet)
                                && collectionSet.contains(key.replace("_expression", ""))
                                && !value.contains(",")
                                && !"-".equals(value)) {
                            value = value + ",";
                        }

                        entry.setValue(new TextNode(value));
                    }
                });
            });
        }
    }
}
