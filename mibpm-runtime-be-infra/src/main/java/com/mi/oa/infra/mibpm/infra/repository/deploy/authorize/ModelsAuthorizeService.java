package com.mi.oa.infra.mibpm.infra.repository.deploy.authorize;

import com.mi.oa.infra.mibpm.common.enums.AuthorizeDimension;
import com.mi.oa.infra.mibpm.common.enums.ModelType;
import com.mi.oa.infra.mibpm.infra.models.entity.ModelDo;
import com.mi.oa.infra.mibpm.infra.remote.sdk.ModelsAuthorityRemote;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ModelMetaPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import javax.annotation.Nullable;

@Slf4j
@Service
public class ModelsAuthorizeService {

    private final Map<ModelType, AuthorizeDimension> userDimensionMap = new ConcurrentHashMap<>();
    private final Map<ModelType, AuthorizeDimension> checkerDimensionMap = new ConcurrentHashMap<>();
    private final Map<ModelType, AuthorizeDimension> ownerDimensionMap = new ConcurrentHashMap<>();

    @Autowired
    private ModelsAuthorityRemote modelsAuthorityRemote;

    public ModelsAuthorizeService() {
        ownerDimensionMap.put(ModelType.MODEL_TYPE_BPMN, AuthorizeDimension.BPMN_OWNER);
        ownerDimensionMap.put(ModelType.MODEL_TYPE_FORM, AuthorizeDimension.FORM_OWNER);
        ownerDimensionMap.put(ModelType.MODEL_TYPE_DECISION_TABLE, AuthorizeDimension.DMN_OWNER);

        userDimensionMap.put(ModelType.MODEL_TYPE_BPMN, AuthorizeDimension.BPMN_USER);
        userDimensionMap.put(ModelType.MODEL_TYPE_FORM, AuthorizeDimension.FORM_USER);
        userDimensionMap.put(ModelType.MODEL_TYPE_DECISION_TABLE, AuthorizeDimension.DMN_USER);

        checkerDimensionMap.put(ModelType.MODEL_TYPE_BPMN, AuthorizeDimension.BPMN_CHECKER);
    }

    /**
     * 处理模型权限的变更
     *
     * @param latestModel 最新的模型
     * @param modelInDb 已存在的模型
     */
    public void handleModelAuthorizeChange(ModelDo latestModel, @Nullable ModelMetaPo modelInDb) {
        if (latestModel.getModelType() != ModelType.MODEL_TYPE_BPMN) {
            return;
        }

        AuthorizeDimension userDimension = userDimensionMap.get(latestModel.getModelType());
        AuthorizeDimension checkerDimension = checkerDimensionMap.get(latestModel.getModelType());
        AuthorizeDimension ownerDimension = ownerDimensionMap.get(latestModel.getModelType());
        String modelId = latestModel.getModelCode();

        // 检查资源是否存在
        if (checkerDimension != null) {
            if (!modelsAuthorityRemote.existDataResource(modelId, checkerDimension)) {
                modelsAuthorityRemote.createDataResource(modelId, latestModel.getName(), checkerDimension);
            }
        }
        if (!modelsAuthorityRemote.existDataResource(modelId, userDimension)) {
            modelsAuthorityRemote.createDataResource(modelId, latestModel.getName(), userDimension);
        }
        if (ownerDimension != null && !modelsAuthorityRemote.existDataResource(modelId, ownerDimension)) {
            modelsAuthorityRemote.createDataResource(modelId, latestModel.getName(), ownerDimension);
        }

        // 增量授权
        List<String> increaseChecker = calculateIncreaseElement(latestModel.getChecker(), modelInDb == null ? null : modelInDb.getChecker());
        // 对模型稽查员授权
        if (null != checkerDimension && CollectionUtils.isNotEmpty(increaseChecker)) {
            modelsAuthorityRemote.assignDataPermission(modelId, increaseChecker, checkerDimension);
        }
        // 对模型负责人授权
        List<String> increaseOwners = calculateIncreaseElement(latestModel.getOwners(), modelInDb == null ? null : modelInDb.getOwners());
        if (ownerDimension != null && CollectionUtils.isNotEmpty(increaseOwners)) {
            modelsAuthorityRemote.assignDataPermission(modelId, increaseOwners, ownerDimension);
        }
        // 对模型使用人授权 增量数据
        List<String> increaseVisibleUsers = calculateIncreaseElement(latestModel.getVisibleConfig().getUsers(), modelInDb == null ? null : modelInDb.getVisibleConfig().getUsers());
        List<String> increaseVisibleDept = calculateIncreaseElement(latestModel.getVisibleConfig().getDepartments(), modelInDb == null ? null : modelInDb.getVisibleConfig().getDepartments());
        if (CollectionUtils.isNotEmpty(increaseVisibleUsers)) {
            modelsAuthorityRemote.assignDataPermission(modelId, increaseVisibleUsers, userDimension);
        }
        if (CollectionUtils.isNotEmpty(increaseVisibleDept)) {
            modelsAuthorityRemote.assignOrgDataPermission(modelId, increaseVisibleDept, userDimension);
        }

        // 撤销授权
        // 撤销失效的模型稽查员数据权限
        List<String> expiredChecker = calculateExpiredElement(latestModel.getChecker(), modelInDb == null ? null : modelInDb.getChecker());
        if (null != checkerDimension && CollectionUtils.isNotEmpty(expiredChecker)) {
            modelsAuthorityRemote.revokeDataPermission(modelId, expiredChecker, checkerDimension);
        }
        // 撤销失效的管理员权限
        List<String> expiredOwners = calculateExpiredElement(latestModel.getOwners(), modelInDb == null ? null : modelInDb.getOwners());
        if (null != ownerDimension && CollectionUtils.isNotEmpty(expiredOwners)) {
            modelsAuthorityRemote.revokeDataPermission(modelId, expiredOwners, ownerDimension);
        }

        List<String> expiredVisibleUsers = calculateExpiredElement(latestModel.getVisibleConfig().getUsers(), modelInDb == null ? null : latestModel.getVisibleConfig().getUsers());
        List<String> expiredVisibleDept = calculateExpiredElement(latestModel.getVisibleConfig().getDepartments(), modelInDb == null ? null : latestModel.getVisibleConfig().getDepartments());
        if (CollectionUtils.isNotEmpty(expiredVisibleUsers)) {
            modelsAuthorityRemote.revokeDataPermission(modelId, expiredVisibleUsers, userDimension);
        }
        if (CollectionUtils.isNotEmpty(expiredVisibleDept)) {
            modelsAuthorityRemote.revokeOrgDataPermission(modelId, expiredVisibleDept, userDimension);
        }
    }

    private List<String> calculateExpiredElement(List<String> list, List<String> oldList) {
        List<String> expiredElements = new LinkedList<>();
        if (CollectionUtils.isEmpty(oldList)) {
            return expiredElements;
        }
        if (CollectionUtils.isEmpty(list)) {
            expiredElements.addAll(oldList);
            return expiredElements;
        }
        for (String owner : oldList) {
            if (!list.contains(owner)) {
                expiredElements.add(owner);
            }
        }
        return expiredElements;
    }

    private List<String> calculateIncreaseElement(List<String> list, List<String> oldList) {
        return calculateExpiredElement(oldList, list);
    }
}
