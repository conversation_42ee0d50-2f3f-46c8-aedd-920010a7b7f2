package com.mi.oa.infra.mibpm.infra.repository.mybatis.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/7 10:56 AM
 **/
@TableName("act_hi_procinst_pin")
@Data
public class ActHiProcInstPinPo {

    @TableId(value = "ID_", type = IdType.AUTO)
    private Integer id;
    @TableField("PROC_INST_ID_")
    private String procInstId;
    @TableField("USER_ID_")
    private String userId;
    @TableField("STATUS_")
    private String status;
    @TableField("UPDATE_TIME_")
    private Date updateTime;
}

