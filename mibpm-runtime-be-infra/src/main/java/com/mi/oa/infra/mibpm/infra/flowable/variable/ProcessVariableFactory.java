package com.mi.oa.infra.mibpm.infra.flowable.variable;

import java.util.Map;

/**
 * 流程变量服务
 *
 * <AUTHOR>
 * @date 2022/3/8 15:46
 */
public interface ProcessVariableFactory {

    /**
     * 构建显示设置的变量及其衍生参数和系统内置参数装，然后填充到流程实例领域对象
     *
     * @param processDefinitionId 流程定义名称
     * @param delegateVariables   流程变量
     * @param updateSummary       是否需要更新摘要信息（form 为空则不更新）
     * @return 构建好的流程变量
     */
    Map<String, Object> buildVariable(String processDefinitionId, Map<String, Object> delegateVariables, boolean updateSummary);

    /**
     * 构建流程实例的名称
     *
     * @param processDefinitionId 流程定义名称
     * @param delegateVariables   流程变量
     * @return 流程实例的名称
     */
    String buildProcessInstanceName(String processDefinitionId, Map<String, Object> delegateVariables);
}
