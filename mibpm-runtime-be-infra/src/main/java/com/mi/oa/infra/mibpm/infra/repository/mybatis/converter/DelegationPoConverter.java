package com.mi.oa.infra.mibpm.infra.repository.mybatis.converter;

import com.mi.oa.infra.mibpm.domain.userconfig.model.DelegationConfig;
import com.mi.oa.infra.mibpm.domain.userconfig.model.DelegationConfig.DelegationConfigBuilder;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.DelegationPo;
import com.mi.oa.infra.mibpm.utils.IdentityUtil;
import org.mapstruct.Mapper;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: qiuzhipeng
 * @Date: 2022/1/17 16:18
 */

@Mapper(componentModel = "spring")
public interface DelegationPoConverter {

    default DelegationConfig poToDo(DelegationPo delegationPo) {
        if (delegationPo == null) {
            return null;
        }

        DelegationConfigBuilder delegationDo = DelegationConfig.builder();

        if (delegationPo.getId() != null) {
            delegationDo.id(delegationPo.getId());
        }
        delegationDo.userId(delegationPo.getUserId());
        delegationDo.delegationUserId(delegationPo.getDelegationUserId());
        List<String> list = delegationPo.getModelCode();
        if (list != null) {
            delegationDo.modelCode(new ArrayList<String>(list));
        }
        delegationDo.startTime(ZonedDateTime.ofInstant(delegationPo.getStartTime().toInstant(), ZoneId.systemDefault()));
        delegationDo.endTime(ZonedDateTime.ofInstant(delegationPo.getEndTime().toInstant(), ZoneId.systemDefault()));
        delegationDo.type(delegationPo.getType());
        delegationDo.status(delegationPo.getStatus());
        delegationDo.updateTime(ZonedDateTime.ofInstant(delegationPo.getUpdateTime().toInstant(), ZoneId.systemDefault()));
        delegationDo.updateUser(delegationPo.getUpdateUser());
        delegationDo.delegationReason(delegationPo.getDelegationReason());
        delegationDo.delegationScope(delegationPo.getDelegationScope());
        delegationDo.initiatingDeptCodes(delegationPo.getInitiatingDeptCodes());
        delegationDo.exclusionModelCodes(delegationPo.getExclusionModelCodes());
        delegationDo.createTime(ZonedDateTime.ofInstant(delegationPo.getCreateTime().toInstant(), ZoneId.systemDefault()));
        return delegationDo.build();
    }

    default DelegationPo doToPo(DelegationConfig delegationDo) {
        if (delegationDo == null) {
            return null;
        }

        DelegationPo delegationPo = new DelegationPo();

        if (delegationDo.getId() != null) {
            delegationPo.setId(delegationDo.getId().longValue());
        }
        delegationPo.setUserId(delegationDo.getUserId());
        delegationPo.setDelegationUserId(delegationDo.getDelegationUserId());
        List<String> list = delegationDo.getModelCode();
        if (list != null) {
            delegationPo.setModelCode(new ArrayList<String>(list));
        }
        if (delegationDo.getStartTime() != null) {
            delegationPo.setStartTime(Date.from(delegationDo.getStartTime().toInstant()));
        }
        if (delegationDo.getEndTime() != null) {
            delegationPo.setEndTime(Date.from(delegationDo.getEndTime().toInstant()));
        }
        delegationPo.setType(delegationDo.getType());
        delegationPo.setStatus(delegationDo.getStatus());
        delegationPo.setUpdateTime(Date.from(ZonedDateTime.now().toInstant()));
        delegationPo.setUpdateUser(IdentityUtil.currentUserName());

        return delegationPo;
    }

    default List<DelegationConfig> poListToDoList(List<DelegationPo> delegationPos) {
        if (delegationPos == null) {
            return null;
        }
        List<DelegationConfig> delegationDos = new ArrayList<>();
        for (DelegationPo delegationPo : delegationPos) {
            delegationDos.add(this.poToDo(delegationPo));
        }
        return delegationDos;
    }
}
