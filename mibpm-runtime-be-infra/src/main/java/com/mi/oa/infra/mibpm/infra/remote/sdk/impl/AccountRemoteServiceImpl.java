package com.mi.oa.infra.mibpm.infra.remote.sdk.impl;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mi.flowable.rpc.x5.X5Caller;
import com.mi.flowable.rpc.x5.X5Response;
import com.mi.oa.infra.mibpm.common.model.BpmOrg;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.infra.remote.entity.UserReportLineEntity;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.oaucf.idm.api.IdmAccountService;
import com.mi.oa.infra.oaucf.idm.api.IdmReportLineService;
import com.mi.oa.infra.oaucf.idm.api.IdmUserService;
import com.mi.oa.infra.oaucf.idm.api.rep.ReportLineDto;
import com.mi.oa.infra.oaucf.idm.api.rep.Resp;
import com.mi.oa.infra.oaucf.idm.api.rep.UserBaseInfoDto;
import com.mi.oa.infra.oaucf.idm.api.rep.UserDo;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import java.io.IOException;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/14 3:14 PM
 **/
@Slf4j
//@Service
public class AccountRemoteServiceImpl implements AccountRemoteService {

    @Autowired
    private IdmAccountService idmAccountService;
    @Autowired
    private IdmReportLineService idmReportLineService;
    @Autowired
    private IdmUserService idmUserService;

    @Value("${hrod.appId}")
    private String hrodAppId;
    @Value("${hrod.appKey}")
    private String hrodAppKey;
    @Value("${hrod.host}")
    private String hrodHost;

    public static final int USER_NAME_MAX_LENGTH = 20;
    public static final String ON_JOB_STATUS = "A";
    /**
     * 主岗
     */
    private static final String MAIN_JOB = "0";

    private final CacheLoader<String, Optional<BpmUser>> cacheLoader = new CacheLoader<String, Optional<BpmUser>>() {
        @Override
        public Optional<BpmUser> load(String key) {
            List<BpmUser> bpmUsers = listUsers(Collections.singleton(key));
            if (CollectionUtils.isNotEmpty(bpmUsers)) {
                return Optional.ofNullable(bpmUsers.get(0));
            }
            return Optional.empty();
        }
    };
    private final LoadingCache<String, Optional<BpmUser>> cache = CacheBuilder.newBuilder()
            .expireAfterAccess(1, TimeUnit.HOURS)
            .build(cacheLoader);


    @Override
    public List<BpmUser> listUserReportLine(String userName) {
        String uid;
        if (userName.length() > USER_NAME_MAX_LENGTH) {
            uid = userName;
        } else {
            uid = idmAccountService.findUidByUserName(userName).getData();
        }
        List<ReportLineDto> data = idmReportLineService.findReportLineByUid(uid, MAIN_JOB).getData();
        log.info("idm获取汇报线, uid={}, data={}", uid, GsonUtils.toJsonWtihNullField(data));
        if (null != data) {
            return data.stream().map(this::buildUser).collect(Collectors.toList());
        } else {
            return Lists.newArrayList();
        }
    }

    @Override
    public BpmUser getUser(String userName) {
        if (StringUtils.isBlank(userName)) {
            return null;
        }
        try {
            Optional<BpmUser> bpmUser = cache.get(userName);
            return bpmUser.orElse(null);
        } catch (Exception e) {
            log.error("远程调用IDM获取用户信息异常", e);
            return null;
        }

    }

    @Override
    public List<BpmUser> listUsers(Collection<String> userNames) {
        return listUser(userNames, false);
    }

    /**
     * 加载用户职级信息
     * 通过hrod获取
     *
     * @param user
     */
    @Override
    public void loadUserRankInfo(BpmUser user) {

        String employeeId = user.getPersonId();
        if (StringUtils.isBlank(employeeId)) {
            user.setUserRank("0");
            return;
        }
        X5Caller hrodCaller = new X5Caller(hrodAppId, hrodAppKey, hrodHost);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("api_name", "getEmployeeInfoForBpm");

        Map<String, Object> subParam = new HashMap<>();
        subParam.put("emplid", employeeId);
        Gson gson = new Gson();
        paramMap.put("hpcConditionByEmplid", gson.toJson(subParam));
        try {
            X5Response<Map> response = hrodCaller.call("/hapi/HR_C", paramMap, new TypeToken<X5Response<Map>>() {
            });

            if (response.isOk()) {
                Map body = response.getBody();
                Map data = (Map) body.get("data");
                if (Objects.nonNull(data)) {
                    user.setUserRank((String) data.get("supv_lvl_id"));
                    return;
                }
            }
        } catch (IOException e) {
            log.error("加载职级信息异常，user={}", user.getUserName(), e);
        }
        user.setUserRank("0");
    }

    @NotNull
    private BpmUser getMiUserEntity(UserBaseInfoDto data) {
        BpmUser user = new BpmUser();
        user.setUid(data.getUid());
        user.setUserName(data.getUserName());
        user.setDisplayName(data.getName());
        user.setPersonId(data.getPersonId());
        user.setHrStatus(data.getHrStatus());
        user.setOrg(BpmOrg.builder().orgId(data.getDeptId()).orgDesc(data.getDeptDescr())
                .fullOrgDesc(data.getFullDeptDescr()).build());
        return user;
    }

    private List<BpmUser> listUser(Collection<String> userNames, boolean filterOnJob) {
        List<String> uidList = Lists.newArrayList();
        List<String> userNameList = Lists.newArrayList();
        Set<String> userNamesSet = userNames.stream().filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        for (String userName : userNamesSet) {
            if (userName.length() > USER_NAME_MAX_LENGTH) {
                uidList.add(userName);
            } else {
                userNameList.add(userName);
            }
        }
        List<BpmUser> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(userNameList)) {
            String req = String.join(",", userNameList);
            Resp<List<UserDo>> resp = idmUserService.findUidsByUserNames(req);
            if (resp.getCode() == 0) {
                List<UserDo> data = resp.getData();
                List<String> collect = data.stream().map(UserDo::getUid).collect(Collectors.toList());
                uidList.addAll(collect);
            }
        }
        if (CollectionUtils.isNotEmpty(uidList)) {
            String uids = String.join(",", uidList);
            Resp<List<UserBaseInfoDto>> userList = idmUserService.findUserInfoListByUidList(uids);
            if (userList.getCode() == 0) {
                for (UserBaseInfoDto i : userList.getData()) {
                    BpmUser user = getMiUserEntity(i);
                    if (filterOnJob && !i.getHrStatus().equals(ON_JOB_STATUS)) {
                        continue;
                    }
                    result.add(user);
                }
            }
        }
        return result;
    }

    private UserReportLineEntity buildUser(ReportLineDto userDo) {
        UserReportLineEntity user = new UserReportLineEntity();
        if (null != userDo) {
            user.setUid(userDo.getUid());
            user.setDisplayName(userDo.getDisplayName());
            user.setUserName(userDo.getUserName());
            user.setLevel(userDo.getLevel());
            user.setPostType(userDo.getPostType());
            user.setHrStatus(userDo.getHrStatus());
        }
        return user;
    }


}
