package com.mi.oa.infra.mibpm.infra.flowable.assign.impl;

import com.mi.oa.infra.mibpm.flowable.extension.model.AssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.AssigneeRuleType;
import com.mi.oa.infra.mibpm.flowable.extension.model.MultipleUserAssigneeRule;
import com.mi.oa.infra.mibpm.flowable.extension.model.SingleUserAssigneeRule;
import com.mi.oa.infra.mibpm.infra.flowable.assign.AbstractAssigneeService;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.MiErpProcessAssignee;
import org.flowable.engine.delegate.DelegateExecution;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * static
 *
 * <AUTHOR>
 * @description
 * @date 2022/3/9 4:36 PM
 **/
@Service
public class StaticAssigneeServiceImpl extends AbstractAssigneeService {

    @Override
    public AssigneeRuleType getAssigneeServiceKey() {
        return AssigneeRuleType.SINGLE_USER;
    }

    @Override
    public List<String> buildAssignee(AssigneeRule assigneeRule, DelegateExecution execution) {
        List<String> resultList = new ArrayList<>();
        resultList.add(((SingleUserAssigneeRule) assigneeRule).getUser());
        return resultList;
    }

    @Override
    public List<String> buildAssignee(AssigneeRule assigneeRule, String processDefinitionId, Map<String, Object> variables) {
        List<String> resultList = new ArrayList<>();
        resultList.add(((SingleUserAssigneeRule) assigneeRule).getUser());
        return resultList;
    }

    @Override
    public AssigneeRule convert(MiErpProcessAssignee miErpProcessAssignee) {
        MultipleUserAssigneeRule multipleUserAssigneeRule = new MultipleUserAssigneeRule();
        ArrayList<String> list = new ArrayList<>(Arrays.asList(miErpProcessAssignee.getValue().split(",")));
        multipleUserAssigneeRule.setUsers(list);
        return multipleUserAssigneeRule;
    }
}
