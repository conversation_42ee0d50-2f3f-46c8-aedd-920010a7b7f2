package com.mi.oa.infra.mibpm.infra.repository.mybatis.converter;

import com.mi.oa.infra.mibpm.domain.apicall.model.ApiCallHistoryDo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ApiCallHistoryPo;
import org.mapstruct.Mapper;

/**
 * @author: qiuzhipeng
 * @Date: 2022/2/10 10:40
 */
@Mapper(componentModel = "spring")
public interface ApiCallHistoryPoConverter {

    ApiCallHistoryDo poToDo(ApiCallHistoryPo apiCallHistoryPo);

    ApiCallHistoryPo doToPo(ApiCallHistoryDo apiCallHistoryDo);
}
