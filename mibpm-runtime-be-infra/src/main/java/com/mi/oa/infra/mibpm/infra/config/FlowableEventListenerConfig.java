package com.mi.oa.infra.mibpm.infra.config;

import com.mi.oa.infra.mibpm.eventbus.EventPublisher;
import com.mi.oa.infra.mibpm.infra.flowable.listener.NotifiedTaskListener;
import com.mi.oa.infra.mibpm.infra.flowable.listener.ProxyFlowableEventListener;
import com.mi.oa.infra.mibpm.infra.flowable.listener.SignTaskListener;
import com.mi.oa.infra.mibpm.infra.flowable.listener.TaskCreatedListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/8/1 12:34
 */
@Configuration
public class FlowableEventListenerConfig {

    @Bean
    public ProxyFlowableEventListener proxyFlowableEventListener(EventPublisher eventPublisher) {
        return new ProxyFlowableEventListener(eventPublisher);
    }

    @Bean
    public NotifiedTaskListener notifiedTaskListener() {
        return new NotifiedTaskListener();
    }

    @Bean
    public TaskCreatedListener taskCreatedListener(@Value("${rocketmq.task-timeout-topic}") String taskTimeoutTopic) {
        return new TaskCreatedListener(taskTimeoutTopic);
    }

    @Bean
    public SignTaskListener signTaskListener() {
        return new SignTaskListener();
    }

}
