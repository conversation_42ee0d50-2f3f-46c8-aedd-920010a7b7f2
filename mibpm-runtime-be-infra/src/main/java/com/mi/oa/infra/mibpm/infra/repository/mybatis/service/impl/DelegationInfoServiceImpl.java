package com.mi.oa.infra.mibpm.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.DelegationInfoMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.DelegationPo;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.service.DelegationInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/23
 * @Description
 */
@Service
public class DelegationInfoServiceImpl extends ServiceImpl<DelegationInfoMapper, DelegationPo>
        implements DelegationInfoService {

    @Autowired
    private DelegationInfoMapper delegationInfoMapper;

    @Override
    public List<DelegationPo> selectByDelegationProcessInstanceId(String delegationProcessInstanceId) {
        return delegationInfoMapper.selectByDelegationProcessInstanceId(delegationProcessInstanceId);
    }

    @Override
    public List<DelegationPo> selectByDelegatorId(String delegatorId) {
        return delegationInfoMapper.selectByDelegatorId(delegatorId);
    }

    @Override
    public List<DelegationPo> selectByApproverId(String approverId) {
        return delegationInfoMapper.selectByApproverId(approverId);
    }

    @Override
    public Integer updateStatusByProcessInstanceId(String delegationProcessInstanceId, Integer status, String currentUserId) {
        return delegationInfoMapper.updateStatusByProcessInstanceId(delegationProcessInstanceId, status, currentUserId);
    }

    @Override
    public void updateStatusByIds(List<Long> ids, Integer status) {
        for (Long id : ids) {
            delegationInfoMapper.updateStatusById(id, status);
        }
    }

    @Override
    public IPage<DelegationPo> selectPage(IPage<DelegationPo> page, Wrapper<DelegationPo> queryWrapper) {
        // 调用 Mapper 层的 selectPage 方法执行分页查询
        return this.delegationInfoMapper.selectPage(page, queryWrapper);
    }
}
