package com.mi.oa.infra.mibpm.domain.operation.constructor;

import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.OperateTerminatedEvent;
import com.mi.oa.infra.mibpm.domain.operation.model.OperationHistoryDo;
import com.mi.oa.infra.mibpm.domain.operation.service.OperationHistoryConstructor;
import com.mi.oa.infra.mibpm.eventbus.Event;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/14 15:53
 **/
public class OperationHistoryTerminatedConstructor implements OperationHistoryConstructor {
    private final EventIdentify operation = EventIdentify.OPERATE_TERMINATED;

    @Override
    public String name() {
        return this.operation.name();
    }


    @Override
    public OperationHistoryDo build(Event event) {
        if (event instanceof OperateTerminatedEvent) {
            OperateTerminatedEvent approvedEvent = (OperateTerminatedEvent) event;
            OperationHistoryDo.OperationHistoryDoBuilder builder = OperationHistoryDo.builder();
            builder.createUser(approvedEvent.getOperator());
            builder.operation(this.operation).processInstId(approvedEvent.getProcessInstanceId())
                    .assignee(approvedEvent.getOperator())
                    .comment(approvedEvent.getComment());
            return builder.build();
        }
        return null;
    }
}
