package com.mi.oa.infra.mibpm.domain.operation.constructor;

import com.google.common.collect.Lists;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.OperateDelegatedEvent;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.domain.operation.model.OperationHistoryDo;
import com.mi.oa.infra.mibpm.domain.operation.service.OperationHistoryConstructor;
import com.mi.oa.infra.mibpm.eventbus.Event;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/14 15:53
 **/
public class OperationHistoryDelegateConstructor implements OperationHistoryConstructor {
    private final EventIdentify operation = EventIdentify.OPERATE_DELEGATED;

    @Override
    public String name() {
        return this.operation.name();
    }


    @Override
    public OperationHistoryDo build(Event event) {
        if (event instanceof OperateDelegatedEvent) {
            OperateDelegatedEvent approvedEvent = (OperateDelegatedEvent) event;
            OperationHistoryDo.OperationHistoryDoBuilder builder = OperationHistoryDo.builder();
            builder.createUser(approvedEvent.getOperator());
            builder.targetUser(Lists.newArrayList(BpmUser.builder().uid(approvedEvent.getDelegateTo()).build()));
            builder.operation(this.operation).processInstId(approvedEvent.getProcessInstanceId())
                    .taskName(approvedEvent.getTaskName()).taskId(approvedEvent.getTaskId())
                    .taskDefKey(approvedEvent.getTaskDefinitionKey()).assignee(approvedEvent.getOperator())
                    .comment(approvedEvent.getComment());
            return builder.build();
        }
        return null;
    }
}
