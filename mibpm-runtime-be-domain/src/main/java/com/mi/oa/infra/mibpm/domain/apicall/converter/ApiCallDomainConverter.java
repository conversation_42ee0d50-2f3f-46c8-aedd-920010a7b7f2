package com.mi.oa.infra.mibpm.domain.apicall.converter;

import com.mi.oa.infra.mibpm.common.model.TaskLog;
import com.mi.oa.infra.mibpm.domain.apicall.dto.RemoteCallProcessInstanceDto;
import com.mi.oa.infra.mibpm.domain.apicall.dto.RemoteCallTaskDto;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import org.mapstruct.Mapper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: qiuzhipeng
 * @Date: 2022/3/14 16:30
 */
@Mapper(componentModel = "spring")
public interface ApiCallDomainConverter {

    default RemoteCallProcessInstanceDto toRemoteCallProcessInstanceDto(ProcessInstanceDo processInstanceDo){
        if ( processInstanceDo == null ) {
            return null;
        }

        RemoteCallProcessInstanceDto remoteCallProcessInstanceDto = new RemoteCallProcessInstanceDto();

        remoteCallProcessInstanceDto.setCategoryCode( processInstanceDo.getCategoryCode() );
        remoteCallProcessInstanceDto.setModelCode( processInstanceDo.getModelCode() );
        remoteCallProcessInstanceDto.setProcessDefinitionId( processInstanceDo.getProcessDefinitionId() );
        remoteCallProcessInstanceDto.setProcessDefinitionName( processInstanceDo.getProcessDefinitionName() );
        remoteCallProcessInstanceDto.setProcessDefinitionVersion( processInstanceDo.getProcessDefinitionVersion() );
        remoteCallProcessInstanceDto.setBusinessKey( processInstanceDo.getBusinessKey() );
        remoteCallProcessInstanceDto.setProcessInstanceId( processInstanceDo.getProcessInstanceId() );
        remoteCallProcessInstanceDto.setProcessInstanceName( processInstanceDo.getProcessInstanceName() );
        remoteCallProcessInstanceDto.setProcessInstanceStatus( processInstanceDo.getProcessInstanceStatus() );
        remoteCallProcessInstanceDto.setDescription( processInstanceDo.getDescription() );
        remoteCallProcessInstanceDto.setStartTime( processInstanceDo.getStartTime() );
        remoteCallProcessInstanceDto.setEndTime( processInstanceDo.getEndTime() );
        remoteCallProcessInstanceDto.setStartUserId( processInstanceDo.getStartUserId() );
        remoteCallProcessInstanceDto.setStartUser( processInstanceDo.getStartUser() );

        remoteCallProcessInstanceDto.setOperator( processInstanceDo.getOperator() );

        return remoteCallProcessInstanceDto;
    }

    default RemoteCallTaskDto toRemoteCallTaskDto(TaskDo taskDo){
        if ( taskDo == null ) {
            return null;
        }

        RemoteCallTaskDto remoteCallTaskDto = new RemoteCallTaskDto();

        remoteCallTaskDto.setCategoryCode( taskDo.getCategoryCode() );
        remoteCallTaskDto.setTaskId( taskDo.getTaskId() );
        remoteCallTaskDto.setTaskName( taskDo.getTaskName() );
        remoteCallTaskDto.setDescription( taskDo.getDescription() );
        remoteCallTaskDto.setPriority( taskDo.getPriority() );
        remoteCallTaskDto.setOwner( taskDo.getOwner() );
        remoteCallTaskDto.setAssignee( taskDo.getAssignee() );
        remoteCallTaskDto.setProcessInstanceId( taskDo.getProcessInstanceId() );
        remoteCallTaskDto.setExecutionId( taskDo.getExecutionId() );
        remoteCallTaskDto.setTaskDefinitionId( taskDo.getTaskDefinitionId() );
        remoteCallTaskDto.setProcessDefinitionId( taskDo.getProcessDefinitionId() );
        remoteCallTaskDto.setScopeId( taskDo.getScopeId() );
        remoteCallTaskDto.setCreateTime( taskDo.getCreateTime() );
        remoteCallTaskDto.setEndTime( taskDo.getEndTime() );
        remoteCallTaskDto.setTaskDefinitionKey( taskDo.getTaskDefinitionKey() );
        remoteCallTaskDto.setDelegationState( taskDo.getDelegationState() );
        remoteCallTaskDto.setDueDate( taskDo.getDueDate() );
        remoteCallTaskDto.setTaskAttribute( taskDo.getTaskAttribute() );
        Map<String, Object> map1 = taskDo.getTaskVariables();
        if ( map1 != null ) {
            remoteCallTaskDto.setTaskVariables( new HashMap<String, Object>( map1 ) );
        }
        List<TaskLog> list = taskDo.getTaskLogs();
        if ( list != null ) {
            remoteCallTaskDto.setTaskLogs( new ArrayList<TaskLog>( list ) );
        }
        remoteCallTaskDto.setOperator( taskDo.getOperator() );
        remoteCallTaskDto.setRevision( taskDo.getRevision() );
        remoteCallTaskDto.setUserTaskWrapper( taskDo.getUserTaskWrapper() );
        remoteCallTaskDto.setReviewed( taskDo.isReviewed() );
        remoteCallTaskDto.setNotifiedTaskId( taskDo.getNotifiedTaskId() );

        return remoteCallTaskDto;
    }
}
