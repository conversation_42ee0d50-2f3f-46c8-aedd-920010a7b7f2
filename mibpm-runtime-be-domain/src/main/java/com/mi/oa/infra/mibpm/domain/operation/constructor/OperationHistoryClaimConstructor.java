package com.mi.oa.infra.mibpm.domain.operation.constructor;

import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.OperateClaimedEvent;
import com.mi.oa.infra.mibpm.domain.operation.model.OperationHistoryDo;
import com.mi.oa.infra.mibpm.domain.operation.service.OperationHistoryConstructor;
import com.mi.oa.infra.mibpm.eventbus.Event;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/14 15:53
 **/
public class OperationHistoryClaimConstructor implements OperationHistoryConstructor {
    private final EventIdentify operation = EventIdentify.OPERATE_CLAIM;

    @Override
    public String name() {
        return this.operation.name();
    }


    @Override
    public OperationHistoryDo build(Event event) {
        if (event instanceof OperateClaimedEvent) {
            OperateClaimedEvent claimedEvent = (OperateClaimedEvent) event;
            OperationHistoryDo.OperationHistoryDoBuilder builder = OperationHistoryDo.builder();
            builder.createUser(claimedEvent.getOperator());
            builder.operation(this.operation).processInstId(claimedEvent.getProcessInstanceId())
                    .taskName(claimedEvent.getTaskName()).taskId(claimedEvent.getTaskId())
                    .taskDefKey(claimedEvent.getTaskDefinitionKey()).assignee(claimedEvent.getOperator())
                    .comment(claimedEvent.getComment());
            return builder.build();
        }
        return null;
    }
}
