package com.mi.oa.infra.mibpm.domain.operation.constructor;

import com.google.common.collect.Lists;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.OperateTransferredEvent;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.domain.operation.model.OperationHistoryDo;
import com.mi.oa.infra.mibpm.domain.operation.service.OperationHistoryConstructor;
import com.mi.oa.infra.mibpm.eventbus.Event;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/14 15:53
 **/
public class OperationHistoryTransferConstructor implements OperationHistoryConstructor {

    private final EventIdentify operation = EventIdentify.OPERATE_TRANSFERRED;

    @Override
    public String name() {
        return this.operation.name();
    }

    @Override
    public OperationHistoryDo build(Event event) {
        if (event instanceof OperateTransferredEvent) {
            OperateTransferredEvent transferredEvent = (OperateTransferredEvent) event;
            OperationHistoryDo.OperationHistoryDoBuilder builder = OperationHistoryDo.builder();
            builder.createUser(transferredEvent.getOperator());
            builder.targetUser(Lists.newArrayList(BpmUser.builder().uid(transferredEvent.getTransferTo()).build()));
            builder.operation(this.operation).processInstId(transferredEvent.getProcessInstanceId())
                    .taskName(transferredEvent.getTaskName()).taskId(transferredEvent.getTaskId())
                    .taskDefKey(transferredEvent.getTaskDefinitionKey()).assignee(transferredEvent.getAssignee())
                    .comment(transferredEvent.getComment());
            return builder.build();
        }
        return null;
    }
}
