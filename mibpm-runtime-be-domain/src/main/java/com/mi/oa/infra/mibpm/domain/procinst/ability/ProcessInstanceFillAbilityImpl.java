package com.mi.oa.infra.mibpm.domain.procinst.ability;

import com.mi.oa.infra.mibpm.common.constant.BpmVariablesConstants;
import com.mi.oa.infra.mibpm.common.exception.DomainException;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.TaskAssignee;
import com.mi.oa.infra.mibpm.domain.procinst.errorcode.ProcInstDomainErrorCodeEnum;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.infra.procinst.remote.ProcessDefinitionRemoteService;
import com.mi.oa.infra.mibpm.infra.procinst.remote.model.ProcessDefinitionModel;
import com.mi.oa.infra.mibpm.infra.procinst.repository.ProcessInstanceRepository;
import com.mi.oa.infra.mibpm.infra.procinst.repository.TaskAssigneeRepository;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.utils.IdentityUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/3/9 19:14
 */
@Service
public class ProcessInstanceFillAbilityImpl implements ProcessInstanceFillAbility {

    @Autowired
    private ProcessInstanceRepository processInstanceRepository;
    @Autowired
    private TaskAssigneeRepository taskAssigneeRepository;
    @Autowired
    private ProcessDefinitionRemoteService processDefinitionRemoteService;
    @Autowired
    private AccountRemoteService accountRemoteService;

    @Override
    public void fillProcessInstanceStartUserId(ProcessInstanceDo processInstanceDo) {
        // 发起人为空时以登录人作为流程发起人
        if (StringUtils.isBlank(processInstanceDo.getStartUserId())) {
            processInstanceDo.setStartUserId(IdentityUtil.currentUid());
            processInstanceDo.setStartUser(accountRemoteService.getUser(IdentityUtil.currentUid()));
            return;
        }

        BpmUser user = accountRemoteService.getUser(processInstanceDo.getStartUserId());
        if (Objects.isNull(user)) {
            throw new DomainException(ProcInstDomainErrorCodeEnum.PROC_INST_START_USER_INVALID);
        }
        processInstanceDo.setStartUser(user);
    }

    @Override
    public void fillProcessInstanceOperator(ProcessInstanceDo processInstanceDo, String operator) {
        if (StringUtils.isNotBlank(operator)) {
            BpmUser user = accountRemoteService.getUser(operator);
            processInstanceDo.setOperator(user);
        }
    }

    @Override
    public void fillProcessInstanceOperator(ProcessInstanceDo processInstanceDo, BpmUser operator) {
        if (Objects.nonNull(operator)) {
            processInstanceDo.setOperator(operator);
        }
    }

    @Override
    public void fillProcessInstanceName(ProcessInstanceDo processInstanceDo, Map<String, Object> formData) {
        if (StringUtils.isBlank(processInstanceDo.getProcessInstanceName())) {
            // 从标题生成器获取流程实例名称
            processInstanceRepository.buildProcessInstanceName(processInstanceDo, formData);
        }
    }

    @Override
    public void fillProcessInstanceBusinessKey(ProcessInstanceDo processInstanceDo) {
        if (StringUtils.isBlank(processInstanceDo.getBusinessKey())) {
            processInstanceDo.setBusinessKey(UUID.randomUUID().toString().replace("-", ""));
        }
    }

    @Override
    public void fillProcessInstanceModelCode(ProcessInstanceDo processInstanceDo) {
        if (StringUtils.isNoneBlank(processInstanceDo.getModelCode())) {
            return;
        }

        if (StringUtils.isBlank(processInstanceDo.getProcessDefinitionId())) {
            return;
        }

        ProcessDefinitionModel processDefinitionModel = processDefinitionRemoteService.queryProcessDefinition(processInstanceDo.getProcessDefinitionId());
        processInstanceDo.setModelCode(processDefinitionModel.getKey());
        processInstanceDo.setProcessDefinitionName(processDefinitionModel.getName());
        processInstanceDo.setProcessDefinitionVersion(processDefinitionModel.getVersion());
    }

    @Override
    public void fillProcessInstanceProcDefId(ProcessInstanceDo processInstanceDo) {
        if (StringUtils.isNoneBlank(processInstanceDo.getProcessDefinitionId())) {
            return;
        }

        if (StringUtils.isBlank(processInstanceDo.getModelCode())) {
            return;
        }

        ProcessDefinitionModel processDefinitionModel = processDefinitionRemoteService.queryLastProcessDefinition(processInstanceDo.getModelCode());
        processInstanceDo.setProcessDefinitionId(processDefinitionModel.getId());
        processInstanceDo.setProcessDefinitionName(processDefinitionModel.getName());
        processInstanceDo.setProcessDefinitionVersion(processDefinitionModel.getVersion());
    }

    @Override
    public void fillProcessInstanceTaskAssignee(ProcessInstanceDo processInstanceDo, List<TaskAssignee> taskAssignees) {
        if (Objects.isNull(processInstanceDo) || CollectionUtils.isEmpty(taskAssignees)) {
            return;
        }

        taskAssigneeRepository.saveTaskAssignee(processInstanceDo, taskAssignees);
    }

    @Override
    public void fillProcessVariables(ProcessInstanceDo processInstanceDo, Map<String, Object> formData) {
        Map<String, Object> delegateVariables = new LinkedHashMap<>();
        // 填充发起人，计算变量时使用
        delegateVariables.put(BpmVariablesConstants.VARIABLE_INITIATOR, processInstanceDo.getStartUserId());
        // 将表单数据也纳入到委托变量中
        delegateVariables.putAll(formData);
        // 将流程变量纳入到委托变量表中
        delegateVariables.putAll(processInstanceDo.getProcessVariables());
        // 构建流程变量，包括显示设置的变量及其衍生参数和系统内置参数
        Map<String, Object> variables = processInstanceRepository.buildProcessVariables(processInstanceDo, delegateVariables,
                MapUtils.isNotEmpty(formData));
        // 计算后的流程变量填充只流程实例
        processInstanceDo.getProcessVariables().putAll(variables);
    }
}
