package com.mi.oa.infra.mibpm.domain.apicall.ability;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.reflect.TypeToken;
import com.mi.flowable.rpc.x5.X5Response;
import com.mi.flowable.rpc.x5.X5Result;
import com.mi.info.comb.common.x5protocol.core.util.X5ProtocolCoreUtils;
import com.mi.oa.infra.mibpm.common.enums.ApiCallProtocolEnum;
import com.mi.oa.infra.mibpm.common.enums.ApiCallStatusEnum;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.RemoteCallErrorEvent;
import com.mi.oa.infra.mibpm.common.exception.DomainException;
import com.mi.oa.infra.mibpm.domain.apicall.converter.JsonUtil;
import com.mi.oa.infra.mibpm.domain.apicall.errorcode.ApiCallDomainErrorCodeEnum;
import com.mi.oa.infra.mibpm.domain.apicall.model.ApiCallHistoryDo;
import com.mi.oa.infra.mibpm.domain.apicall.model.ApiCallInstanceDo;
import com.mi.oa.infra.mibpm.domain.apicall.model.ApiTemplateDo;
import com.mi.oa.infra.mibpm.eventbus.EventPublisher;
import com.mi.oa.infra.mibpm.infra.apicall.repository.ApiCallHistoryRepository;
import com.mi.oa.infra.mibpm.infra.apicall.repository.ApiTemplateRepository;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import com.mioffice.ums.open.common.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.apache.rocketmq.spring.support.RocketMQHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MimeTypeUtils;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 *
 * 服务调用能力
 *
 * @author: qiuzhipeng
 * @Date: 2022/2/28 15:12
 */

@Slf4j
@Component
public class ApiCallInstanceAbilityImpl implements ApiCallInstanceAbility {

    @Value("${rocketmq.api-call-topic}")
    private String apiCallTopic;
    @NacosValue(value = "${mibpm.x5method: }", autoRefreshed = true)
    private String x5Method;

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private RocketMQTemplate mqTemplate;
    @Autowired
    private ApiCallHistoryRepository apiCallHistoryRepository;
    @Autowired
    private ApiTemplateRepository apiTemplateRepository;
    @Autowired
    private EventPublisher eventPublisher;

    private static final String CODE = "code";
    private static final String MESSAGE = "message";


    @Override
    public void checkApiInstance(ApiCallInstanceDo apiCallInstanceDo) {
        Assert.notNull(apiCallInstanceDo, "API调用实例为空!");
    }

    @Override
    public void callHttp(ApiCallInstanceDo apiCallInstance) {
        String method = apiCallInstance.getMethod();
        Map headers = GsonUtils.fromJson(apiCallInstance.getHeader(), Map.class);
        String url = apiCallInstance.getUrl();

        ResponseEntity<LinkedHashMap> forEntityResult = null;
        StopWatch stopWatch = StopWatch.createStarted();

        try {
            // get
            if (HttpMethod.GET.name().equals(method)) {
                HttpHeaders requestHeaders = new HttpHeaders();
                requestHeaders.put(HttpHeaders.PRAGMA, Arrays.asList(ApiCallProtocolEnum.HTTP.getCode(), apiCallInstance.getAppCode()));
                HttpEntity<String> requestEntity = new HttpEntity<String>(null, requestHeaders);
                forEntityResult = restTemplate.exchange(url, HttpMethod.GET, requestEntity, LinkedHashMap.class);
            }
            // post
            if (HttpMethod.POST.name().equals(method)) {
                MultiValueMap<String, String> header = new LinkedMultiValueMap();

                // multipart/form-data
                if (MediaType.MULTIPART_FORM_DATA_VALUE.equals(headers.get(HttpHeaders.CONTENT_TYPE))) {
                    header.put(HttpHeaders.CONTENT_TYPE, Collections.singletonList(MediaType.MULTIPART_FORM_DATA_VALUE));
                } else {
                    // application/json
                    header.put(HttpHeaders.CONTENT_TYPE, Collections.singletonList(MediaType.APPLICATION_JSON_VALUE));
                }

                // 设置 Accept
                header.put(HttpHeaders.ACCEPT, Arrays.asList(MediaType.APPLICATION_JSON_VALUE));
                // 设置认证协议扩充字段
                header.put(HttpHeaders.PRAGMA, Arrays.asList(ApiCallProtocolEnum.HTTP.getCode(), apiCallInstance.getAppCode()));

                // 添加body
                LinkedHashMap body = JsonUtil.toBean(apiCallInstance.getPayload(), LinkedHashMap.class);

                HttpEntity request = new HttpEntity(body, header);

                log.info("HTTP协议调用, req={}", GsonUtils.toJsonWtihNullField(body));
                forEntityResult = restTemplate.postForEntity(url, request, LinkedHashMap.class);
                log.info("HTTP协议调用, req={}, reps={}", apiCallInstance.getPayload(), GsonUtils.toJsonWtihNullField(forEntityResult));
            }

            if (forEntityResult == null || forEntityResult.getStatusCode() != HttpStatus.OK) {
                throw new RuntimeException(JsonUtils.toJson(forEntityResult));
            }
        }catch (Exception e){
            // 记录异常信息
            String message = e.getMessage() == null ? Arrays.toString(e.getStackTrace()) : e.getMessage();
            apiCallInstance.setResponse(message);
            apiCallInstance.setHttpStatus(HttpStatus.BAD_REQUEST);
            apiCallInstance.setCost(0L);
            throw new DomainException(ApiCallDomainErrorCodeEnum.DOMAIN_CALL_ERROR, e.getMessage());
        }
        stopWatch.stop();

        // 设置响应数据
        apiCallInstance.setResponse(GsonUtils.toJsonWtihNullField(forEntityResult));
        apiCallInstance.setHttpStatus(forEntityResult.getStatusCode());
        apiCallInstance.setCost(stopWatch.getTime(TimeUnit.MILLISECONDS));
    }

    @Override
    public void callX5(ApiCallInstanceDo apiCallInstance) {

        String method = null;
        method = getMethod(apiCallInstance);
        // 构建x5协议请求体
        String encodedX5Request = X5ProtocolCoreUtils.genX5RequestJson(apiCallInstance.getAppId(),
                apiCallInstance.getAppKey(),
                method,
                apiCallInstance.getPayload(),
                new ObjectMapper());

        StopWatch stopWatch = StopWatch.createStarted();

        String rawResult = "";
        Object response = null;
        try {
            OkHttpClient httpClient = (new OkHttpClient.Builder()).build();
            Request request = (new Request.Builder()).url(apiCallInstance.getUrl())
                    .post((new FormBody.Builder()).add("data", encodedX5Request)
                            .build()).build();
            Response httpResponse = httpClient.newCall(request).execute();

            rawResult = httpResponse.body().string();
            log.info("x5协议调用, req={}, reps={}", encodedX5Request, rawResult);

            X5Response x5Response = GsonUtils.fromJson(rawResult, new TypeToken<X5Response>() {
            }.getType());
            X5Result x5Result = GsonUtils.fromJson(rawResult, new TypeToken<X5Result>() {
            }.getType());

            if (x5Response.getHeader() != null) {
                if (!x5Response.isOk()) {
                    throw new RuntimeException(x5Response.getHeader().getDesc());
                }
                response = x5Response;
            } else {
                if (x5Result == null || !Objects.requireNonNull(x5Result).isOk()) {
                    throw new RuntimeException(JsonUtils.toJson(httpResponse));
                }
                response = x5Result;
            }

            apiCallInstance.setHttpStatus(HttpStatus.resolve(httpResponse.code()));
        } catch (Exception e) {
            String message = e.getMessage() == null ? Arrays.toString(e.getStackTrace()) : e.getMessage();
            // 记录错误信息
            if (StringUtils.isBlank(rawResult)) {
                rawResult = message;
            }
            apiCallInstance.setHttpStatus(HttpStatus.BAD_REQUEST);
            apiCallInstance.setResponse(rawResult);
            apiCallInstance.setCost(0L);
            throw new DomainException(ApiCallDomainErrorCodeEnum.DOMAIN_CALL_ERROR, e.getMessage());
        }

        stopWatch.stop();

        // 设置响应数据
        apiCallInstance.setHttpStatus(HttpStatus.OK);
        apiCallInstance.setResponse(GsonUtils.toJsonWtihNullField(new ResponseEntity(response, HttpStatus.OK)));
        apiCallInstance.setCost(stopWatch.getTime(TimeUnit.MILLISECONDS));
    }

    private String getMethod(ApiCallInstanceDo apiCallInstance) {
        String method = null;
        try {
            if (StringUtils.isNotEmpty(x5Method)) {
                String[] methods = x5Method.split(",");
                for (String s : methods) {
                    String[] split = s.split("@");
                    String k = split[0];
                    String v = split[1];
                    if (apiCallInstance.getUrl().equals(k)) {
                        return v;
                    }
                }
            }
            return method;
        } catch (Exception e) {
            log.error("get x5 method error!", e);
            return null;
        }
    }

    @Override
    public void addDeliveryTimes(ApiCallInstanceDo apiCallInstance) {
        Integer deliveryTimes = apiCallInstance.getDeliveryTimes();

        if(Objects.isNull(deliveryTimes)){
            apiCallInstance.setDeliveryTimes(1);
            return;
        }

        apiCallInstance.setDeliveryTimes(++deliveryTimes);
    }

    @Override
    public void recordCallHistory(ApiCallInstanceDo apiCallInstance) {

        // 构建调用日志记录
        ApiCallHistoryDo apiCallHistoryDo = ApiCallHistoryDo.builder()
                .id(apiCallInstance.getId())
                .callContext(GsonUtils.toJsonWtihNullField(apiCallInstance))
                .sequenceId(apiCallInstance.getSequenceId())
                .appCode(apiCallInstance.getAppCode())
                .apiId(apiCallInstance.getApiTemplateCode())
                .instanceId(apiCallInstance.getInstanceId())
                .modelCode(apiCallInstance.getModelCode())
                .cost(apiCallInstance.getCost() == null ? 0 : apiCallInstance.getCost())
                .url(apiCallInstance.getUrl())
                .request(apiCallInstance.getPayload())
                .response(apiCallInstance.getResponse())
                .status(ApiCallStatusEnum.buildByHttpStatus(apiCallInstance.getHttpStatus()).getCode())
                .build();

        // 检查是否记录请求响应体
        if(Objects.nonNull(apiCallInstance.getIsRecordLog())
                && !apiCallInstance.getIsRecordLog()){
            apiCallHistoryDo.setRequest("***");
            apiCallHistoryDo.setResponse("***");
        }
        try {
            ApiCallHistoryDo oldApiCallHistoryDo =
                    apiCallHistoryRepository.queryByApiIdAndSequenceId(apiCallHistoryDo.getApiId(),
                            apiCallHistoryDo.getSequenceId());
            // 已存在记录则设置主键id，在save方法中会更新调用日志
            if (oldApiCallHistoryDo != null) {
                apiCallHistoryDo.setId(oldApiCallHistoryDo.getId());
            }
            // 保存调用日志
            apiCallHistoryRepository.saveApiCallHistory(apiCallHistoryDo);
            apiCallInstance.setId(apiCallHistoryDo.getId());
        }catch (Exception e){
            log.error("保存调用记录失败,json={}",GsonUtils.toJsonWtihNullField(apiCallHistoryDo), e);
        }
    }

    /**
     * 0s 30s 1m 共调用三次
     * @param apiCallInstance
     */
    @Override
    public void sendMessageToMq(ApiCallInstanceDo apiCallInstance) {
        // 获取延迟等级
        //messageDelayLevel=1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m
        // 从3开始 3,4,5，即10s 30s 1m
        Integer deliveryTimes = apiCallInstance.getDeliveryTimes() + 2;

        // 构建mq message
        Message message = MessageBuilder
                .withPayload(apiCallInstance)
                .setHeader(MessageHeaders.CONTENT_TYPE, MimeTypeUtils.APPLICATION_JSON_VALUE)
                .setHeader(RocketMQHeaders.KEYS, apiCallInstance.getSequenceId())
                .build();

        // 推送消息到消息队列
        SendResult sendResult;
        if(apiCallInstance.getDeliveryTimes() == 1){
            // 第一次调用不走延迟队列
            sendResult = mqTemplate.syncSend(apiCallTopic, message, 500);
        }else{
            sendResult = mqTemplate.syncSend(apiCallTopic, message, 500, deliveryTimes);
        }

        log.info("send message to mq. message=[{}], mq message id=[{}]", apiCallInstance, sendResult.getMsgId());
    }

    @Override
    public void notifyAdminOnCallError(ApiCallInstanceDo apiCallInstance) {
        // 回调错误，超时等场景
        if(Objects.isNull(apiCallInstance.getHttpStatus())
                || apiCallInstance.getHttpStatus() == HttpStatus.OK
                || Boolean.FALSE.equals(apiCallInstance.getWarnOnFail())) {
            return;
        }
        if (apiCallInstance.getDeliveryTimes() != null && apiCallInstance.getDeliveryTimes() <= 2) {
            return;
        }
        ApiTemplateDo apiTemplateDo =
                apiTemplateRepository.queryApiTemplateByTemplateCode(apiCallInstance.getApiTemplateCode());
        if(Objects.isNull(apiTemplateDo)){
            return;
        }
        List<String> users = apiTemplateDo.getAdminUser();
        // 发送回调错误事件
        RemoteCallErrorEvent remoteCallErrorEvent = RemoteCallErrorEvent.builder()
                .sequenceId(apiCallInstance.getSequenceId())
                .adminUsers(users)
                .modelCode(apiCallInstance.getModelCode())
                .templateName(apiTemplateDo.getApiName())
                .processInstanceId(apiCallInstance.getInstanceId())
                .time(ZonedDateTime.now())
                .build();

        // 设置事件基础信息
        remoteCallErrorEvent.setId(apiCallInstance.getSequenceId());
        remoteCallErrorEvent.setIdentifier(EventIdentify.REMOTE_CALL_ERROR.name());
        remoteCallErrorEvent.setTimestamp(System.currentTimeMillis());

        //发送回调错误事件
        eventPublisher.publish(remoteCallErrorEvent);
    }

    @Override
    public String parseSubmitCheckResponse(ApiCallInstanceDo apiCallInstanceDo) {
        try {
            ResponseEntity<LinkedHashMap> responseEntity = GsonUtils.fromJson(apiCallInstanceDo.getResponse(), new TypeToken<ResponseEntity<LinkedHashMap>>() {
            }.getType());

            if (Objects.isNull(responseEntity) || Objects.isNull(responseEntity.getBody())) {
                return null;
            }

            if (apiCallInstanceDo.getProtocol() == ApiCallProtocolEnum.X5) {
                ResponseEntity<X5Response> x5Response = GsonUtils.fromJson(apiCallInstanceDo.getResponse(), new TypeToken<ResponseEntity<X5Response>>() {
                }.getType());
                return x5Response.getBody().getHeader().getDesc();
            }

            String code = (String) responseEntity.getBody().get(CODE);
            String message = (String) responseEntity.getBody().get(MESSAGE);
            if (!String.valueOf(HttpStatus.OK.value()).equals(code)) {
                //更新上下文中的响应状态码
                apiCallInstanceDo.setHttpStatus(HttpStatus.BAD_REQUEST);
                return message;
            }
        }catch (Exception e){
            log.info("parseSubmitCheckResponse error", e);
        }
        return null;
    }

    @Override
    public Object parseResponse(ApiCallInstanceDo apiCallInstanceDo) {
        ResponseEntity<LinkedHashMap> responseEntity = GsonUtils.fromJson(apiCallInstanceDo.getResponse(), new TypeToken<ResponseEntity<LinkedHashMap>>() {
        }.getType());

        if (Objects.isNull(responseEntity) || Objects.isNull(responseEntity.getBody())) {
            return null;
        }

        return responseEntity.getBody();
    }
}
