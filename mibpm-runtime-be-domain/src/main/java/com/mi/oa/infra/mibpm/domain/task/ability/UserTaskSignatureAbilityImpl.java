package com.mi.oa.infra.mibpm.domain.task.ability;

import com.mi.oa.infra.mibpm.domain.task.model.UserTaskSignature;
import com.mi.oa.infra.mibpm.infra.task.repository.UserTaskSignatureRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/6 11:07
 **/
@Service
public class UserTaskSignatureAbilityImpl implements UserTaskSignatureAbility {

    @Autowired
    private UserTaskSignatureRepository userTaskSignatureRepository;

    @Override
    public void saveSignature(UserTaskSignature userTaskSignature) {
        userTaskSignatureRepository.save(userTaskSignature);
    }

    @Override
    public String findByUserIdAndTaskId(String userId, String taskId) {
        UserTaskSignature userTaskSignature = userTaskSignatureRepository.getByTaskIdAndUserId(taskId, userId);
        return null == userTaskSignature ? "" : userTaskSignature.getSignature();
    }
}
