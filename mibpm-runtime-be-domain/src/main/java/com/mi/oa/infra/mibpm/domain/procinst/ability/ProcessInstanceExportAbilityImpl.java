package com.mi.oa.infra.mibpm.domain.procinst.ability;

import com.mi.oa.infra.mibpm.common.model.ProcessInstanceExportReq;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceExportHistoryDo;
import com.mi.oa.infra.mibpm.infra.procinst.repository.ProcessInstanceExportRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/7 8:20 PM
 **/
@Service
@Slf4j
public class ProcessInstanceExportAbilityImpl implements ProcessInstanceExportAbility {

    @Autowired
    private ProcessInstanceExportRepository processInstanceExportRepository;

    @Override
    public ProcessInstanceExportHistoryDo exportProcessInstance(ProcessInstanceExportReq req) {
        return processInstanceExportRepository.exportProcessInstance(req);
    }

    @Override
    public ProcessInstanceExportHistoryDo exportProcessInstanceAndForm(ProcessInstanceExportReq req) {
        return processInstanceExportRepository.exportProcessInstanceAndForm(req);
    }
}
