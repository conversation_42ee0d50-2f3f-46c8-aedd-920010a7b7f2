package com.mi.oa.infra.mibpm.domain.task.ability;

import com.google.common.collect.Lists;
import com.mi.oa.infra.mibpm.common.constant.RemoteCallVariablesConstants;
import com.mi.oa.infra.mibpm.common.enums.AuthorizeDimension;
import com.mi.oa.infra.mibpm.common.enums.AuthorizeRoleEnum;
import com.mi.oa.infra.mibpm.common.exception.DomainException;
import com.mi.oa.infra.mibpm.common.model.ApiCallVariable;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.MiBpmProperties;
import com.mi.oa.infra.mibpm.common.model.UserTaskActivity;
import com.mi.oa.infra.mibpm.domain.apicall.model.ApiCallInstanceDo;
import com.mi.oa.infra.mibpm.domain.apicall.service.ApiCallInstanceDomainService;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.task.errorcode.TaskDomainErrorCodeEnum;
import com.mi.oa.infra.mibpm.domain.task.model.DashBoardInfo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.domain.userconfig.model.ProcessViewAuthConfig;
import com.mi.oa.infra.mibpm.flowable.bpmn.BpmnModelService;
import com.mi.oa.infra.mibpm.flowable.extension.BpmnExtensionHelper;
import com.mi.oa.infra.mibpm.flowable.extension.model.SignAddTypeEnum;
import com.mi.oa.infra.mibpm.flowable.extension.model.SubmitCheck;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskWrapper;
import com.mi.oa.infra.mibpm.infra.procinst.repository.HistoricProcInstRepository;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.ModelsAuthorityRemote;
import com.mi.oa.infra.mibpm.infra.task.repository.DashBoardPermissionRepository;
import com.mi.oa.infra.mibpm.infra.task.repository.NotifiedTaskRepository;
import com.mi.oa.infra.mibpm.infra.task.repository.TaskRepository;
import com.mi.oa.infra.mibpm.infra.userconfig.repository.UserConfigRepository;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.AccountAuthorityResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.HistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2022/3/11 16:04
 */
@Service
@Slf4j
public class CheckTaskAbilityImpl implements CheckTaskAbility {

    private static final String WHALE = "whale";

    @Autowired
    private ModelsAuthorityRemote authorityRemote;
    @Autowired
    private NotifiedTaskRepository notifiedTaskRepository;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private HistoricProcInstRepository historicProcInstRepository;
    @Autowired
    private BpmnModelService bpmnModelService;
    @Autowired
    private BpmnExtensionHelper bpmnExtensionHelper;
    @Autowired
    private ApiCallInstanceDomainService apiCallInstanceService;
    @Autowired
    private TaskRepository taskRepository;
    @Autowired
    private UserConfigRepository userConfigRepository;
    @Autowired
    private MiBpmProperties miBpmProperties;
    @Autowired
    private HttpServletRequest request;
    @Autowired
    private AccountRemoteService accountRemoteService;
    @Autowired
    private DashBoardPermissionRepository dashBoardPermissionRepository;

    @Override
    public void checkCompleteTaskPermission(TaskDo taskDo) {
        if (Objects.isNull(taskDo.getOperator())) {
            throw new DomainException(TaskDomainErrorCodeEnum.TASK_OPERATOR_IS_EMPTY);
        }
        // 任务处理人具有操作权限
        if (Objects.nonNull(taskDo.getAssignee())
                && taskDo.getOperator().getUid().equals(taskDo.getAssignee().getUid())) {
            return;
        }
        // 超管具有操作权限
        if (authorityRemote.isSuperAdmin(taskDo.getOperator().getUserName())) {
            return;
        }
        // 流程管理员具有操作权限
        ProcessInstanceDo processInstanceDo = historicProcInstRepository.queryHistoricProcInst(
                taskDo.getProcessInstanceId());
        List<String> modelCodes = authorityRemote.queryDataResource(taskDo.getOperator().getUserName(),
                AuthorizeDimension.BPMN_OWNER);
        if (modelCodes.contains(processInstanceDo.getModelCode())) {
            return;
        }


        throw new DomainException(TaskDomainErrorCodeEnum.NO_PERMISSION_COMPLETE_TASK,
                taskDo.getOperator().getUserName());
    }

    @Override
    public boolean checkOperateTaskPermission(TaskDo taskDo) {
        if (Objects.isNull(taskDo.getOperator())) {
            throw new DomainException(TaskDomainErrorCodeEnum.TASK_OPERATOR_IS_EMPTY);
        }
        log.info("check read permission taskId={} assignee={} opt={}", taskDo.getTaskId(), taskDo.getAssignee(),
                taskDo.getOperator());
        // 超管具有查看权限
        if (authorityRemote.isSuperAdmin(taskDo.getOperator().getUserName())) {
            return true;
        }
        // 流程管理员具有查看权限
        ProcessInstanceDo processInstanceDo = historicProcInstRepository.queryHistoricProcInst(
                taskDo.getProcessInstanceId());
        List<String> modelCodes = authorityRemote.queryDataResource(taskDo.getOperator().getUserName(),
                AuthorizeDimension.BPMN_OWNER);
        if (modelCodes.contains(processInstanceDo.getModelCode())) {
            return true;
        }

        // 审批人为空时候选人有操作权限
        if (taskDo.getAssignee() == null || StringUtils.isBlank(taskDo.getAssignee().getUid())) {
            if (CollectionUtils.isNotEmpty(taskDo.getCandidates())
                    && taskDo.getCandidates().stream()
                    .anyMatch(i -> StringUtils.equals(i.getUid(), taskDo.getOperator().getUid()))) {
                return true;
            }
        }
        if (taskDo.getAssignee() == null) {
            return false;
        }
        if (taskDo.getAssignee().getUserName() == null && taskDo.getAssignee().getUid() == null) {
            return false;
        }
        return taskDo.getAssignee().getUid().equals(taskDo.getOperator().getUid())
                || taskDo.getAssignee().getUid().equals(taskDo.getOperator().getUserName())
                || taskDo.getAssignee().getUserName().equals(taskDo.getOperator().getUserName());
    }

    @Override
    public boolean checkSetAssigneeTaskPermission(TaskDo taskDo) {
        if (Objects.isNull(taskDo.getOperator())) {
            throw new DomainException(TaskDomainErrorCodeEnum.TASK_OPERATOR_IS_EMPTY);
        }
        // 超管具有修改权限
        if (authorityRemote.isSuperAdmin(taskDo.getOperator().getUserName())) {
            return true;
        }
        // 检查是否为该流程管理员
        ProcessInstanceDo processInstanceDo = historicProcInstRepository.queryHistoricProcInst(
                taskDo.getProcessInstanceId());
        List<String> modelCodes = authorityRemote.queryDataResource(taskDo.getOperator().getUserName(),
                AuthorizeDimension.BPMN_OWNER);
        if (modelCodes.contains(processInstanceDo.getModelCode())) {
            return true;
        }

        return false;
    }

    @Override
    public void checkTaskReadPermission(TaskDo taskDo) {
        if (Objects.isNull(taskDo.getOperator())) {
            throw new DomainException(TaskDomainErrorCodeEnum.TASK_OPERATOR_IS_EMPTY);
        }

        // 判断是否为当前审批人
         if (Objects.nonNull(taskDo.getAssignee())
                && taskDo.getOperator().getUid().equals(taskDo.getAssignee().getUid())) {
            return;
        }

        // 候选人有阅读权限
        if (CollectionUtils.isNotEmpty(taskDo.getCandidates())
                && taskDo.getCandidates().stream()
                .anyMatch(i -> StringUtils.equals(i.getUid(), taskDo.getOperator().getUid()))) {
            return;
        }

        // 抄送权限
        if (notifiedTaskRepository.checkNotifiedTaskPermission(taskDo.getProcessInstanceId(),
                taskDo.getTaskDefinitionKey(), taskDo.getOperator())) {
            return;
        }

        // 曾向发起人申请查看权限
        ProcessViewAuthConfig viewAuthConfig = ProcessViewAuthConfig.builder()
                .procInstId(taskDo.getProcessInstanceId())
                .taskKey(taskDo.getTaskDefinitionKey())
                .userId(taskDo.getOperator().getUserName())
                .build();
        if (userConfigRepository.hasProcessViewAuth(viewAuthConfig)) {
            return;
        }

        // 若当前流程不允许管理员查看，则抛出异常，无需判断后续权限
        if (Objects.nonNull(miBpmProperties.getViewAuth())
                && ListUtils.emptyIfNull(miBpmProperties.getViewAuth().getAdminAuthExcludes())
                .contains(taskDo.getModelCode())) {
            throw new DomainException(TaskDomainErrorCodeEnum.NO_PERMISSION_READ_TASK,
                    taskDo.getOperator().getUserName());
        }

        // 加载用户权限数据
        AccountAuthorityResp accountAuthority = authorityRemote.queryAccountAuth(
                taskDo.getOperator().getUserName());
        if (Objects.isNull(accountAuthority)) {
            throw new DomainException(TaskDomainErrorCodeEnum.NO_PERMISSION_READ_TASK,
                    taskDo.getOperator().getUserName());
        }
        List<String> roleCodeList = accountAuthority.getRoleCodeList();

        // 判断是否为超级管理员角色
        if (CollectionUtils.isNotEmpty(roleCodeList) && roleCodeList.contains(
                AuthorizeRoleEnum.SUPER_ADMIN.getCode())) {
            return;
        }

        // 全局稽查员具有查看权限
        if (CollectionUtils.isNotEmpty(roleCodeList)
                && roleCodeList.contains(AuthorizeRoleEnum.CHECKER.getCode())) {
            return;
        }

        // 判断是否授权
        if (authorityRemote.hasDataResource(accountAuthority,
                taskDo.getProcessInstanceId() + ":" + taskDo.getTaskId(), AuthorizeDimension.PROC_INST_USER)) {
            return;
        }

        ProcessInstanceDo processInstanceDo = historicProcInstRepository.queryHistoricProcInst(
                taskDo.getProcessInstanceId());

        // 检查是否为该流程管理员
        if (authorityRemote.hasDataResource(accountAuthority,
                processInstanceDo.getModelCode(), AuthorizeDimension.BPMN_OWNER)) {
            return;
        }

        // 检查是否为该流程稽查员
        if (authorityRemote.hasDataResource(accountAuthority,
                processInstanceDo.getModelCode(), AuthorizeDimension.BPMN_CHECKER)) {
            return;
        }

        // 判断是否为数鲸看板链接, 并且是否有与看板一致权限
        if (checkWhaleDashBoardPermission(taskDo)) {
            return;
        }

        // 无查看权限 抛出异常
        throw new DomainException(TaskDomainErrorCodeEnum.NO_PERMISSION_READ_TASK, taskDo.getOperator().getUserName());
    }

    @Override
    public boolean checkHistoricTaskRead(TaskDo taskDo) {
        if (Objects.isNull(taskDo.getOperator())) {
            throw new DomainException(TaskDomainErrorCodeEnum.TASK_OPERATOR_IS_EMPTY);
        }
        long count = historyService.createHistoricTaskInstanceQuery()
                .taskAssigneeIds(Lists.newArrayList(taskDo.getOperator().getUid(), taskDo.getOperator().getUserName()))
                .processInstanceId(taskDo.getProcessInstanceId())
                .count();
        return count > 0;
    }

    @Override
    public boolean checkTaskCompleted(TaskDo taskDo) {
        return Objects.nonNull(taskDo.getEndTime());
    }

    @Override
    public void checkTaskAddSign(TaskDo taskDo, SignAddTypeEnum signType, List<BpmUser> signUsers) {
        if (CollectionUtils.isEmpty(signUsers) || null == signType) {
            throw new DomainException(TaskDomainErrorCodeEnum.TASK_SIGN_ERROR);
        }
    }

    @Override
    public void checkCompleteRemoteCondition(TaskDo taskDo) {
        // 调用远程服务检查是否满足提交条件（若存在校验规则配置）
        UserTask userTask = bpmnModelService
                .findUserTask(taskDo.getProcessDefinitionId(), taskDo.getTaskDefinitionKey());

        // 获取用户节点包装对象
        UserTaskWrapper userTaskWrapper = bpmnExtensionHelper.getUserTaskWrapper(userTask);
        SubmitCheck submitCheck = userTaskWrapper.getSubmitCheck();
        // 存在校验条件
        if (Objects.nonNull(submitCheck)) {
            String apiId = submitCheck.getApiId();

            // 获取流程变量
            ProcessInstanceDo instance = historicProcInstRepository.queryHistoricProcInst(
                    taskDo.getProcessInstanceId());
            historicProcInstRepository.loadProcessVariables(instance);
            historicProcInstRepository.loadProcessInstanceStatus(instance);

            Map<String, Object> variables = apiCallInstanceService.buildBaseApiCallVariables(instance, taskDo);

            // 添加操作状态
            variables.put(RemoteCallVariablesConstants.STATUS, taskDo.getTaskAttribute().getOperation().getCode());
            variables.put(RemoteCallVariablesConstants.VARIABLES, instance.getProcessVariables());
            // 添加表单数据
            if (Objects.nonNull(taskDo.getFrom())) {
                variables.put(RemoteCallVariablesConstants.FORM_DATA, taskDo.getFrom().getData());
            }

            ApiCallVariable apiCallVariable = ApiCallVariable.builder()
                    .variables(variables)
                    .build();
            // 构建请求实例
            ApiCallInstanceDo apiCallInstanceDo = apiCallInstanceService.buildCallInstanceByTemplate(apiId,
                    apiCallVariable);
            apiCallInstanceDo.setWarnOnFail(Boolean.FALSE);

            apiCallInstanceService.callSync(apiCallInstanceDo);

            // 解析提交校验响应体
            String errorMessage = apiCallInstanceService.parseSubmitCheckResponse(apiCallInstanceDo);
            if (!apiCallInstanceDo.isSuccess()) {

                if (StringUtils.isNotBlank(errorMessage)) {
                    // 若存在错误信息，则抛出指定错误提示
                    throw new DomainException(TaskDomainErrorCodeEnum.REMOTE_CHECK_CONDITION_ERROR, errorMessage);
                } else {
                    // 抛出默认错误提示
                    throw new DomainException(TaskDomainErrorCodeEnum.REMOTE_CHECK_CONDITION_DEFAULT_ERROR);
                }
            }
        }
    }

    @Override
    public void checkReturnActivities(TaskDo taskDo, String targetActivityId) {
        if (StringUtils.isBlank(targetActivityId)) {
            throw new DomainException(TaskDomainErrorCodeEnum.NOT_ALLOW_RETURN_ACTIVITY, targetActivityId);
        }
        List<UserTaskActivity> userTaskActivities = taskRepository.queryReturnActivities(taskDo);
        boolean match = userTaskActivities.stream()
                .map(UserTaskActivity::getId)
                .anyMatch(s -> s.equals(targetActivityId));
        if (!match) {
            throw new DomainException(TaskDomainErrorCodeEnum.NOT_ALLOW_RETURN_ACTIVITY, targetActivityId);
        }
    }

    @Override
    public void checkTaskClaimPermission(TaskDo taskDo) {
        if (CollectionUtils.isEmpty(taskDo.getCandidates())) {
            throw new DomainException(TaskDomainErrorCodeEnum.TASK_CLAIM_CANDIDATE_EMPTY);
        }
        if (Objects.isNull(taskDo.getOperator())) {
            throw new DomainException(TaskDomainErrorCodeEnum.TASK_OPERATOR_IS_EMPTY);
        }
        if (Objects.nonNull(taskDo.getAssignee()) && StringUtils.isNotBlank(taskDo.getAssignee().getUid())) {
            throw new DomainException(TaskDomainErrorCodeEnum.TASK_CLAIM_ALREADY_CLAIMED);
        }
        Optional<BpmUser> any = taskDo.getCandidates().stream()
                .filter(i -> StringUtils.equals(i.getUid(), taskDo.getOperator().getUid())).findAny();
        if (!any.isPresent()) {
            throw new DomainException(TaskDomainErrorCodeEnum.TASK_CLAIM_NOT_IN_CANDIDATES);
        }
    }

    private boolean checkWhaleDashBoardPermission(TaskDo taskDo) {
        // 拿到数鲸看板链接标识
        String flag = (String) request.getAttribute("flag");
        // 是否是数鲸看板
        if (WHALE.equals(flag)) {
            // 执行数鲸看板的鉴权逻辑
            log.info("whale flag:{},user:{}", flag, taskDo.getOperator() != null ?
                    taskDo.getOperator().getUserName() : "empty");
            // 判断是否是当前用户下级员工的审批单
            if (isSubordinateApproval(taskDo)) {
                return true;
            }
            // 判断是否是当前用户负责部门的员工的审批单或个人授权
            if (isDepartmentOrPersonalApproval(taskDo)) {
                return true;
            }
        }
        return false;
    }

    // 当前审批单的审批人是否为当前登录人的下级
    private boolean isSubordinateApproval(TaskDo taskDo) {
        // 判断审批分析汇报线权限
        List<BpmUser> leaderLine = accountRemoteService.listUserReportLine(taskDo.getAssignee().getUid());
        List<String> leaderLineUsername = leaderLine.stream().map(BpmUser::getUserName).collect(Collectors.toList());
        if (leaderLineUsername.contains(taskDo.getOperator().getUserName())) {
            return true;
        }
        // 判断委托分析汇报线权限
        BpmUser orgAssignee = taskDo.getOriginalAssignee();
        if (orgAssignee != null && orgAssignee.getUserName() != null) {
            List<BpmUser> orgLeaderLine = accountRemoteService.listUserReportLine(taskDo.getOriginalAssignee().getUid());
            List<String> orgLeaderLineUsername =
                    orgLeaderLine.stream().map(BpmUser::getUserName).collect(Collectors.toList());
            if (orgLeaderLineUsername.contains(taskDo.getOperator().getUserName())) {
                return true;
            }
        }
        return false;
    }

    // 当前用户是否  负责当前审批单审批人的部门/有当前审批单查看权限
    private boolean isDepartmentOrPersonalApproval(TaskDo taskDo) {
        DashBoardInfo dashBoardInfo = dashBoardPermissionRepository.getDashBoardInfo();
        String currentUser = taskDo.getOperator().getUserName();
        // 审批任务信息
        BpmUser taskAssignee = taskDo.getAssignee();
        String taskUser = taskAssignee.getUserName();
        String taskUserDept = taskAssignee.getOrg().getOrgId();
        // 委托任务信息
        BpmUser orgTaskAssignee = taskDo.getOriginalAssignee();
        // 生成权限映射关系，支持多对多关系，key为当前登录用户，value为对应有权限的人/部门
        Map<String, Set<String>> dashBoardPermissionAssigneeMap = dashBoardInfo.getDashBoardPermissionAssigneeList().stream()
                .collect(Collectors.groupingBy(
                        DashBoardInfo.DashBoardPermissionAssignee::getUserOrpid,
                        Collectors.mapping(DashBoardInfo.DashBoardPermissionAssignee::getPermissionUserId, Collectors.toSet())
                ));
        Map<String, Set<String>> dashBoardPermissionDeptMap = dashBoardInfo.getDashBoardPermissionDeptList().stream()
                .collect(Collectors.groupingBy(
                        DashBoardInfo.DashBoardPermissionDept::getUserOrpid,
                        Collectors.mapping(DashBoardInfo.DashBoardPermissionDept::getDeptId, Collectors.toSet())
                ));
        Map<String, Set<String>> dashBoardPermissionFeiShuDeptMap = dashBoardInfo.getDashBoardPermissionFeiShuDeptList().stream()
                .collect(Collectors.groupingBy(
                        DashBoardInfo.DashBoardPermissionFeiShuDept::getUsername,
                        Collectors.mapping(DashBoardInfo.DashBoardPermissionFeiShuDept::getDepartmentPermission, Collectors.toSet())
                ));
        Map<String, Set<String>> dashBoardPermissionUserMap = dashBoardInfo.getDashBoardPermissionUserList().stream()
                .collect(Collectors.groupingBy(
                        DashBoardInfo.DashBoardPermissionUser::getUserOrpid,
                        Collectors.mapping(DashBoardInfo.DashBoardPermissionUser::getPermissionUserId, Collectors.toSet())
                ));
        // 判断当前用户是否有查看当前审批人的审批单的权限（数鲸权限表 * 2）
        if (dashBoardPermissionAssigneeMap.containsKey(currentUser)) {
            Set<String> permissionUser = dashBoardPermissionAssigneeMap.get(currentUser);
            if ((!permissionUser.isEmpty()) && (permissionUser.contains("all") || permissionUser.contains(taskUser))) {
                return true;
            }
        }
        if (dashBoardPermissionUserMap.containsKey(currentUser)) {
            Set<String> permissionUser = dashBoardPermissionUserMap.get(currentUser);
            if ((!permissionUser.isEmpty()) && (permissionUser.contains("all") || permissionUser.contains(taskUser))) {
                return true;
            }
        }
        // 判断当前用户是否是当前审批单用户的部门负责人
        if (dashBoardPermissionDeptMap.containsKey(currentUser)) {
            Set<String> permissionDept = dashBoardPermissionDeptMap.get(currentUser);
            if (deptPermissionCheck(permissionDept, taskUserDept, orgTaskAssignee)) {
                return true;
            }
        }
        if (dashBoardPermissionFeiShuDeptMap.containsKey(currentUser)) {
            Set<String> permissionDept = dashBoardPermissionFeiShuDeptMap.get(currentUser);
            if (deptPermissionCheck(permissionDept, taskUserDept, orgTaskAssignee)) {
                return true;
            }
        }
        return false;
    }

    // 部门权限判断逻辑
    private boolean deptPermissionCheck(Set<String> permissionDept, String taskUserDept, BpmUser orgTaskAssignee) {
        if (!permissionDept.isEmpty()) {
            // 审批看板部门判断
            if (permissionDept.contains("all") || permissionDept.contains(taskUserDept)) {
                return true;
            }
            // 子部门判断
            for (String permission : permissionDept) {
                if (userConfigRepository.deptBelong(taskUserDept, permission)) {
                    return true;
                }
            }
            // 委托看板部门判断
            if (orgTaskAssignee != null && (orgTaskAssignee.getUserName() != null && orgTaskAssignee.getOrg() != null)) {
                String orgTaskUserDept = orgTaskAssignee.getOrg().getOrgId();
                if (permissionDept.contains(orgTaskUserDept)) {
                    return true;
                }
                // 委托子部门判断
                for (String permission : permissionDept) {
                    if (userConfigRepository.deptBelong(orgTaskUserDept, permission)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }
}
