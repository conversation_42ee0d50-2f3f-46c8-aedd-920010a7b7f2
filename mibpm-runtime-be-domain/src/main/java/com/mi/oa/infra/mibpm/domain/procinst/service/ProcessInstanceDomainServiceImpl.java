package com.mi.oa.infra.mibpm.domain.procinst.service;

import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.ProcessInstanceExportReq;
import com.mi.oa.infra.mibpm.common.model.TaskAssignee;
import com.mi.oa.infra.mibpm.domain.procinst.ability.CheckProcessInstanceAbility;
import com.mi.oa.infra.mibpm.domain.procinst.ability.ProcessInstanceAbility;
import com.mi.oa.infra.mibpm.domain.procinst.ability.ProcessInstanceExportAbility;
import com.mi.oa.infra.mibpm.domain.procinst.ability.ProcessInstanceFillAbility;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceExportHistoryDo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.AccountAuthorityResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/2/9 14:32
 */
@Service
public class ProcessInstanceDomainServiceImpl implements ProcessInstanceDomainService {

    @Autowired
    private ProcessInstanceAbility processInstanceAbility;
    @Autowired
    private ProcessInstanceFillAbility processInstanceFillAbility;
    @Autowired
    private CheckProcessInstanceAbility checkProcessInstanceAbility;
    @Autowired
    private ProcessInstanceExportAbility processInstanceExportAbility;

    @Override
    public void fillStartProcessInstance(ProcessInstanceDo processInstanceDo) {
        // 填充流程实例发起人
        processInstanceFillAbility.fillProcessInstanceStartUserId(processInstanceDo);
        // 填充流程实例的操作人
        processInstanceFillAbility.fillProcessInstanceOperator(processInstanceDo, processInstanceDo.getStartUserId());
        // 填充流程实例业务唯一编码
        processInstanceFillAbility.fillProcessInstanceBusinessKey(processInstanceDo);
        // 填充流程实例模型编码
        processInstanceFillAbility.fillProcessInstanceModelCode(processInstanceDo);
        // 填充流程实例的流程定义ID
        processInstanceFillAbility.fillProcessInstanceProcDefId(processInstanceDo);
    }

    @Override
    public void fillProcessVariables(ProcessInstanceDo processInstanceDo, Map<String, Object> formData) {
        // 填充流程实例变量
        processInstanceFillAbility.fillProcessVariables(processInstanceDo, formData);
    }

    @Override
    public void fillProcessInstanceName(ProcessInstanceDo processInstanceDo, Map<String, Object> formData) {
        // 填充流程实例名称
        processInstanceFillAbility.fillProcessInstanceName(processInstanceDo, formData);
    }

    @Override
    public void fillProcessInstanceTaskAssignee(ProcessInstanceDo processInstanceDo, List<TaskAssignee> taskAssignees) {
        // 填充任务实例的各任务节点的处理人
        processInstanceFillAbility.fillProcessInstanceTaskAssignee(processInstanceDo, taskAssignees);
    }

    @Override
    public void fillProcessInstanceOperator(ProcessInstanceDo processInstanceDo, String operator) {
        // 填充流程实例的操作人
        processInstanceFillAbility.fillProcessInstanceOperator(processInstanceDo, operator);
    }

    @Override
    public void fillProcessInstanceOperator(ProcessInstanceDo processInstanceDo, BpmUser operator) {
        // 填充流程实例的操作人
        processInstanceFillAbility.fillProcessInstanceOperator(processInstanceDo, operator);
    }

    @Override
    public void checkStartProcessInstance(ProcessInstanceDo processInstanceDo, Integer version) {
        // 检查发起人是否缺失
        checkProcessInstanceAbility.checkProcessInstanceStartUser(processInstanceDo);
        // 检查发起人是否具有发起流程的权限
        checkProcessInstanceAbility.checkProcessInstanceStartPermission(processInstanceDo);

        // 检查流程实例名称
        checkProcessInstanceAbility.checkProcessInstanceName(processInstanceDo);
        // 检查流程实例的模型编码
        checkProcessInstanceAbility.checkProcessInstanceModelCode(processInstanceDo);
        // 检查和流程定义ID
        checkProcessInstanceAbility.checkProcessInstanceProcDefId(processInstanceDo, version);
        // 检查流程实例业务唯一ID
        checkProcessInstanceAbility.checkProcessInstanceBusinessKey(processInstanceDo);
        // 检查流程实例是否已经发起
        checkProcessInstanceAbility.checkProcessInstanceStarted(processInstanceDo);
    }

    @Override
    public void checkProcessStatus(ProcessInstanceDo processInstanceDo) {
        // 检查流程启用与否
        processInstanceAbility.checkProcessStatus(processInstanceDo);
    }

    @Override
    public void checkTerminateProcessInstance(ProcessInstanceDo processInstanceDo, List<TaskDo> taskDos) {
        // 检查操作人是否缺失
        checkProcessInstanceAbility.checkProcessInstanceOperator(processInstanceDo);
        // 检查当前登录人是否具有终止流程的权限
        checkProcessInstanceAbility.checkProcessInstanceTerminatePermission(processInstanceDo, taskDos);
    }

    @Override
    public void checkProcessInstanceRecall(ProcessInstanceDo processInstanceDo) {
        // 检查操作人是否缺失
        checkProcessInstanceAbility.checkProcessInstanceOperator(processInstanceDo);
        // 检查当前登录人是否具有撤回流程的权限
        checkProcessInstanceAbility.checkProcessInstanceRecallPermission(processInstanceDo);
    }

    @Override
    public void startProcessInstance(ProcessInstanceDo processInstanceDo) {
        processInstanceAbility.start(processInstanceDo);
    }

    @Override
    public void terminateProcessInstance(ProcessInstanceDo processInstanceDo) {
        processInstanceAbility.terminate(processInstanceDo);
    }

    @Override
    public void recall(ProcessInstanceDo processInstanceDo) {
        processInstanceAbility.recall(processInstanceDo);
    }

    @Override
    public ProcessInstanceDo queryProcessInstanceByBusinessKey(String businessKey) {
        return processInstanceAbility.queryProcessInstanceByBusinessKey(businessKey);
    }

    @Override
    public ProcessInstanceDo queryProcessInstance(String processInstanceId) {
        ProcessInstanceDo instance = processInstanceAbility.queryProcessInstance(processInstanceId);
        // 加载流程发起人对象
        processInstanceAbility.loadProcessStartUser(instance);
        return instance;
    }

    @Override
    public ProcessInstanceDo queryHistoricProcessInstanceByBusinessKey(String businessKey) {
        ProcessInstanceDo instance = processInstanceAbility.queryHistoricProcessInstanceByBusinessKey(businessKey);
        // 加载流程发起人对象
        processInstanceAbility.loadProcessStartUser(instance);
        return instance;
    }

    @Override
    public void loadProcessVariables(ProcessInstanceDo processInstanceDo) {
        processInstanceAbility.loadProcessVariables(processInstanceDo);
    }

    @Override
    public void setProcessVariables(ProcessInstanceDo processInstanceDo) {
        processInstanceAbility.setProcessVariables(processInstanceDo);
    }

    @Override
    public void setProcessVariable(ProcessInstanceDo processInstanceDo, String variableName, Object value) {
        processInstanceAbility.setProcessVariable(processInstanceDo, variableName, value);
    }

    @Override
    public void loadProcessStartUser(ProcessInstanceDo processInstanceDo) {
        processInstanceAbility.loadProcessStartUser(processInstanceDo);
    }

    @Override
    public void loadProcessWrapper(ProcessInstanceDo processInstanceDo) {
        processInstanceAbility.loadProcessWrapper(processInstanceDo);
    }

    @Override
    public void checkProcessExportPermission(String modelCode) {
        checkProcessInstanceAbility.checkProcessExportPermission(modelCode);
    }

    @Override
    public ProcessInstanceExportHistoryDo exportProcessInstance(ProcessInstanceExportReq req) {
        return processInstanceExportAbility.exportProcessInstance(req);
    }

    @Override
    public ProcessInstanceExportHistoryDo exportProcessInstanceAndForm(ProcessInstanceExportReq req) {
        return processInstanceExportAbility.exportProcessInstanceAndForm(req);
    }

    @Override
    public void checkFormData(ProcessInstanceDo processInstanceDo, Map<String, Object> formData) {
        checkProcessInstanceAbility.checkFormData(processInstanceDo, formData);
    }

    @Override
    public void checkProcessInstanceMonitorPermission(ProcessInstanceDo processInstanceDo) {
        checkProcessInstanceAbility.checkProcessInstanceMonitorPermission(processInstanceDo);
    }

    @Override
    public void checkProcessInstanceMonitorPermission(ProcessInstanceDo processInstanceDo, AccountAuthorityResp accountAuthority) {
        checkProcessInstanceAbility.checkProcessInstanceMonitorPermission(processInstanceDo, accountAuthority);
    }
}
