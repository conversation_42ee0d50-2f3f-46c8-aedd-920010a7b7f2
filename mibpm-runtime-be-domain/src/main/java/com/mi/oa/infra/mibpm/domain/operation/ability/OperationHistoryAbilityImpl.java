package com.mi.oa.infra.mibpm.domain.operation.ability;

import com.mi.oa.infra.mibpm.domain.operation.model.OperationHistoryDo;
import com.mi.oa.infra.mibpm.infra.operation.repository.OperationHistoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/17 19:35
 **/
@Service
public class OperationHistoryAbilityImpl implements OperationHistoryAbility {
    @Autowired
    private OperationHistoryRepository operationHistoryRepository;

    @Override
    public void save(OperationHistoryDo operationHistoryDo) {
        operationHistoryRepository.save(operationHistoryDo);
    }

    @Override
    public void loadTask(OperationHistoryDo operationHistoryDo, String taskId) {

    }
}
