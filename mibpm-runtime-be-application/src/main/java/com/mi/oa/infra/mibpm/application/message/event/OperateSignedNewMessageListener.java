package com.mi.oa.infra.mibpm.application.message.event;

import com.google.common.collect.Lists;
import com.larksuite.appframework.sdk.client.message.card.TemplateColor;
import com.larksuite.appframework.sdk.client.message.card.objects.I18n;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.OperateSignedEvent;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.Actions;
import com.mi.oa.infra.mibpm.domain.message.model.Content;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo.LarkMessageDoBuilder;
import com.mi.oa.infra.mibpm.domain.message.service.LarkMessageDomainService;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.infra.procinst.repository.HistoricProcInstRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 审批任务被加签事件监听器
 *
 * <AUTHOR>
 * @date 2022/3/18 20:51
 */

@Slf4j
@Service
public class OperateSignedNewMessageListener extends AbstractMessageListener<OperateSignedEvent> {


    @Autowired
    private LarkMessageDomainService larkMessageDomainService;
    @Autowired
    private HistoricProcInstRepository historicProcInstRepository;

    @Override
    public String identifier() {
        return EventIdentify.OPERATE_SIGNED_NEW.name();
    }

    @Override
    public void process(OperateSignedEvent event) {
        log.info("消息模块消费加签事件，event = {}", event);
        this.syncAppBadge(event.getProcessInstanceId(), event.getTaskDefinitionKey());
        // 由事件构建消息实例
        LarkMessageDo larkMessageDo = buildLarkMessage(event);
        // 检查消息实例
        larkMessageDomainService.checkLarkMessage(larkMessageDo);
        // 发送消息
        larkMessageDomainService.sendApprovalMessageCard(larkMessageDo);

    }


    private LarkMessageDo buildLarkMessage(OperateSignedEvent event) {

        LarkMessageDoBuilder builder = LarkMessageDo.builder();
        // 获取流程实例
        ProcessInstanceDo instance = historicProcInstRepository
                .queryHistoricProcInst(event.getProcessInstanceId());
        if (Objects.isNull(instance)) {
            return null;
        }
        BpmUser startUser = instance.getStartUser();
        builder.username(startUser.getUserName());
        String comment = event.getComment();
        BpmUser operator = event.getOperator();
        builder.comment(new I18n(comment, comment, comment));
        builder.templateColor(TemplateColor.GREEN);
        buildInstanceInfo(event.getTaskId(), builder, instance, true);
        buildActions(builder);
        buildContent(operator, builder, event.getTaskId());
        buildTitle(builder, event.getTargetUsers());
        buildActions(builder);
        builder.eventType(EventIdentify.OPERATE_SIGNED);
        return builder.build();
    }

    /**
     * 构建流程标题
     *
     * @param builder
     * @param targetUsers
     */
    private void buildTitle(LarkMessageDoBuilder builder, List<BpmUser> targetUsers) {
        BpmUser bpmUser = targetUsers.get(0);
        int size = targetUsers.size();
        String s = size <= 1 ? "" : "等" + size + "人";
        String title = String.format("您发起的流程被加签给[%s]%s", bpmUser.getDisplayName(), s);
        String enTitle = size <= 1 ? String.format("The process you initiated added approver [%s].",
                bpmUser.getDisplayName()) : String.format("The process you initiated added %d approvers.", size);

        builder.title(new I18n(title, enTitle, enTitle));
    }


    /**
     * 构建业务数据区
     * @param operator
     * @return
     */
    /**
     * 构建业务数据区
     *
     * @param operator
     * @return
     */
    private void buildContent(BpmUser operator, LarkMessageDoBuilder builder, String taskId) {
        // 构建content
        Content content = Content.builder()
                .isApprove(true)
                .username(String.format("%s(%s)", operator.getDisplayName(), operator.getUserName()))
                .deptInfo(String.join("-", buildDeptInfo(operator)))
                .summaries(Lists.newArrayList(parseSummary(taskId)))
                .build();
        builder.content(content);
    }

    /**
     * 构建按钮区
     *
     * @param builder
     */
    private void buildActions(LarkMessageDoBuilder builder) {
        //设置查看详情按钮
        Actions detail = new Actions();
        detail.setActionKey("DETAIL");
        detail.setActionName("查看详情");
        builder.actions(Collections.singletonList(detail));
    }
}
