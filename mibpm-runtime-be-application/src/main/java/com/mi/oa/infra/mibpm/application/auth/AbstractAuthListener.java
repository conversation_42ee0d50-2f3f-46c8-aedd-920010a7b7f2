package com.mi.oa.infra.mibpm.application.auth;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.HistoryService;
import org.flowable.task.api.TaskInfo;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.beans.factory.annotation.Autowired;
import com.google.common.collect.Lists;
import com.mi.oa.infra.form.def.common.model.schema.FiledTypes;
import com.mi.oa.infra.mibpm.common.enums.AuthorizeDimension;
import com.mi.oa.infra.mibpm.common.model.FormFlatDataDto;
import com.mi.oa.infra.mibpm.common.model.FormRelationProcessDataDto;
import com.mi.oa.infra.mibpm.domain.userconfig.model.ProcessViewAuthConfig;
import com.mi.oa.infra.mibpm.eventbus.Event;
import com.mi.oa.infra.mibpm.eventbus.EventSubscriber;
import com.mi.oa.infra.mibpm.infra.remote.sdk.FormRemoteService;
import com.mi.oa.infra.mibpm.infra.userconfig.repository.UserConfigRepository;
import com.mi.oa.infra.oaucf.idm.api.IdmAccountService;
import com.mi.oa.infra.oaucf.idm.api.rep.AccountDo;
import com.mi.oa.infra.uc.common.enmu.AssignOperationEnum;
import lombok.extern.slf4j.Slf4j;

/**
 * 消息回调监听器
 *
 * @author: qiuzhipeng
 * @Date: 2022/3/30 21:10
 */
@Slf4j
public abstract class AbstractAuthListener<T extends Event> implements EventSubscriber<T> {
    @Autowired
    private FormRemoteService formRemoteService;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private UserConfigRepository userConfigRepository;
    @Autowired
    private IdmAccountService idmAccountService;

    public static final String BPMN_AUTH_PREFIX = AuthorizeDimension.PROC_INST_USER.getCode();
    public static final String BPMN_AUTH_SEPARATOR = ":";
    public static final String SUCCESS_CODE = "0";
    
    protected String createProcInstResource(String taskId, String procInstId) {
        // 校验参数
        if (StringUtils.isBlank(taskId)) {
            return null;
        }
        return genCode(procInstId, taskId);
    }

    protected void createProcInstResource(String resourceCode) {
        createProcInstResource(resourceCode, null);
    }

    protected void assignDataPermission(List<String> resourceCodes, List<String> assignUsers) {
        operateDataPermission(resourceCodes, assignUsers, AssignOperationEnum.ADD);
    }

    protected void revokeDataPermission(List<String> resourceCodes, List<String> assignUsers) {
        operateDataPermission(resourceCodes, assignUsers, AssignOperationEnum.REVOKE);
    }

    private void operateDataPermission(List<String> resourceCodes, List<String> assignUsers, AssignOperationEnum assignOperationEnum) {
        if (CollectionUtils.isEmpty(resourceCodes) || CollectionUtils.isEmpty(assignUsers) || assignOperationEnum == null) {
            return;
        }
        List<String> userNames = idmAccountService.findAccountListByUids(String.join(",", assignUsers)).getData().stream().map(AccountDo::getUserName).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userNames)) {
            return;
        }

        Set<String> codes = new HashSet<>(resourceCodes); // 使用HashSet直接进行去重
        log.info("用户授权流程实例权限:{} {} {}", assignOperationEnum, codes, userNames);

        codes.forEach(resourceCode -> {
            String[] split = resourceCode.split(BPMN_AUTH_SEPARATOR);
            if (split.length < 2) {
                log.warn("无效的权限编码格式: {}", resourceCode); // 增加日志记录
                return;
            }

            String procInstId = split[1];
            String taskKey = (split.length >= 3) ? split[2] : null;

            userNames.forEach(userId -> {
                ProcessViewAuthConfig auth = ProcessViewAuthConfig.builder()
                        .procInstId(procInstId)
                        .taskKey(taskKey)
                        .userId(userId)
                        .build();

                if (userConfigRepository.hasProcessViewAuth(auth)) {
                    if (assignOperationEnum == AssignOperationEnum.REVOKE) {
                        userConfigRepository.removeProcessViewAuth(auth);
                    }
                } else if (assignOperationEnum == AssignOperationEnum.ADD) {
                    userConfigRepository.saveProcessViewAuth(auth);
                }
            });
        });
    }

    protected FormRelationProcessDataDto queryFormInstData(String procInstId) {
        FormFlatDataDto formTypeData = formRemoteService.getFormTypeData(procInstId, null, null,
                Lists.newArrayList(FiledTypes.RELATION_PROC));
        FormRelationProcessDataDto result = new FormRelationProcessDataDto();
        if (null == formTypeData) {
            return result;
        }
        Collection<FormFlatDataDto.FieldData> values = formTypeData.getMainFieldData().values();
        // 需要获取到表单配置信息，做基础的权限控制
        List<String> modelCodes = values.stream()
                .filter(i -> Objects.nonNull(i.getField()) && FiledTypes.RELATION_PROC.equals(((Map) i.getField()).get("type"))
                        && Objects.nonNull(((Map) i.getField()).get("processList")))
                .flatMap(i -> {
                    List<Map<String, Object>> processList = (List<Map<String, Object>>) ((Map) i.getField()).get("processList");
                    return processList.stream().map(p -> (String) p.get("value"));
                }).collect(Collectors.toList());
        List<String> taskIds = values.stream()
                .filter(i -> Objects.nonNull(i.getData()))
                .flatMap(i -> ((List<Map<String, String>>) i.getData()).stream().map(t -> t.get("taskId")))
                .collect(Collectors.toList());
        result.setModelCodes(modelCodes);
        result.setTaskIds(taskIds);
        return result;
    }

    /**
     * 查询任务id关联的任务和流程信息（需要做基础的数据安全校验，防止越权）
     *
     * @param taskIds
     * @param modelCodes 只返回在此列表中的流程对应的taskId
     * @return
     */
    protected Map<String, List<String>> mapProcTask(List<String> taskIds, List<String> modelCodes) {
        if (CollectionUtils.isEmpty(taskIds) || CollectionUtils.isEmpty(modelCodes)) {
            return new HashMap<>();
        }
        List<HistoricTaskInstance> list = historyService.createHistoricTaskInstanceQuery().searchInTaskIds(taskIds)
                .list();
        Map<String, List<HistoricTaskInstance>> collect = list.stream()
                .filter(t -> modelCodes.contains(getModelCode(t.getProcessDefinitionId())))
                .collect(Collectors.groupingBy(TaskInfo::getProcessInstanceId));
        HashMap<String, List<String>> result = new HashMap<>(16);
        collect.forEach((key, value) -> {
            List<String> values = value.stream().map(TaskInfo::getTaskDefinitionKey).collect(Collectors.toList());
            result.put(key, values);
        });
        return result;
    }

    private String getModelCode(String procDefId) {
        return procDefId.split(":")[0];
    }

    public static String genCode(String procInstId, String taskId) {
        String code;
        code = BPMN_AUTH_PREFIX;
        if (StringUtils.isNotBlank(procInstId)) {
            code += BPMN_AUTH_SEPARATOR + procInstId;
        }
        if (StringUtils.isNotBlank(taskId)) {
            code += BPMN_AUTH_SEPARATOR + taskId;
        }
        return code;
    }

    protected List<String> genPermissionCodes(Map<String, List<String>> relatedProcTaskMap) {
        List<String> permissionCodes = new ArrayList<>();
        for (Map.Entry<String, List<String>> procTaskEntry : relatedProcTaskMap.entrySet()) {
            String relatedProcInstId = procTaskEntry.getKey();
            createProcInstResource(relatedProcInstId);
            List<String> subTaskIds = procTaskEntry.getValue();
            for (String subTaskId : subTaskIds) {
                String permissionCode = genCode(relatedProcInstId, subTaskId);
                createProcInstResource(subTaskId, relatedProcInstId);
                if (StringUtils.isNotBlank(permissionCode)) {
                    permissionCodes.add(permissionCode);
                }
            }
        }
        return permissionCodes;
    }

}
