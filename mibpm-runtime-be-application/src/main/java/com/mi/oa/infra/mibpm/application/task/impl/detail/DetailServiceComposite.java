package com.mi.oa.infra.mibpm.application.task.impl.detail;

import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailCcResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailHistoryResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailOperationHistoryResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.VoteConfigResp;
import com.mi.oa.infra.mibpm.application.task.service.DetailService;
import com.mi.oa.infra.mibpm.common.enums.ClientEnum;
import com.mi.oa.infra.mibpm.common.enums.SourceEnum;
import com.mi.oa.infra.mibpm.common.exception.DomainException;
import com.mi.oa.infra.mibpm.domain.mitask.model.MiTaskDo;
import com.mi.oa.infra.mibpm.infra.mitask.repository.MiTaskRepository;
import com.mi.oa.infra.mibpm.infra.task.errorcode.TaskInfraErrorCodeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class DetailServiceComposite implements DetailService {

    @Autowired
    private BpmDetailServiceImpl bpmDetailService;
    @Autowired
    private ExtDetailServiceImpl extDetailService;
    @Autowired
    private MiTaskRepository miTaskRepository;

    @Override
    public TaskDetailResp detailBase(String taskId, ClientEnum client) {
        return getDetailService(taskId).detailBase(taskId, client);
    }

    @Override
    public TaskDetailResp detailForm(String taskId, ClientEnum client) {
        return getDetailService(taskId).detailForm(taskId, client);
    }

    @Override
    public TaskDetailCcResp detailCc(String taskId, ClientEnum client) {
        return getDetailService(taskId).detailCc(taskId, client);
    }

    @Override
    public TaskDetailResp detailConfig(String taskId, ClientEnum client) {
        return getDetailService(taskId).detailConfig(taskId, client);
    }

    @Override
    public TaskDetailHistoryResp detailHistory(String taskId, ClientEnum client) {
        return getDetailService(taskId).detailHistory(taskId, client);
    }

    @Override
    public TaskDetailOperationHistoryResp detailOperationHistory(String taskId, ClientEnum client) {
        return getDetailService(taskId).detailOperationHistory(taskId, client);
    }

    @Override
    public TaskDetailHistoryResp detailPredict(String taskId, ClientEnum client) {
        return getDetailService(taskId).detailPredict(taskId, client);
    }

    @Override
    public VoteConfigResp voteConfig(String procInstId, String taskDefKey) {
        MiTaskDo miTaskDo = miTaskRepository.getMiTaskProcInst(procInstId);
        DetailService detailService;
        if (miTaskDo == null) {
            throw new DomainException(TaskInfraErrorCodeEnum.PROC_INST_NOT_EXISTS, procInstId);
        }
        if (SourceEnum.UNIFIED_TODO_EXTERNAL == miTaskDo.getSource()) {
            detailService = extDetailService;
        } else {
            detailService = bpmDetailService;
        }
        return detailService.voteConfig(procInstId, taskDefKey);
    }

    private DetailService getDetailService(String taskId) {
        MiTaskDo miTaskDo = miTaskRepository.findMiTaskDoByTaskId(taskId);
        if (miTaskDo == null) {
            throw new DomainException(TaskInfraErrorCodeEnum.TASK_NOT_EXISTS, taskId);
        }
        if (SourceEnum.UNIFIED_TODO_EXTERNAL == miTaskDo.getSource()) {
            return extDetailService;
        }
        return bpmDetailService;
    }
}
