package com.mi.oa.infra.mibpm.application.task.event.cc;

import com.mi.oa.infra.mibpm.application.task.event.AbstractCcTaskListener;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.OperateRejectedEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 审批任务被驳回事件监听器
 *
 * <AUTHOR>
 * @date 2023/5/4 19:51
 */

@Slf4j
@Service
public class OperateRejectedCcListener extends AbstractCcTaskListener<OperateRejectedEvent> {

    @Override
    public String identifier() {
        return EventIdentify.OPERATE_REJECTED.name();
    }

    @Override
    public void process(OperateRejectedEvent event) {
        createNotifiedEvents(event, event.getTaskDefinitionKey(), event.getProcessDefinitionId(),
                event.getProcessInstanceId(), null, event.getTaskId(), event.getTaskName());
    }

}
