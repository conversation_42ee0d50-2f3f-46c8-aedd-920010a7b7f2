package com.mi.oa.infra.mibpm.application.message.event;

import com.google.common.collect.Lists;
import com.larksuite.appframework.sdk.client.message.card.TemplateColor;
import com.larksuite.appframework.sdk.client.message.card.objects.I18n;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.OperateApprovedEvent;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.Actions;
import com.mi.oa.infra.mibpm.domain.message.model.Content;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo.LarkMessageDoBuilder;
import com.mi.oa.infra.mibpm.domain.message.service.LarkMessageDomainService;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.infra.procinst.remote.ProcessDefinitionRemoteService;
import com.mi.oa.infra.mibpm.infra.procinst.repository.HistoricProcInstRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Objects;

/**
 * 审批任务被同意监听器
 *
 * <AUTHOR>
 * @date 2022/3/18 20:51
 */
@Slf4j
@Component
public class OperateApprovedMessageListener extends AbstractMessageListener<OperateApprovedEvent> {

    @Autowired
    private LarkMessageDomainService larkMessageDomainService;
    @Autowired
    private ProcessDefinitionRemoteService processDefinitionRemoteService;
    @Autowired
    private HistoricProcInstRepository historicProcInstRepository;

    @Override
    public String identifier() {
        return EventIdentify.OPERATE_APPROVED.name();
    }

    @Override
    public void process(OperateApprovedEvent event) {
        log.info("消息模块消费审批同意事件，event = {}", event);
        if (filterFirstNode(event) || event.isNotSendLarkMessage()) {
            return;
        }
        // 由事件构建消息实例
        LarkMessageDo larkMessageDo = buildLarkMessage(event);
        // 检查消息实例
        larkMessageDomainService.checkLarkMessage(larkMessageDo);
        // 发送消息
        larkMessageDomainService.sendApprovalMessageCard(larkMessageDo);

    }

    private boolean filterFirstNode(OperateApprovedEvent event) {
        if (processDefinitionRemoteService.listFirstNodeDef(event.getProcessDefinitionId())
                .contains(event.getTaskDefinitionKey())) {
            return true;
        }
        return false;
    }


    private LarkMessageDo buildLarkMessage(OperateApprovedEvent event) {

        LarkMessageDoBuilder builder = LarkMessageDo.builder();
        // 获取流程实例
        ProcessInstanceDo instance = historicProcInstRepository
                .queryHistoricProcInst(event.getProcessInstanceId());
        if (Objects.isNull(instance)) {
            return null;
        }
        builder.isEndNode(Objects.nonNull(instance.getEndTime()));
        BpmUser startUser = instance.getStartUser();
        BpmUser operator = event.getOperator();
        builder.username(startUser.getUserName());
        String comment = event.getComment();
        builder.comment(new I18n(comment, comment, comment));
        builder.templateColor(TemplateColor.GREEN);
        buildInstanceInfo(event.getTaskId(), builder, instance, true);
        buildContent(operator, builder, event.getTaskId());
        buildTitle(builder, operator, Objects.nonNull(instance.getEndTime()));
        buildActions(builder);
        builder.eventType(EventIdentify.OPERATE_APPROVED);
        return builder.build();
    }

    /**
     * 构建流程标题
     *
     * @param builder
     * @param user
     */
    private void buildTitle(LarkMessageDoBuilder builder, BpmUser user, boolean isEndNode) {

        String title = String.format("您发起的流程被[%s]审批通过", user.getDisplayName());
        String enTitle = String.format("Your process has been approved by [%s].", user.getUserName());
        if (isEndNode) {
            title = String.format("您发起的流程被[%s]终审通过", user.getDisplayName());
            enTitle = String.format("Your process has been reviewed and finished by [%s]", user.getUserName());
        }
        builder.title(new I18n(title, enTitle, enTitle));
    }

    /**
     * 构建业务数据区
     *
     * @param operator
     * @return
     */
    private void buildContent(BpmUser operator, LarkMessageDoBuilder builder, String taskId) {
        // 构建content
        Content content = Content.builder()
                .isApprove(true)
                .username(String.format("%s(%s)", operator.getDisplayName(), operator.getUserName()))
                .deptInfo(String.join("-", buildDeptInfo(operator)))
                .summaries(Lists.newArrayList(parseSummary(taskId)))
                .build();
        builder.content(content);
    }

    /**
     * 构建按钮区
     *
     * @param builder
     */
    private void buildActions(LarkMessageDoBuilder builder) {
        //设置查看详情按钮
        Actions detail = new Actions();
        detail.setActionKey("DETAIL");
        detail.setActionName("查看详情");
        builder.actions(Collections.singletonList(detail));
    }

}
