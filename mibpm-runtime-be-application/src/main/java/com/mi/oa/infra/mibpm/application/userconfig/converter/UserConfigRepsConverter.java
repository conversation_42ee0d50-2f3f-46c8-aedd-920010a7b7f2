package com.mi.oa.infra.mibpm.application.userconfig.converter;

import com.larksuite.appframework.sdk.core.protocol.client.contact.UserDetailResponse.Detail;
import com.mi.oa.infra.mibpm.application.userconfig.dto.reps.LarkUserInfoReps;
import org.mapstruct.Mapper;

/**
 * @author: qiuzhipeng
 * @Date: 2022/3/21 17:52
 */
@Mapper(componentModel = "spring")
public interface UserConfigRepsConverter {
    LarkUserInfoReps tolarkUserInfo(Detail detail);
}
