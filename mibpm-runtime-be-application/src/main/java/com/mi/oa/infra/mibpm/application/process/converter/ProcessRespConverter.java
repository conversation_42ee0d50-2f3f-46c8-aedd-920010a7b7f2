package com.mi.oa.infra.mibpm.application.process.converter;

import java.util.List;

import com.mi.oa.infra.mibpm.domain.process.model.VisibleConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import com.mi.oa.infra.mibpm.application.process.dto.resp.ProcessResp;
import com.mi.oa.infra.mibpm.domain.process.model.ProcessDo;
import com.mi.oa.infra.mibpm.domain.process.model.ProcessEnableStatus;
import com.mi.oa.infra.mibpm.domain.process.model.UsableConfig;

@Mapper(componentModel = "spring")
public interface ProcessRespConverter {

    default Integer convertModelEnableStatus(ProcessEnableStatus modelEnableStatus) {
        if (modelEnableStatus == null) {
            return null;
        }

        return modelEnableStatus.getCode();
    }

    default ProcessResp.UsableConfig convertUsableConfig(UsableConfig usableConfig) {
        if (usableConfig == null) {
            return null;
        }
        ProcessResp.UsableConfig result = new ProcessResp.UsableConfig();
        result.setWebEnabled(usableConfig.getWebEnabled());
        result.setBusinessEnabled(usableConfig.getBusinessEnabled());
        result.setAppEnabled(usableConfig.getAppEnabled());
        return result;
    }

    default ProcessResp.VisibleConfig convertVisibleConfig(VisibleConfig visibleConfig) {
        if (visibleConfig == null) {
            return null;
        }
        ProcessResp.VisibleConfig result = new ProcessResp.VisibleConfig();
        result.setDepartments(visibleConfig.getDepartments());
        result.setUsers(visibleConfig.getUsers());
        return result;
    }

    @Mappings({
            @Mapping(source = "enable", target = "modelEnableStatus")
    })
    ProcessResp convert(ProcessDo processDo);

    List<ProcessResp> convert(List<ProcessDo> processDoList);
}
