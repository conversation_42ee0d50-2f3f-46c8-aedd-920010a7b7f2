package com.mi.oa.infra.mibpm.application.todocenter.event;

import com.mi.oa.infra.mibpm.common.model.TaskLink;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.domain.task.service.TaskDomainService;
import com.mi.oa.infra.mibpm.eventbus.Event;
import com.mi.oa.infra.mibpm.eventbus.EventSubscriber;
import com.mi.oa.infra.mibpm.infra.procinst.repository.HistoricProcInstRepository;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.GscTodoRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.ModelRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.TodoCenterRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 服务回调监听器
 *
 * @author: qiuzhipeng
 * @Date: 2022/3/30 21:10
 */
@Slf4j
public abstract class AbstractTodoCenterSyncListener<T extends Event> implements EventSubscriber<T> {

    @Autowired
    protected HistoricProcInstRepository historicProcInstRepository;
    @Autowired
    protected TaskDomainService taskDomainService;
    @Autowired
    protected GscTodoRemoteService gscTodoRemoteService;
    @Autowired
    protected TodoCenterRemoteService todoCenterRemoteService;
    @Autowired
    protected ModelRemoteService modelRemoteService;
    @Autowired
    protected HistoryService historyService;
    @Autowired
    protected AccountRemoteService accountRemoteService;

    @Override
    public String identifier() {
        // 子类必须实现
        return "";
    }

    protected TaskLink getTaskLink(TaskDo taskDo) {
        // 获取详情跳转链接
        return taskDomainService.getTaskDetailLink(taskDo);
    }
}
