package com.mi.oa.infra.mibpm.application.mitask.factory;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.gson.reflect.TypeToken;
import com.larksuite.appframework.sdk.client.message.card.TemplateColor;
import com.mi.flowable.external.api.ExtHistoricTaskInstance;
import com.mi.flowable.external.api.ExternalHistoryService;
import com.mi.flowable.idm.api.AppInfo;
import com.mi.oa.infra.mibpm.application.mitask.dto.ExtCardBot;
import com.mi.oa.infra.mibpm.common.constant.BpmConstants;
import com.mi.oa.infra.mibpm.common.constant.LarkMessageConstants;
import com.mi.oa.infra.mibpm.common.exception.DomainException;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.ExternalContent;
import com.mi.oa.infra.mibpm.common.model.CardBot;
import com.mi.oa.infra.mibpm.domain.mitask.model.MiTaskDo;
import com.mi.oa.infra.mibpm.domain.procinst.errorcode.ProcInstDomainErrorCodeEnum;
import com.mi.oa.infra.mibpm.infra.mitask.repository.MiTaskRepository;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.mapper.ExtProcItemMapper;
import com.mi.oa.infra.mibpm.infra.repository.mybatis.po.ExtProcItemPo;
import com.mi.oa.infra.mibpm.infra.task.errorcode.TaskInfraErrorCodeEnum;
import com.mi.oa.infra.oaucf.idm.api.rep.DeptInfoDto;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: zoutongxu1
 * @DateTime: 2024/10/23 21:10
 * @Description:
 **/
@Component
@Slf4j
public class CardBotBuildFactory {
    @Autowired
    private AccountRemoteService accountRemoteService;
    @Autowired
    private MiTaskRepository miTaskRepository;
    @Autowired
    private ExtProcItemMapper extProcItemMapper;
    @Autowired
    private ExternalHistoryService externalHistoryService;

    public CardBot setColor(CardBot cardBot) {
        String status = cardBot.getStatus();

        if (cardBot.getTemplateColor() != null) {
            return cardBot;
        }
        if (status == null) {
            cardBot.setTemplateColor(TemplateColor.BLUE);
            return cardBot;
        }
        switch (status) {
            case BpmConstants.OPERATE_BTN_REJECT:
            case BpmConstants.OPERATE_BTN_STOP:
            case BpmConstants.OPERATE_BTN_INTERRUPT:
                cardBot.setTemplateColor(TemplateColor.RED);
                break;
            case BpmConstants.OPERATE_BTN_RESOLVE:
            case BpmConstants.OPERATE_BTN_AGREE:
            case BpmConstants.OPERATE_BTN_SUBMIT:
            case BpmConstants.OPERATE_BTN_END:
                cardBot.setTemplateColor(TemplateColor.GREEN);
                break;
            case LarkMessageConstants.URGE:
                cardBot.setTemplateColor(TemplateColor.YELLOW);
                // fall through
            default:
                cardBot.setTemplateColor(TemplateColor.BLUE);
                break;
        }
        return cardBot;
    }

    /**
     * 将三方业务系统流程转化为BPM内部流程参数
     *
     * @param extCardBot
     * @param cardBot
     */
    public CardBot extParamsToBpmParams(ExtCardBot extCardBot, CardBot cardBot, AppInfo appInfo) {

        String taskId = extCardBot.getTaskId();
        // 先查miTask
        MiTaskDo extHisMiTaskDo = miTaskRepository.findMiTaskDoByTaskId(taskId);
        if (extHisMiTaskDo == null) {
            ExtHistoricTaskInstance extHistoricTaskInstance = externalHistoryService.createHistoricTaskQuery()
                    .taskKey(taskId)
                    .businessKey(cardBot.getInstanceId())
                    .assignee(extCardBot.getUsername())
                    .tenantId(appInfo.getId())
                    .singleResult();
            if (extHistoricTaskInstance == null) {
                throw new DomainException(TaskInfraErrorCodeEnum.TASK_NOT_EXISTS, taskId);
            } else {
                throw new DomainException(TaskInfraErrorCodeEnum.TASK_NOT_EXISTS, "任务" + taskId + "不存在,但EXT表存在");
            }
        }
        // 根据modelCode查model信息
        ExtProcItemPo extProcItemPo = extProcItemMapper.selectOne(new LambdaQueryWrapper<ExtProcItemPo>()
                .eq(ExtProcItemPo::getProcessKey, extHisMiTaskDo.getModelCode()));
        if (extProcItemPo == null) {
            throw new DomainException(TaskInfraErrorCodeEnum.MODEL_NOT_EXISTS, "Model does not exist");
        }
        // 设置卡片属性
        cardBot.setTenantId(appInfo.getId());
        cardBot.setBpmType(extProcItemPo.getParentKey());
        cardBot.setProcessKey(extCardBot.getProcessKey());
        cardBot.setInstanceId(extHisMiTaskDo.getProcInstId());
        cardBot.setTaskId(extHisMiTaskDo.getTaskId());

        return cardBot;
    }

    public CardBot setInstanceInfo(ExtCardBot extCardBot, CardBot cardBot) {

        CardBot.InstanceInfo extInstanceInfo = new CardBot.InstanceInfo();

        MiTaskDo extMiTaskDo = miTaskRepository.findProcInstInfo(extCardBot.getInstanceId());
        if (extMiTaskDo == null) {
            log.error("instanceId is Invalid! extInstanceId: [" + extCardBot.getInstanceId() + "]");
            throw new DomainException(ProcInstDomainErrorCodeEnum.PROC_INST_NOT_EXISTS, "instanceId is Invalid");
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String createTime = sdf.format(Date.from(extMiTaskDo.getCreateTime().toInstant()));
        extInstanceInfo.setProcessName(extMiTaskDo.getProcInstName());
        extInstanceInfo.setStartTime(createTime);

        cardBot.setInstanceInfo(extInstanceInfo);

        return cardBot;
    }

    public CardBot setExtTitle(CardBot cardBot) {
        String title = "";
        String enTitle = "";
        String status = cardBot.getStatus();
        String userId = cardBot.getContent().getUsername();

        if (StringUtils.isNotBlank(userId)) {
            BpmUser user = accountRemoteService.getUser(userId);

            // 根据modelCode查model信息
            ExtProcItemPo extProcItemPo = extProcItemMapper.selectOne(new LambdaQueryWrapper<ExtProcItemPo>()
                    .eq(ExtProcItemPo::getProcessKey, cardBot.getProcessKey()));

            String userName = user != null ? user.getDisplayName() : "";
            title = String.format("[%s]发起[%s]，请及时处理", userName, reBuildTitle(extProcItemPo.getProcessName(), 22));
            enTitle = String.format("[%s] initiate [%s], please check in time.",
                    userId, reBuildTitle(extProcItemPo.getProcessEnName(), 40));
            cardBot.setTitle(title);
            cardBot.setEnTitle(enTitle);
            return cardBot;
        }

        String approver = cardBot.getContent().getApproverName();
        BpmUser approverInfo = accountRemoteService.getUser(approver);
        String displayUsername = approverInfo != null ? approverInfo.getDisplayName() : "";

        switch (status) {
            //agree
            case BpmConstants.OPERATE_BTN_AGREE:
                //resolve 委派完成
            case BpmConstants.OPERATE_BTN_RESOLVE:
                title = String.format("您发起的流程已经被[%s]审批通过！", displayUsername);
                enTitle = String.format("Your process has been approved by [%s].", approver);
                break;
            //reject
            case BpmConstants.OPERATE_BTN_REJECT:
                title = String.format("您发起的流程已经被[%s]驳回！", displayUsername);
                enTitle = String.format("Your process has been rejected by [%s].", approver);
                break;
            //stop
            case BpmConstants.OPERATE_BTN_STOP:
            case BpmConstants.OPERATE_BTN_INTERRUPT:
                title = String.format("您发起的流程已经被[%s]终止！", displayUsername);
                enTitle = String.format("Your process has been terminated by [%s].", approver);
                break;
            //submit
            case BpmConstants.OPERATE_BTN_SUBMIT:
                title = "您发起的流程已成功提交！";
                enTitle = "Your process has been successfully submitted.";
                break;
            //end
            case BpmConstants.OPERATE_BTN_END:
                title = String.format("您发起的流程已经被[%s]终审完成！", displayUsername);
                enTitle = String.format("Your process has been reviewed and finished by [%s].", approver);
                break;
            //transfer
            case BpmConstants.OPERATE_BTN_TRANSFER:
            case BpmConstants.OPERATE_BTN_TRANSFER_ADD:
                title = String.format("您发起的流程已经被转审至[%s]", displayUsername);
                enTitle = String.format("Your process has been assigned to [%s].", approver);
                break;
            //sign
            case BpmConstants.OPERATE_BTN_SIGN:
                title = String.format("您发起的流程已经被[%s]加签，请及时处理！", displayUsername);
                enTitle = String.format("[%s] have requested your process to be resubmitted. Please check in time.",
                        approver);
                break;
            //delegate
            case BpmConstants.OPERATE_BTN_DELEGATE:
                title = String.format("您发起的流程已经被委派至[%s]", displayUsername);
                enTitle = String.format("Awaiting [%s] to join and approve.", approver);
                break;
            default:
                throw new IllegalArgumentException("unsupported operation status!, status: " + status);
        }
        log.info("ext card title: {}", title);
        cardBot.setTitle(title);
        cardBot.setEnTitle(enTitle);
        return cardBot;
    }

    public CardBot setContent(ExtCardBot extCardBot, CardBot cardBot) {
        String approver = extCardBot.getContent().getApproverName();
        String username = extCardBot.getContent().getUsername();

        ExternalContent content = new ExternalContent();
        String userId = "";
        content.setSummaries(extCardBot.getContent().getSummaries());
        if (StringUtils.isNotBlank(username)) {
            userId = username;
            content.setApprover(false);
        } else {
            userId = approver;
            content.setApprover(true);
        }

        BpmUser user = accountRemoteService.getUser(userId);
        String userName = user != null ? user.getDisplayName() : "";
        String dispalyName = userName + "(" + userId + ")";
        content.setUsername(dispalyName);

        content.setDeptInfo("-");
        BpmUser userInfo = accountRemoteService.getUser(userId);
        if (userInfo == null) {
            log.error("申请人信息不存在, userId: " + userId);
            throw new RuntimeException("start user info is null, start userid: " + userId);
        } else {
            List<String> deptInfo = buildDeptInfo(userInfo);
            content.setDeptInfo(String.join("-", deptInfo));

        }
        cardBot.setContent(content);
        return cardBot;
    }

    private List<String> buildDeptInfo(BpmUser user) {
        List<String> deptNames = new ArrayList<>();
        if (!user.functionAccount()) {
            List<DeptInfoDto> deptInfo = GsonUtils.fromJson(user.getOrg().getFullOrgDesc(),
                    new TypeToken<List<DeptInfoDto>>() {
                    }.getType());
            if (Objects.nonNull(deptInfo)) {
                // 获取部门的前两级
                deptNames = deptInfo.stream().filter(t -> !t.getLevel().equals("0"))
                        .limit(2).map(DeptInfoDto::getDeptName).collect(Collectors.toList());
            }
        }
        return deptNames;
    }

    public String reBuildTitle(String title, int maxLen) {
        if (title.length() <= maxLen) {
            return title;
        }
        return title.substring(0, maxLen - 1).concat("...");
    }
}
