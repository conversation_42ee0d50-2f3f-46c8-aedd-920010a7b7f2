package com.mi.oa.infra.mibpm.application.message.event;

import com.google.common.collect.Lists;
import com.larksuite.appframework.sdk.client.message.card.TemplateColor;
import com.larksuite.appframework.sdk.client.message.card.objects.I18n;
import com.mi.oa.infra.mibpm.common.enums.AutoOperationTypeEnum;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.OperateTerminatedEvent;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.Actions;
import com.mi.oa.infra.mibpm.domain.message.model.Content;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo;
import com.mi.oa.infra.mibpm.domain.message.service.LarkMessageDomainService;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.infra.procinst.repository.HistoricProcInstRepository;
import com.mi.oa.infra.mibpm.infra.task.repository.HistoricTaskRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 审批任务被终止事件监听器
 *
 * <AUTHOR>
 * @date 2022/3/18 20:51
 */
@Slf4j
@Service
public class OperateTerminatedMessageListener extends AbstractMessageListener<OperateTerminatedEvent> {

    @Autowired
    private LarkMessageDomainService larkMessageDomainService;
    @Autowired
    private HistoricProcInstRepository historicProcInstRepository;
    @Autowired
    private HistoricTaskRepository historicTaskRepository;

    @Override
    public String identifier() {
        return EventIdentify.OPERATE_TERMINATED.name();
    }

    @Override
    public void process(OperateTerminatedEvent event) {
        log.info("消息模块消费任务终止事件，event = {}", event);
        this.syncAppBadge(event.getProcessInstanceId(), null);
        // 由事件构建消息实例
        LarkMessageDo larkMessageDo = buildLarkMessage(event);
        // 检查消息实例
        larkMessageDomainService.checkLarkMessage(larkMessageDo);
        // 发送消息
        larkMessageDomainService.sendApprovalMessageCard(larkMessageDo);
    }

    private LarkMessageDo buildLarkMessage(OperateTerminatedEvent event) {

        LarkMessageDo.LarkMessageDoBuilder builder = LarkMessageDo.builder();
        // 获取流程实例
        ProcessInstanceDo instance = historicProcInstRepository
                .queryHistoricProcInst(event.getProcessInstanceId());
        if (Objects.isNull(instance)) {
            return null;
        }

        // 查找当前任务节点
        String pendingTaskId = "";
        List<TaskDo> taskDos = historicTaskRepository.queryHistoricTasksByProcInstId(event.getProcessInstanceId());
        if (CollectionUtils.isNotEmpty(taskDos)) {
            TaskDo taskDo = taskDos.get(taskDos.size() - 1);
            pendingTaskId = taskDo.getTaskId();
        }

        BpmUser startUser = instance.getStartUser();
        builder.username(startUser.getUserName());
        String comment = event.getComment();
        BpmUser operator = event.getOperator();
        builder.comment(new I18n(comment, comment, comment));
        builder.templateColor(TemplateColor.RED);
        buildInstanceInfo(pendingTaskId, builder, instance, true);
        buildActions(builder);
        buildContent(operator, builder, pendingTaskId);
        buildTitle(builder, operator, event.getAutoOperationType());
        buildActions(builder);
        builder.eventType(EventIdentify.OPERATE_SIGNED);
        return builder.build();
    }

    /**
     * 构建流程标题
     *
     * @param builder
     * @param user
     */
    private void buildTitle(LarkMessageDo.LarkMessageDoBuilder builder, BpmUser user, AutoOperationTypeEnum autoOperationType) {
        String displayName = user.getDisplayName();
        if (AutoOperationTypeEnum.TIMEOUT.equals(autoOperationType)) {
            displayName = "系统(超时)";
        }
        String title = String.format("您发起的流程被[%s]终止", displayName);
        String enTitle = String.format("Your process has been terminated by [%s].",
                user.getUserName());
        builder.title(new I18n(title, enTitle, enTitle));
    }

    /**
     * 构建业务数据区
     * @param operator
     * @return
     */
    /**
     * 构建业务数据区
     *
     * @param operator
     * @return
     */
    private void buildContent(BpmUser operator, LarkMessageDo.LarkMessageDoBuilder builder, String taskId) {
        operator = accountRemoteService.getUser(operator.getUserName());

        // 构建content
        Content content = Content.builder()
                .isApprove(true)
                .username(String.format("%s(%s)", operator.getDisplayName(), operator.getUserName()))
                .deptInfo(String.join("-", buildDeptInfo(operator)))
                .summaries(Lists.newArrayList(parseSummary(taskId)))
                .build();
        builder.content(content);
    }

    /**
     * 构建按钮区
     *
     * @param builder
     */
    private void buildActions(LarkMessageDo.LarkMessageDoBuilder builder) {
        //设置查看详情按钮
        Actions detail = new Actions();
        detail.setActionKey("DETAIL");
        detail.setActionName("查看详情");
        builder.actions(Collections.singletonList(detail));
    }
}
