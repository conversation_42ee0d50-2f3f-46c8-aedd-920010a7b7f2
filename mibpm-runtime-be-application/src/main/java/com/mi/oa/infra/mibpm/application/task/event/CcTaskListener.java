package com.mi.oa.infra.mibpm.application.task.event;

import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.OperateCarbonCopiedEvent;
import com.mi.oa.infra.mibpm.eventbus.EventSubscriber;
import com.mi.oa.infra.mibpm.infra.task.repository.NotifiedTaskRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CcTaskListener implements EventSubscriber<OperateCarbonCopiedEvent> {

    @Autowired
    private NotifiedTaskRepository notifiedTaskRepository;

    @Override
    public String identifier() {
        return EventIdentify.OPERATE_CC.name();
    }

    @Override
    public void process(OperateCarbonCopiedEvent event) {
        log.info("接收到抄送任务事件 event = {}", event);
        String modelCode = event.getProcessDefinitionId().split(":")[0];
        notifiedTaskRepository.createNotifiedTask(modelCode, event.getProcessInstanceId(), event.getTaskDefinitionKey(),
                event.getCcTo(), event.getOperator());
    }

}
