package com.mi.oa.infra.mibpm.application.procinst.converter;

import com.mi.oa.infra.mibpm.application.proinst.dto.reps.OpenViewAuthResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.OpenQueryViewAuthReq;
import com.mi.oa.infra.mibpm.domain.userconfig.model.ProcessViewAuthConfig;
import com.mi.oa.infra.mibpm.infra.procinst.repository.HistoricProcInstRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/24 17:19
 **/
@Component
public class OpenQueryProcInstAuthConverter {

    @Autowired
    private HistoryService historyService;

    public List<OpenViewAuthResp> convert(List<ProcessViewAuthConfig> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        Set<String> procInstIds = list.stream().map(ProcessViewAuthConfig::getProcInstId)
                .collect(Collectors.toSet());
        List<HistoricProcessInstance> procInstList = historyService.createHistoricProcessInstanceQuery()
                .processInstanceIds(procInstIds).list();
        Map<String, String> procInstMap = procInstList.stream()
                .collect(Collectors.toMap(HistoricProcessInstance::getId,
                        HistoricProcessInstance::getBusinessKey, (k1, k2) -> k1));
        return list.stream()
                .map(i -> OpenViewAuthResp.builder().userId(i.getUserId())
                        .businessKey(procInstMap.get(i.getProcInstId()))
                        .taskDefKey(i.getTaskKey()).build())
                .collect(Collectors.toList());
    }

    public ProcessViewAuthConfig convert(OpenQueryViewAuthReq req) {
        String businessKey = req.getBusinessKey();
        String user = req.getUserId();
        String taskDefKey = req.getTaskDefKey();
        String processInstanceId = null;
        // 根据业务唯一编码获取流程实例
        if (StringUtils.isNotBlank(businessKey)) {
            HistoricProcessInstance processInstanceDo = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceBusinessKey(
                            businessKey).singleResult();
            processInstanceId = processInstanceDo.getId();
        }
        return ProcessViewAuthConfig.builder().procInstId(processInstanceId).taskKey(taskDefKey).userId(user).build();
    }
}
