package com.mi.oa.infra.mibpm.application.message.event;

import com.alibaba.nacos.shaded.com.google.common.reflect.TypeToken;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.mi.oa.infra.mibpm.common.model.Actions;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.ModelMeta;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo;
import com.mi.oa.infra.mibpm.infra.procinst.repository.ModelMetaRepository;
import com.mi.oa.infra.mibpm.sdk.dto.UpdateBpmnModelAdminReq;
import com.mi.oa.infra.mibpm.sdk.service.BpmRepositoryRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/17 14:06
 */
@Slf4j
public abstract class AbstractHandoverMessageListener {
    @Autowired
    private ModelMetaRepository modelMetaRepository;
    @Autowired
    private BpmRepositoryRemote bpmRepositoryRemote;


    protected void buildActions(LarkMessageDo.LarkMessageDoBuilder builder) {
        //进入流程管理后台
        Actions processManagement = new Actions();
        processManagement.setActionKey("URL");
        processManagement.setActionName("进入流程管理后台");
        String manageUrl = "https://ams.test.mioffice.cn/apps/bpm-admin/process";
        processManagement.setPcUrl(manageUrl);
        processManagement.setIosUrl(manageUrl);
        processManagement.setAndroidUrl(manageUrl);
        processManagement.setSort(7);

        // 查看流程管理手册
        Actions viewProcessManual = new Actions();
        viewProcessManual.setActionKey("URL");
        viewProcessManual.setActionName("查看流程管理手册");
        String manualUrl = "https://xiaomi.f.mioffice.cn/wiki/wikk4NZO5ic5QNFMgmW2H5iELOg";
        viewProcessManual.setPcUrl(manualUrl);
        viewProcessManual.setAndroidUrl(manualUrl);
        viewProcessManual.setIosUrl(manualUrl);
        viewProcessManual.setSort(7);

        builder.actions(Arrays.asList(processManagement, viewProcessManual));
    }

    protected void handleModelUpdates(List<ModelMeta> ownerProcessModels, List<ModelMeta> businessOwnerProcessModels, List<ModelMeta> checkerProcessModels,
                                      BpmUser curAssignee, BpmUser targetAssignee, Set<String> allModelCodes) {
        if (!CollectionUtils.isEmpty(businessOwnerProcessModels)) {
            businessOwnerProcessModels.forEach(modelMeta -> allModelCodes.add(modelMeta.getModelCode()));

            businessOwnerProcessModels.forEach(modelMeta -> {
                try {
                    // 获取当前负责人列表
                    List<String> currentOwners = modelMeta.getBusinessOwner();
                    if (currentOwners.contains(curAssignee.getUserName())) {
                        currentOwners.remove(curAssignee.getUserName());
                        currentOwners.add(targetAssignee.getUserName());
                        Set<String> uniqueBusinessOwner = new HashSet<>(currentOwners);
                        String currentPrimaryDepartment = modelMeta.getOwnerDept();
                        String targetDepartment = getTargetDept(targetAssignee.getOrg().getFullOrgDesc());

                        // 如果交接人不属于原来的流程主责部门，更新流程的主责部门为交接人的部门
                        if (!targetDepartment.equals(currentPrimaryDepartment)) {
                            // 更新主责部门
                            modelMeta.setOwnerDept(targetDepartment);
                            log.info("流程 [{}] 的主责部门已从 {} 更新为 {}",
                                    modelMeta.getModelCode(), currentPrimaryDepartment, targetDepartment);
                        }

                        modelMeta.setBusinessOwner(new ArrayList<>(uniqueBusinessOwner));
                        modelMetaRepository.save(modelMeta);
                    }
                } catch (Exception e) {
                    log.error("处理流程 [{}] 的业务负责人变更时发生错误: {}", modelMeta.getModelCode(), e.getMessage(), e);
                }
            });
        }

        if (!CollectionUtils.isEmpty(ownerProcessModels)) {
            ownerProcessModels.forEach(modelMeta -> allModelCodes.add(modelMeta.getModelCode()));

            ownerProcessModels.forEach(modelMeta -> {
                try {
                    // 获取当前管理员列表
                    List<String> currentOwners = modelMeta.getOwners();
                    if (currentOwners.contains(curAssignee.getUserName())) {
                        currentOwners.remove(curAssignee.getUserName());
                        currentOwners.add(targetAssignee.getUserName());
                        Set<String> uniqueOwners = new HashSet<>(currentOwners);
                        modelMeta.setOwners(new ArrayList<>(uniqueOwners));
                        log.info("流程 [{}] 的管理员已从 {} 更新为 {}", modelMeta.getModelCode(), curAssignee.getUserName(), targetAssignee.getUserName());

                        UpdateBpmnModelAdminReq updateRequest = new UpdateBpmnModelAdminReq();
                        updateRequest.setModelCode(modelMeta.getModelCode());
                        updateRequest.setOwners(new ArrayList<>(uniqueOwners));

                        bpmRepositoryRemote.directUpdateCheckerOrAdmin(updateRequest);
                    }
                } catch (Exception e) {
                    log.error("处理流程 [{}] 的负责人变更时发生错误: {}", modelMeta.getModelCode(), e.getMessage(), e);
                }
            });
        }

        if (!CollectionUtils.isEmpty(checkerProcessModels)) {
            businessOwnerProcessModels.forEach(modelMeta -> allModelCodes.add(modelMeta.getModelCode()));

            // 处理每个流程模型
            checkerProcessModels.forEach(modelMeta -> {
                try {
                    // 获取当前稽查员列表
                    List<String> currentOwners = modelMeta.getChecker();
                    if (currentOwners.contains(curAssignee.getUserName())) {
                        currentOwners.remove(curAssignee.getUserName());
                        currentOwners.add(targetAssignee.getUserName());
                        Set<String> uniqueChecker = new HashSet<>(currentOwners);
                        modelMeta.setChecker(new ArrayList<>(uniqueChecker));

                        UpdateBpmnModelAdminReq updateRequest = new UpdateBpmnModelAdminReq();
                        updateRequest.setModelCode(modelMeta.getModelCode());
                        updateRequest.setChecker(modelMeta.getChecker());

                        bpmRepositoryRemote.directUpdateCheckerOrAdmin(updateRequest);
                        log.info("流程 [{}] 的稽查员已从 {} 更新为 {}", modelMeta.getModelCode(), curAssignee.getUserName(), targetAssignee.getUserName());
                    }
                } catch (Exception e) {
                    log.error("处理流程 [{}] 的稽查员变更时发生错误: {}", modelMeta.getModelCode(), e.getMessage(), e);
                }
            });
        }
    }


    /**
     * 获取指定用户的最具体的部门OrgId
     * 查找层级最高（数值最大的）的部门，并返回其deptId
     */
    private String getTargetDept(String fullOrgDescJson) {
        Gson gson = new Gson();
        List<Map<String, String>> fullOrgDesc = gson.fromJson(fullOrgDescJson, new TypeToken<List<Map<String, String>>>() {
        }.getType());

        // 查找最具体的部门，即层级数值最大的部门
        return fullOrgDesc.stream()
                // 按层级（level）进行排序，将层级数值最大的部门放在前面
                .sorted((dept1, dept2) -> Integer.compare(
                        Integer.parseInt(dept2.get("level")),
                        Integer.parseInt(dept1.get("level"))
                ))
                // 获取排序后的第一个部门的deptId
                .map(dept -> dept.get("deptId"))
                .findFirst()
                .orElse(null);
    }

    public List<String> queryModelCodesByModelMetas(List<ModelMeta> modelMetas) {
        if (modelMetas == null || modelMetas.isEmpty()) {
            return new ArrayList<>();
        }
        return modelMetas.stream()
                .map(ModelMeta::getModelCode)
                .collect(Collectors.toList());
    }
}
