package com.mi.oa.infra.mibpm.application.message.event;

import com.larksuite.appframework.sdk.client.message.card.TemplateColor;
import com.larksuite.appframework.sdk.client.message.card.objects.I18n;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.BusinessOwnerReminderEvent;
import com.mi.oa.infra.mibpm.common.model.ModelMeta;
import com.mi.oa.infra.mibpm.common.model.Actions;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo.LarkMessageDoBuilder;
import com.mi.oa.infra.mibpm.domain.message.service.LarkMessageDomainService;
import com.mi.oa.infra.mibpm.eventbus.EventSubscriber;
import com.mi.oa.infra.mibpm.infra.procinst.repository.ModelMetaRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 流程负责人填写提醒
 *
 * <AUTHOR>
 * @date 2024/8/20 17:20
 */
@Slf4j
@Service
public class BusinessOwnerReminderMessageListener implements EventSubscriber<BusinessOwnerReminderEvent> {

    @Autowired
    private LarkMessageDomainService larkMessageDomainService;
    @Autowired
    private ModelMetaRepository modelMetaRepository;

    @Override
    public String identifier() {
        return EventIdentify.BUSINESS_OWNER_REMINDER.name();
    }

    @Override
    public void process(BusinessOwnerReminderEvent event) {
        log.info("消息模块消费流程负责人填写提醒事件，event = {}", event);

        // 查询已启用且没有流程负责人的流程
        List<ModelMeta> processModels = modelMetaRepository.queryEnabledProcessesWithoutOwners();

        // 按 owners 分组，结果保存在 modelsGroupedByOwners
        Map<String, List<ModelMeta>> modelsGroupedByOwners = processModels.stream()
                .flatMap(model -> model.getOwners().stream()
                        .map(owner -> new AbstractMap.SimpleEntry<>(owner, model)))
                .collect(Collectors.groupingBy(Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())));


        // 遍历分组后的 owners 并发送消息
        modelsGroupedByOwners.forEach((owner, ownerModels) -> {
            // 构建消息实例
            LarkMessageDoBuilder builder = LarkMessageDo.builder();
            builder.templateColor(TemplateColor.BLUE);

            // 将每个owner对应的ModelMeta列表作为消息内容
            buildContent(builder, ownerModels);
            buildTitle(builder);
            builder.eventType(EventIdentify.BUSINESS_OWNER_REMINDER);
            buildActions(builder);
            LarkMessageDo larkMessageDo = builder.build();
            // 设置消息的接收人（即当前的 owner）
            larkMessageDo.setUsername(owner);

            // 发送消息给当前 owner
            log.info("发送提醒消息给流程负责人: {}", owner);
            larkMessageDomainService.checkLarkMessage(larkMessageDo);
            larkMessageDomainService.sendReminderMessageCard(larkMessageDo);
        });
    }


    private void buildTitle(LarkMessageDoBuilder builder) {
        String title = "根据集团要求，请及时填写以下流程的负责人";
        builder.title(new I18n(title, title, title));
    }

    private void buildContent(LarkMessageDoBuilder builder, List<ModelMeta> processModels) {
        StringBuilder content = new StringBuilder();

        for (int i = 0; i < Math.min(processModels.size(), 30); i++) {
            ModelMeta model = processModels.get(i);
            String modelCode = model.getModelCode();
            String modelName = model.getName();  // 获取模型名称
            content.append(String.format("%s (ID: %s) \n", modelName, modelCode));
        }

        content.append("\n温馨提示：如果您所在团队需填写的流程较多，建议收集起来，联系BPM批量修改，无需单独走审批。\n");

        builder.customizeContent(new I18n(content.toString(), content.toString(), content.toString()));
    }

    private void buildActions(LarkMessageDoBuilder builder) {
        // 设置“去后台填写”按钮
        Actions fillInButton = new Actions();
        fillInButton.setActionKey("URL");
        fillInButton.setActionName("去后台填写");
        String fillUrl = "https://ams.test.mioffice.cn/apps/bpm-admin/process";
        fillInButton.setPcUrl(fillUrl);
        fillInButton.setIosUrl(fillUrl);
        fillInButton.setAndroidUrl(fillUrl);
        fillInButton.setSort(7);

        // 设置“了解填写方法”按钮
        Actions learnMethodButton = new Actions();
        learnMethodButton.setActionKey("URL");
        learnMethodButton.setActionName("了解填写方法");
        String learnUrl = "https://xiaomi.f.mioffice.cn/docx/doxk4vQWcf3SgtO7rjBpFPCzonc";
        learnMethodButton.setPcUrl(learnUrl);
        learnMethodButton.setAndroidUrl(learnUrl);
        learnMethodButton.setIosUrl(learnUrl);
        learnMethodButton.setSort(7);


        // 设置“联系BPM批量修改”按钮
        Actions contactBPMButton = new Actions();
        contactBPMButton.setActionKey("URL");
        contactBPMButton.setActionName("联系BPM批量修改");
        String contractUrl = "https://applink.f.mioffice.cn/client/chat/chatter/add_by_link?link_token=3e6r4ecc-33df-47ea-b44b-ba4982dd3m7u";
        contactBPMButton.setPcUrl(contractUrl);
        contactBPMButton.setIosUrl(contractUrl);
        contactBPMButton.setAndroidUrl(contractUrl);
        contactBPMButton.setSort(7);

        builder.actions(Arrays.asList(fillInButton, learnMethodButton, contactBPMButton));
    }
}

