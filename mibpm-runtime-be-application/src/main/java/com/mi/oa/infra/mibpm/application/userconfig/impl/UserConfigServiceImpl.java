package com.mi.oa.infra.mibpm.application.userconfig.impl;

import com.larksuite.appframework.sdk.core.protocol.client.contact.UserDetailResponse.Detail;
import com.mi.oa.infra.mibpm.application.message.dto.reps.MessagePushRuleReps;
import com.mi.oa.infra.mibpm.application.message.dto.req.CreateMessagePushRuleReq;
import com.mi.oa.infra.mibpm.application.message.dto.req.UpdateMessagePushRuleReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.AutoNextApprovalReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.TaskViewAuthApplyReq;
import com.mi.oa.infra.mibpm.application.userconfig.converter.DelegationRepsConverter;
import com.mi.oa.infra.mibpm.application.userconfig.converter.DelegationReqConverter;
import com.mi.oa.infra.mibpm.application.userconfig.converter.MessagePushRuleRepsConverter;
import com.mi.oa.infra.mibpm.application.userconfig.converter.MessagePushRuleReqConverter;
import com.mi.oa.infra.mibpm.application.userconfig.converter.UserConfigRepsConverter;
import com.mi.oa.infra.mibpm.application.userconfig.dto.reps.LarkUserInfoReps;
import com.mi.oa.infra.mibpm.application.userconfig.dto.reps.QueryDelegationReps;
import com.mi.oa.infra.mibpm.application.userconfig.dto.req.CreateDelegationReq;
import com.mi.oa.infra.mibpm.application.userconfig.dto.req.QueryDelegationReq;
import com.mi.oa.infra.mibpm.application.userconfig.service.UserConfigService;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.TaskViewAuthApplyEvent;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.domain.userconfig.model.AutoNextApprovalConfig;
import com.mi.oa.infra.mibpm.domain.userconfig.model.DelegationConfig;
import com.mi.oa.infra.mibpm.domain.userconfig.model.MessagePushRuleConfig;
import com.mi.oa.infra.mibpm.domain.userconfig.model.ProcessViewAuthConfig;
import com.mi.oa.infra.mibpm.domain.userconfig.model.UserConfigDo;
import com.mi.oa.infra.mibpm.domain.userconfig.service.UserConfigDomainService;
import com.mi.oa.infra.mibpm.eventbus.EventPublisher;
import com.mi.oa.infra.mibpm.infra.remote.sdk.LarkAppRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.ModelsAuthorityRemote;
import com.mi.oa.infra.mibpm.infra.task.repository.HistoricTaskRepository;
import com.mi.oa.infra.mibpm.utils.IdentityUtil;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.uc.common.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.UUID;

/**
 * 用户配置服务
 *
 * @author: qiuzhipeng
 * @Date: 2022/3/21 17:47
 */
@Slf4j
@Service
public class UserConfigServiceImpl implements UserConfigService {

    @Autowired
    private DelegationReqConverter delegationReqConverter;
    @Autowired
    private DelegationRepsConverter delegationRepsConverter;
    @Autowired
    private MessagePushRuleRepsConverter messagePushRuleRepsConverter;
    @Autowired
    private MessagePushRuleReqConverter messagePushRuleReqConverter;
    @Autowired
    private UserConfigRepsConverter userConfigRepsConverter;
    @Autowired
    private UserConfigDomainService userConfigDomainService;
    @Autowired
    private LarkAppRemoteService larkAppRemoteService;
    @Autowired
    private HistoricTaskRepository historicTaskRepository;
    @Autowired
    private EventPublisher eventPublisher;
    @Autowired
    private ModelsAuthorityRemote authorityRemote;

    @Override
    public void addDelegation(CreateDelegationReq createDelegationReq, boolean isAdmin) {

        if (!isAdmin || !authorityRemote.isSuperAdmin(IdentityUtil.currentUserName())) {
            createDelegationReq.setUserId(IdentityUtil.currentUserName());
        }
        DelegationConfig delegationConfig = delegationReqConverter.dtoTodo(createDelegationReq);
        UserConfigDo userConfigDo = UserConfigDo
                .builder()
                .delegationConfig(delegationConfig)
                .build();
        userConfigDomainService.addDelegation(userConfigDo);
    }

    @Override
    public void cancelDelegation(Long id, boolean isAdmin) {
        DelegationConfig delegationConfig = DelegationConfig.builder()
                .id(id)
                .build();

        if (!isAdmin || !authorityRemote.isSuperAdmin(IdentityUtil.currentUserName())) {
            delegationConfig.setUserId(IdentityUtil.currentUserName());
        }
        UserConfigDo userConfigDo = UserConfigDo
                .builder()
                .delegationConfig(delegationConfig)
                .build();
        userConfigDomainService.updateUserConfig(userConfigDo);
    }

    @Override
    public PageModel<QueryDelegationReps> queryDelegation(QueryDelegationReq queryDelegationReq,
                                                          Integer page, Integer pageSize) {
        DelegationConfig delegationConfig = delegationReqConverter.dtoTodo(queryDelegationReq);
        PageModel<DelegationConfig> pageModel = userConfigDomainService.queryDelegationPageList(delegationConfig, page,
                pageSize);

        return PageModel.build(delegationRepsConverter.doListToDtoList(pageModel.getList()),
                pageModel.getPageSize(), pageModel.getPageNum(), pageModel.getTotal());
    }

    @Override
    public AutoNextApprovalReq autoNextApproval() {
        UserConfigDo userConfigDo = userConfigDomainService.queryAutoNextApproval(IdentityUtil.currentUserName());
        AutoNextApprovalReq autoNextApprovalReq = new AutoNextApprovalReq();
        autoNextApprovalReq.setIsAutoNextApproval(userConfigDo.getAutoNextApprovalConfig().getIsOpen());
        autoNextApprovalReq.setIsAutoNextApprovalComment(userConfigDo.getAutoNextApprovalConfig().getIsOpenApprovalComment());
        return autoNextApprovalReq;
    }

    @Override
    public void updateNextApproval(AutoNextApprovalReq autoNextApprovalReq) {
        AutoNextApprovalConfig autoNextApprovalConfig = AutoNextApprovalConfig.builder()
                .userName(IdentityUtil.currentUserName())
                .isOpen(autoNextApprovalReq.getIsAutoNextApproval())
                .isOpenApprovalComment(autoNextApprovalReq.getIsAutoNextApprovalComment())
                .build();

        UserConfigDo userConfigDo = UserConfigDo
                .builder()
                .autoNextApprovalConfig(autoNextApprovalConfig)
                .build();
        userConfigDomainService.updateUserConfig(userConfigDo);

    }

    @Override
    public void addMessagePushRule(CreateMessagePushRuleReq createMessagePushRuleReq) {
        MessagePushRuleConfig messagePushRuleConfig = messagePushRuleReqConverter.dtoToDo(createMessagePushRuleReq);
        UserConfigDo userConfigDo = UserConfigDo
                .builder()
                .messagePushRuleConfig(messagePushRuleConfig)
                .build();
        userConfigDomainService.addMessagePushRule(userConfigDo);
    }

    @Override
    public void updateMessagePushRule(UpdateMessagePushRuleReq updateMessagePushRuleReq) {
        MessagePushRuleConfig messagePushRuleConfig = messagePushRuleReqConverter.dtoToDo(updateMessagePushRuleReq);
        UserConfigDo userConfigDo = UserConfigDo
                .builder()
                .messagePushRuleConfig(messagePushRuleConfig)
                .build();
        userConfigDomainService.updateUserConfig(userConfigDo);
    }

    @Override
    public MessagePushRuleReps queryMessagePushRule(String userId) {
        UserConfigDo userConfigDo = userConfigDomainService.queryMessagePushRule(userId);
        return messagePushRuleRepsConverter.doToDto(userConfigDo.getMessagePushRuleConfig());
    }

    @Override
    public LarkUserInfoReps getUserDetail(String userId) {
        Detail userDetail = larkAppRemoteService.getUserDetail(userId);
        return userConfigRepsConverter.tolarkUserInfo(userDetail);
    }

    @Override
    public void applyTaskViewAuth(TaskViewAuthApplyReq taskViewAuthApplyReq) {

        TaskDo taskDo = historicTaskRepository.queryHistoricTask(taskViewAuthApplyReq.getTaskId());
        if (Objects.isNull(taskDo)) {
            return;
        }
        ProcessViewAuthConfig processViewAuthConfig = ProcessViewAuthConfig.builder()
                .procInstId(taskDo.getProcessInstanceId())
                .taskKey(taskDo.getTaskDefinitionKey())
                .userId(IdentityUtil.currentUserName())
                .build();

        // 构建事件对象
        TaskViewAuthApplyEvent taskViewAuthApplyEvent = TaskViewAuthApplyEvent.builder()
                .taskId(taskViewAuthApplyReq.getTaskId())
                .message(taskViewAuthApplyReq.getMessage())
                .applyUser(IdentityUtil.currentUser())
                .processViewAuthConfig(GsonUtil.toJsonString(processViewAuthConfig))
                .build();

        // 设置领域事件基础信息
        taskViewAuthApplyEvent.setId(UUID.randomUUID().toString());
        taskViewAuthApplyEvent.setIdentifier(EventIdentify.TASK_VIEW_AUTH_APPLY.name());
        taskViewAuthApplyEvent.setTimestamp(System.currentTimeMillis());
        // publish event
        log.info("发送任务查看权限申请事件，event = {}", taskViewAuthApplyEvent);
        eventPublisher.publish(taskViewAuthApplyEvent);
    }

    @Override
    public BpmUser getTaskViewAuthAssignee(String taskId) {
        TaskDo taskDo = historicTaskRepository.queryHistoricTask(taskId);
        if (Objects.nonNull(taskDo)) {
            return taskDo.getAssignee();
        }
        return null;
    }

    @Override
    public String getUserDefaultSignature(String userId) {
        return userConfigDomainService.getUserDefaultSignature(userId);
    }
}
