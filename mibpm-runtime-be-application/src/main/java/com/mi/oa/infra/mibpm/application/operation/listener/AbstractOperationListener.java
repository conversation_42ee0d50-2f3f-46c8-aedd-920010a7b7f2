package com.mi.oa.infra.mibpm.application.operation.listener;

import com.mi.oa.infra.mibpm.domain.operation.service.OperationHistoryDomainService;
import com.mi.oa.infra.mibpm.eventbus.Event;
import com.mi.oa.infra.mibpm.eventbus.EventSubscriber;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 消息回调监听器
 *
 * @author: qiuzhipeng
 * @Date: 2022/3/30 21:10
 */
@Slf4j
public abstract class AbstractOperationListener<T extends Event> implements EventSubscriber<T> {

    @Autowired
    private OperationHistoryDomainService operationHistoryDomainService;

    @Override
    public void process(Event event) {
        operationHistoryDomainService.save(event);
    }
}
