package com.mi.oa.infra.mibpm.application.task.impl;

import com.mi.oa.infra.mibpm.application.task.service.TaskRejectStrategyService;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.domain.task.service.TaskDomainService;
import com.mi.oa.infra.mibpm.flowable.extension.model.RejectStrategy;
import org.flowable.bpmn.model.StartEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2023/2/8 17:01
 **/
@Service
public class RejectToStartStrategyImpl implements TaskRejectStrategyService {
    @Autowired
    private TaskDomainService taskDomainService;

    @Override
    public String getName() {
        return RejectStrategy.REJECT_TO_START_USER;
    }

    @Override
    public void reject(RejectStrategy rejectStrategy, TaskDo taskDo, ProcessInstanceDo processInstanceDo, BpmUser operator) {
        // 获取流程发起事件
        StartEvent startEvent = processInstanceDo.getProcessWrapper().findStartEvent();
        // 退回任务到指定节点
        if (Objects.nonNull(startEvent)) {
            taskDomainService.returnTask(taskDo, startEvent.getId());
        }
    }
}
