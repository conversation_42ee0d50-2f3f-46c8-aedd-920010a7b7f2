package com.mi.oa.infra.mibpm.application.apicall.event;

import com.mi.oa.infra.mibpm.common.enums.ActivityTypeEnum;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.ActivityStartedEvent;
import com.mi.oa.infra.mibpm.common.model.ApiCallVariable;
import com.mi.oa.infra.mibpm.flowable.extension.model.EventCall;
import com.mi.oa.infra.mibpm.flowable.extension.model.EventCallBack;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 活动节点开始
 *
 * @author: qiuzhipeng
 * @Date: 2022/3/17 20:50
 */
@Slf4j
@Component
public class UserTaskActivityStartedRpcListener extends AbstractRpcListener<ActivityStartedEvent> {

    @Override
    public String identifier() {
        return EventIdentify.ACTIVITY_STARTED.name();
    }

    @Override
    public void process(ActivityStartedEvent activityStartedEvent) {
        log.info("服务调用模块消费活动节点开始事件，event = {}", activityStartedEvent);

        if (!ActivityTypeEnum.USER_TASK.getCode().equals(activityStartedEvent.getActivityType())) {
            return;
        }
        List<EventCall> eventCallBacks = this.getEventCallBack(activityStartedEvent.getProcessDefinitionId(),
                activityStartedEvent.getTaskDefinitionKey());

        if (CollectionUtils.isEmpty(eventCallBacks)) {
            return;
        }

        List<EventCallBack> bpmEventCall = eventCallBacks.stream().filter(e -> e instanceof EventCallBack)
                .map(e -> (EventCallBack) e).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(bpmEventCall)) {
            ApiCallVariable apiCallVariable = this.obtainBpmCallVariable(activityStartedEvent.getProcessInstanceId(),
                    null, activityStartedEvent.getFormData(), null, activityStartedEvent);

            // 执行调用
            bpmInvokeCalls(eventCallBacks, apiCallVariable);
        }
        // 执行btd调用
        btdInvokeCalls(eventCallBacks, activityStartedEvent.getProcessInstanceId(), null, activityStartedEvent.getFormData(), null, activityStartedEvent);
    }

    @Override
    String getSupportEventCode() {
        return EventIdentify.ACTIVITY_STARTED.name();
    }

    @Override
    protected boolean isSupportAllOperateEvent() {
        return false;
    }
}
