package com.mi.oa.infra.mibpm.application.operation.listener;

import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.OperateDelegatedEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 审批任务被委派事件监听器
 *
 * <AUTHOR>
 * @date 2023/5/4 19:51
 */

@Slf4j
@Service
public class OperateDelegatedHistoryListener extends AbstractOperationListener<OperateDelegatedEvent> {

    @Override
    public String identifier() {
        return EventIdentify.OPERATE_DELEGATED.name();
    }

}
