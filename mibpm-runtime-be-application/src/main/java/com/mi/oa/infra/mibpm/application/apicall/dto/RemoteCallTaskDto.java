package com.mi.oa.infra.mibpm.application.apicall.dto;

import com.mi.oa.infra.mibpm.common.enums.DelegationStateEnum;
import com.mi.oa.infra.mibpm.common.model.TaskLog;
import com.mi.oa.infra.mibpm.domain.task.model.TaskAttribute;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskWrapper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

/**
 * 任务
 *
 * <AUTHOR>
 * @date 2021/10/14 19:49
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteCallTaskDto {

    /**
     * 分类编码
     */
    protected String categoryCode;
    /**
     * 任务ID
     */
    protected String taskId;
    /**
     * 任务名称
     */
    protected String taskName;
    /**
     * 描述
     */
    protected String description;
    /**
     * 优先级
     */
    protected Integer priority;
    /**
     * 任务所有人
     */
    protected RemoteCallBpmUserDto owner;
    /**
     * 任务处理人
     */
    protected RemoteCallBpmUserDto assignee;
    /**
     * 流程实例ID
     */
    protected String processInstanceId;
    /**
     * 流程运行ID
     */
    protected String executionId;
    /**
     * 任务定义ID
     */
    protected String taskDefinitionId;
    /**
     * 流程定义ID
     */
    protected String processDefinitionId;
    /**
     * 范围标识符
     */
    protected String scopeId;
    /**
     * 任务创建时间
     */
    protected ZonedDateTime createTime;
    /**
     * 任务结束时间
     */
    protected ZonedDateTime endTime;
    /**
     * 任务定义KEY
     */
    protected String taskDefinitionKey;
    /**
     * 任务委托状态
     */
    protected DelegationStateEnum delegationState;
    /**
     * 任务过期时间
     */
    protected ZonedDateTime dueDate;
    /**
     * 任务属性
     */
    protected TaskAttribute taskAttribute;
    /**
     * 流程变量
     */
    protected Map<String, Object> taskVariables;
    /**
     * 任务日志
     */
    protected List<TaskLog> taskLogs;
    /**
     * 当前操作人
     */
    protected RemoteCallBpmUserDto operator;
    /**
     * 版本号
     */
    protected Integer revision;
    /**
     * 用户任务节点配置
     */
    protected UserTaskWrapper userTaskWrapper;
    /**
     * 是否已读
     */
    protected Boolean reviewed;
    protected String signature;
    /**
     * 抄送任务id
     */
    protected Long notifiedTaskId;

    protected String autoOperationType;
}
