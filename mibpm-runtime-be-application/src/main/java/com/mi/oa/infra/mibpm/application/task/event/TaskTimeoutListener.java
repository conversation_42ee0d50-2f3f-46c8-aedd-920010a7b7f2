package com.mi.oa.infra.mibpm.application.task.event;

import com.mi.oa.infra.mibpm.application.proinst.dto.req.ProcessInstanceTerminateReq;
import com.mi.oa.infra.mibpm.application.proinst.service.ProcessInstanceService;
import com.mi.oa.infra.mibpm.application.task.dto.req.CompleteTaskReq;
import com.mi.oa.infra.mibpm.application.task.service.ApprovalService;
import com.mi.oa.infra.mibpm.common.enums.AutoOperationTypeEnum;
import com.mi.oa.infra.mibpm.common.enums.ClientEnum;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.TaskTimeoutEvent;
import com.mi.oa.infra.mibpm.domain.mitask.model.MiTaskDo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.eventbus.EventPublisher;
import com.mi.oa.infra.mibpm.flowable.extension.model.TimeoutPolicy;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import com.mi.oa.infra.mibpm.infra.flowable.listener.TaskCreatedListener;
import com.mi.oa.infra.mibpm.infra.mitask.repository.MiTaskRepository;
import com.mi.oa.infra.mibpm.infra.remote.entity.TaskTimeoutDTO;
import com.mi.oa.infra.mibpm.infra.remote.sdk.MessageRemoteService;
import com.mi.oa.infra.mibpm.infra.task.repository.HistoricTaskRepository;
import com.mi.oa.infra.mibpm.utils.ZoneDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.apache.rocketmq.spring.core.RocketMQPushConsumerLifecycleListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/13 6:20 PM
 **/
@Slf4j
@Service
@RocketMQMessageListener(
        consumerGroup = "${rocketmq.task-timeout-group}",
        topic = "${rocketmq.task-timeout-topic}",
        consumeMode = ConsumeMode.CONCURRENTLY
)
public class TaskTimeoutListener implements RocketMQListener<TaskTimeoutDTO>, RocketMQPushConsumerLifecycleListener {

    public static final ChronoUnit REMIND_TIME_UNIT = ChronoUnit.HOURS;
    private static final int MAX_REMIND_DAYS = 7;

    @Autowired
    private MessageRemoteService messageRemoteService;
    @Autowired
    private HistoricTaskRepository repository;
    @Autowired
    private ApprovalService approvalService;
    @Autowired
    private ProcessInstanceService processInstanceService;
    @Autowired
    private EventPublisher eventPublisher;
    @Autowired
    private MiTaskRepository miTaskRepository;
    @Value("${rocketmq.task-timeout-topic}")
    private String taskTimeoutTopic;

    @Override
    public void onMessage(TaskTimeoutDTO taskTimeoutDTO) {
        log.info("consume task timeout msg={}", taskTimeoutDTO);
        ZonedDateTime now = ZonedDateTime.now();
        ZonedDateTime autoOperateTime = ZoneDateTimeUtil.parseISO(taskTimeoutDTO.getAutoOperateTime());
        ZonedDateTime deadline = ZoneDateTimeUtil.parseISO(taskTimeoutDTO.getDeadline());
        if (null == autoOperateTime && null == deadline) {
            return;
        }
        // 查任务状态 已完成的直接返回
        TaskDo taskDo = repository.queryHistoricTask(taskTimeoutDTO.getTaskId());
        if (null == taskDo || null != taskDo.getEndTime()) {
            return;
        }
        // 更新任务处理人
        taskTimeoutDTO.setAssignee(taskDo.getAssignee().getUid());
        TimeoutPolicy timeoutPolicy = taskTimeoutDTO.getTimeoutPolicy();
        if (null != autoOperateTime && autoOperateTime.isBefore(now)) {
            // 已过自动操作时间 开始操作
            if (null != timeoutPolicy) {
                if (UserTaskOperation.TERMINATE.equals(timeoutPolicy.getOperateType())) {

                    MiTaskDo procInstInfo = miTaskRepository.findProcInstInfo(taskDo.getProcessInstanceId());

                    ProcessInstanceTerminateReq req = ProcessInstanceTerminateReq.builder()
                            .processInstanceId(taskDo.getProcessInstanceId())
                            .operator(procInstInfo.getProcInstStarter())
                            .autoOperationType(AutoOperationTypeEnum.TIMEOUT)
                            .comment("超时自动终止")
                            .commentEn("Terminated automatically because of timeout.")
                            .build();
                    processInstanceService.terminateProcessInstance(req);
                    TaskTimeoutEvent taskTimeoutEvent = TaskTimeoutEvent.builder()
                            .taskId(taskDo.getTaskId())
                            .processInstanceId(taskDo.getProcessInstanceId())
                            .targetUser(taskTimeoutDTO.getAssignee())
                            .build();
                    // 设置事件基础信息
                    taskTimeoutEvent.setId(taskDo.getTaskName());
                    taskTimeoutEvent.setIdentifier(EventIdentify.TIMEOUT_REJECT.name());
                    taskTimeoutEvent.setTimestamp(System.currentTimeMillis());
                    // 发送审批任务被超时终止事件
                    eventPublisher.publish(taskTimeoutEvent);
                } else if (UserTaskOperation.AGREE.equals(timeoutPolicy.getOperateType())) {
                    CompleteTaskReq req = new CompleteTaskReq();
                    req.setTaskId(taskDo.getTaskId());
                    req.setOperator(taskDo.getAssignee());
                    req.setComment("超时自动通过(Agreed automatically because of timeout.)");
                    req.setCommentEn("Agreed automatically because of timeout.");
                    req.setClient(ClientEnum.SYSTEM);
                    req.setFastApproval(true);
                    req.setAutoOperationType(AutoOperationTypeEnum.TIMEOUT);
                    approvalService.approve(req);
                    TaskTimeoutEvent taskTimeoutEvent = TaskTimeoutEvent.builder()
                            .taskId(taskDo.getTaskId())
                            .processInstanceId(taskDo.getProcessInstanceId())
                            .targetUser(taskTimeoutDTO.getAssignee())
                            .autoOperationType(AutoOperationTypeEnum.TIMEOUT)
                            .build();
                    // 设置事件基础信息
                    taskTimeoutEvent.setId(taskDo.getTaskName());
                    taskTimeoutEvent.setIdentifier(EventIdentify.TIMEOUT_AGREE.name());
                    taskTimeoutEvent.setTimestamp(System.currentTimeMillis());
                    // 发送审批任务被超时通过事件
                    eventPublisher.publish(taskTimeoutEvent);
                }
            }
        } else if (null != deadline && deadline.isBefore(now)) {
            // 已过期，开始提醒
            long until = deadline.until(now, REMIND_TIME_UNIT);
            if (null == autoOperateTime && deadline.plusDays(MAX_REMIND_DAYS).isBefore(now)) {
                // 操作时间为空 并且已经超过提醒日期上限的，直接废弃不再处理
                return;
            }
            // 发一条提醒
            if (taskTimeoutDTO.isNeedRemind()) {
                TaskTimeoutEvent taskTimeoutEvent = TaskTimeoutEvent.builder()
                        .taskId(taskDo.getTaskId())
                        .processInstanceId(taskDo.getProcessInstanceId())
                        .targetUser(taskTimeoutDTO.getAssignee())
                        .timeoutHours(until)
                        .build();
                // 设置事件基础信息
                taskTimeoutEvent.setId(taskDo.getTaskName());
                taskTimeoutEvent.setIdentifier(EventIdentify.TIMEOUT_REMIND.name());
                taskTimeoutEvent.setTimestamp(System.currentTimeMillis());
                // 发送审批任务超时提醒事件
                eventPublisher.publish(taskTimeoutEvent);
            }
            Integer reminderFrequency = timeoutPolicy.getReminderFrequency();
            if (null != reminderFrequency) {
                ZonedDateTime nextRemindTime = now.plus(reminderFrequency, REMIND_TIME_UNIT);
                Long deliverSecond;
                // 如果下次提醒时间超过自动操作时间
                if (null != autoOperateTime && nextRemindTime.isAfter(autoOperateTime)) {
                    taskTimeoutDTO.setNeedRemind(false);
                    deliverSecond = autoOperateTime.toEpochSecond() * 1000;
                } else {
                    // 下次提醒时间没有超过自动操作时间
                    taskTimeoutDTO.setNeedRemind(true);
                    deliverSecond = nextRemindTime.toEpochSecond() * 1000;
                }
                messageRemoteService.sendMessageAtTime(taskTimeoutTopic, taskTimeoutDTO.getTaskId(),
                        deliverSecond, taskTimeoutDTO);
            }
        } else if (null != deadline && deadline.isAfter(now)) {
            // 还没过期
            ZonedDateTime nextRemindTime = now.plus(3L, TaskCreatedListener.TIMEOUT_UNIT);
            Long deliverSecond;
            if (nextRemindTime.isAfter(deadline)) {
                taskTimeoutDTO.setNeedRemind(false);
                deliverSecond = (deadline.toEpochSecond() + 10) * 1000;
            } else {
                deliverSecond = nextRemindTime.toEpochSecond() * 1000;
            }
            messageRemoteService.sendMessageAtTime(taskTimeoutTopic, taskTimeoutDTO.getTaskId(),
                    deliverSecond, taskTimeoutDTO);
        }
    }

    @Override
    public void prepareStart(DefaultMQPushConsumer consumer) {
        // 消息重试次数 3次
        consumer.setMaxReconsumeTimes(3);
        // 消息重试间隔 1000ms
        consumer.setSuspendCurrentQueueTimeMillis(1000);
    }
}
