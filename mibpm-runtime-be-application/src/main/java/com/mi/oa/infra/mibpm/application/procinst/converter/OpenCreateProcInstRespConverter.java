package com.mi.oa.infra.mibpm.application.procinst.converter;

import com.mi.oa.infra.mibpm.application.proinst.dto.reps.OpenCreateProcInstResp;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @date 2022/5/5 15:18
 */
@Mapper(componentModel = "spring")
public interface OpenCreateProcInstRespConverter {

    OpenCreateProcInstResp doToDto(ProcessInstanceDo processInstanceDo);
}
