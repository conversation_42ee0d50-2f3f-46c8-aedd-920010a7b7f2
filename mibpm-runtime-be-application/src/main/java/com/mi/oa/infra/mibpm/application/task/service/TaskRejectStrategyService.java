package com.mi.oa.infra.mibpm.application.task.service;

import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.flowable.extension.model.RejectStrategy;

/**
 * <AUTHOR>
 * @description
 * @date 2023/2/8 16:36
 **/
public interface TaskRejectStrategyService {

    String getName();

    void reject(RejectStrategy rejectStrategy, TaskDo taskDo, ProcessInstanceDo processInstanceDo, BpmUser operator);
}
