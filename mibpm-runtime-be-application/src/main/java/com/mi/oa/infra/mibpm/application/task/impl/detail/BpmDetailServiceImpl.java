package com.mi.oa.infra.mibpm.application.task.impl.detail;

import com.google.common.collect.Lists;
import com.mi.oa.infra.mibpm.application.task.converter.OperationHistoryDtoConverter;
import com.mi.oa.infra.mibpm.application.task.converter.TaskDetailRepsBuilder;
import com.mi.oa.infra.mibpm.application.task.converter.TaskInstConverter;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailCcResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailHistoryResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailOperationHistoryResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.VoteConfigResp;
import com.mi.oa.infra.mibpm.application.task.service.DetailService;
import com.mi.oa.infra.mibpm.application.task.service.HtmlDetailService;
import com.mi.oa.infra.mibpm.common.constant.BpmCommonConstants;
import static com.mi.oa.infra.mibpm.common.constant.BpmCommonConstants.FIRST_TASK_DEFAULT_KEY;
import static com.mi.oa.infra.mibpm.common.constant.BpmCommonConstants.FORM_DEFAULT_TASK_KEY;
import com.mi.oa.infra.mibpm.common.constant.BpmVariablesConstants;
import com.mi.oa.infra.mibpm.common.enums.ActivityTypeEnum;
import static com.mi.oa.infra.mibpm.common.enums.ActivityTypeEnum.USER_TASK;
import com.mi.oa.infra.mibpm.common.enums.ClientEnum;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import com.mi.oa.infra.mibpm.common.exception.DomainException;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.ProcessItem;
import com.mi.oa.infra.mibpm.domain.operation.model.OperationHistoryDo;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.procinst.service.ProcessInstanceDomainService;
import com.mi.oa.infra.mibpm.domain.task.ability.UserTaskSignatureAbility;
import com.mi.oa.infra.mibpm.domain.task.errorcode.TaskDomainErrorCodeEnum;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.domain.task.service.TaskDomainService;
import com.mi.oa.infra.mibpm.flowable.bpmn.BpmnModelService;
import com.mi.oa.infra.mibpm.flowable.extension.BpmnExtensionHelper;
import com.mi.oa.infra.mibpm.flowable.extension.model.ApprovalStrategy;
import com.mi.oa.infra.mibpm.flowable.extension.model.BpmnExtensionElements;
import com.mi.oa.infra.mibpm.flowable.extension.model.EmptyApproverStrategy;
import com.mi.oa.infra.mibpm.flowable.extension.model.ProcessWrapper;
import com.mi.oa.infra.mibpm.flowable.extension.model.ReceiveTaskWrapper;
import com.mi.oa.infra.mibpm.flowable.extension.model.RollbackStrategy;
import com.mi.oa.infra.mibpm.flowable.extension.model.ServiceTaskWrapper;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskSignType;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskWrapper;
import com.mi.oa.infra.mibpm.infra.operation.repository.OperationHistoryRepository;
import com.mi.oa.infra.mibpm.infra.procinst.repository.HistoricProcInstRepository;
import com.mi.oa.infra.mibpm.infra.procinst.repository.ModelMetaRepository;
import com.mi.oa.infra.mibpm.infra.procinst.repository.ProcInstPinRepository;
import com.mi.oa.infra.mibpm.infra.procinst.repository.ProcessInstanceRepository;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.FormRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.ModelRemoteService;
import com.mi.oa.infra.mibpm.infra.task.repository.HistoricTaskRepository;
import com.mi.oa.infra.mibpm.infra.task.repository.NotifiedTaskRepository;
import com.mi.oa.infra.mibpm.sdk.dto.ModelDto;
import com.mi.oa.infra.mibpm.utils.IdentityUtil;
import com.mi.oa.infra.mibpm.utils.SpringContextUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.ReceiveTask;
import org.flowable.bpmn.model.ServiceTask;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.runtime.Execution;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/7/15 11:00
 **/
@Service
public class BpmDetailServiceImpl implements DetailService {

    @Autowired
    private TaskDomainService taskDomainService;
    @Autowired
    private HistoricTaskRepository historicTaskRepository;
    @Autowired
    private ProcessInstanceRepository processInstanceRepository;
    @Autowired
    private HistoricProcInstRepository historicProcInstRepository;
    @Autowired
    private FormRemoteService formRemoteService;
    @Autowired
    private AccountRemoteService accountRemoteService;
    @Autowired
    private ModelRemoteService modelRemoteService;
    @Autowired
    private ProcessInstanceDomainService processInstanceDomainService;
    @Autowired
    private ProcInstPinRepository procInstPinRepository;
    @Autowired
    private BpmnModelService bpmnModelService;
    @Autowired
    private NotifiedTaskRepository notifiedTaskRepository;
    @Autowired
    private TaskInstConverter taskInstConverter;
    @Autowired
    private OperationHistoryRepository operationHistoryRepository;
    @Autowired
    private OperationHistoryDtoConverter operationHistoryDtoConverter;
    @Autowired
    private BpmnExtensionHelper bpmnExtensionHelper;
    @Autowired
    private UserTaskSignatureAbility userTaskSignatureAbility;
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private HtmlDetailService htmlDetailService;
    @Autowired
    private ModelMetaRepository modelMetaRepository;

    @Override
    public TaskDetailResp detailBase(String taskId, ClientEnum client) {
        BpmUser operator = IdentityUtil.currentUser();
        TaskDo taskDo = internalGetTaskDo(taskId, operator);
        // 查询流程实例
        ProcessInstanceDo processInstanceDo = processInstanceRepository.queryProcessInstance(
                taskDo.getProcessInstanceId());
        if (Objects.isNull(processInstanceDo)) {
            processInstanceDo = historicProcInstRepository.queryHistoricProcInst(taskDo.getProcessInstanceId());
            historicProcInstRepository.loadProcessVariables(processInstanceDo);
        } else {
            processInstanceDomainService.loadProcessVariables(processInstanceDo);
        }
        ModelDto modelDto = modelRemoteService.queryByCode(processInstanceDo.getModelCode());
        Integer fromOld = 1;
        if (null != modelDto) {
            fromOld = calculateOldType(processInstanceDo, modelDto);
            processInstanceDo.setCategoryCode(modelDto.getCategoryCode());

        }
        TaskDetailRepsBuilder builder = new TaskDetailRepsBuilder()
                .taskDo(taskDo)
                .processInstanceDo(processInstanceDo)
                .modelDto(modelDto)
                .oldType(fromOld);
        // 组装任务详情
        return builder.build();
    }

    @NotNull
    public TaskDo internalGetTaskDo(String taskId, BpmUser operator) {
        // 查询待办任务
        TaskDo taskDo = taskDomainService.queryTaskDo(taskId);
        if (Objects.isNull(taskDo)) {
            throw new DomainException(TaskDomainErrorCodeEnum.TASK_NOT_EXISTS, taskId);
        }
        // 填充任务操作人
        taskDomainService.fillTaskOperator(taskDo, operator);
        // 加载任务配置
        taskDomainService.loadUserTaskWrapper(taskDo);
        // 加载审批类型
        taskDomainService.loadActivityType(taskDo);
        // 加载候选人
        taskDomainService.loadTaskCandidates(taskDo);
        // 检查流程查看权限
        taskDomainService.checkTaskReadPermission(taskDo);
        return taskDo;
    }

    @Override
    public TaskDetailResp detailForm(String taskId, ClientEnum client) {
        // 查询待办任务
        BpmUser operator = IdentityUtil.currentUser();
        TaskDo taskDo = internalGetTaskDo(taskId, operator);

        BpmnModel bpmnModel = bpmnModelService.getBpmnModelByProcessDefId(taskDo.getProcessDefinitionId());
        ProcessWrapper processWrapper = bpmnExtensionHelper.getProcessWrapper(bpmnModel.getMainProcess());
        if (processWrapper.getFormType().isExtHtmlFormType()) {
            return htmlDetailService.xmlDetailForm(taskId, client);
        }

        TaskDetailRepsBuilder builder = new TaskDetailRepsBuilder().taskDo(taskDo);
        TaskDetailResp.Form form = new TaskDetailResp.Form();
        String processInstanceId = taskDo.getProcessInstanceId();
        String taskDefinitionKey = taskDo.getTaskDefinitionKey();
        String schemaKey;
        if (FIRST_TASK_DEFAULT_KEY.equals(taskDefinitionKey)) {
            schemaKey = FORM_DEFAULT_TASK_KEY;
        } else {
            schemaKey = taskDefinitionKey;
        }
        // 获取表单定义
        Map<String, Object> formDef = formRemoteService.getFromDef(taskDo.getProcessDefinitionId(),
                schemaKey);
        // 获取表单数据
        Map<String, Object> formData = formRemoteService.getFormData(taskDo.getProcessInstanceId(),
                taskDefinitionKey);
        form.setData(formData);
        form.setSchema(formDef);
        // 小程序
        if (ClientEnum.MINI_APP.equals(client)) {
            String pinStatus = procInstPinRepository.getProcPinStatus(processInstanceId,
                    IdentityUtil.currentUserName());
            builder.pinStatus(pinStatus == null ? "unpin" : pinStatus);
        }
        builder.form(form);
        // 组装任务详情
        return builder.build();
    }

    @Override
    public TaskDetailCcResp detailCc(String taskId, ClientEnum client) {
        TaskDo taskDo = taskDomainService.queryTaskDo(taskId);
        if (Objects.isNull(taskDo)) {
            throw new DomainException(TaskDomainErrorCodeEnum.TASK_NOT_EXISTS, taskId);
        }
        List<TaskDo> taskDos = notifiedTaskRepository.listNotifiedTask(taskDo.getProcessInstanceId());
        taskDomainService.fillTaskOperator(taskDo, IdentityUtil.currentUser());
        notifiedTaskRepository.checkNotifiedTaskPermission(taskDo.getProcessInstanceId(),
                taskDo.getTaskDefinitionKey(), taskDo.getOperator());
        TaskDetailCcResp.TaskDetailCcRespBuilder builder = TaskDetailCcResp.builder()
                .processInstanceId(taskDo.getProcessInstanceId())
                .taskList(taskInstConverter.doToCcDto(taskDos));
        return builder.build();
    }

    @Override
    public TaskDetailResp detailConfig(String taskId, ClientEnum client) {
        // 查询待办任务
        BpmUser operator = IdentityUtil.currentUser();
        // 查询待办任务
        TaskDo taskDo = taskDomainService.queryTaskDo(taskId);
        if (Objects.isNull(taskDo)) {
            throw new DomainException(TaskDomainErrorCodeEnum.TASK_NOT_EXISTS, taskId);
        }
        // 加载任务配置
        taskDomainService.loadUserTaskWrapper(taskDo);
        // 填充任务操作人
        taskDomainService.fillTaskOperator(taskDo, operator);
        // 加载审批类型
        taskDomainService.loadActivityType(taskDo);
        // 加载候选人
        taskDomainService.loadTaskCandidates(taskDo);
        ProcessInstanceDo processInstanceDo = processInstanceRepository.queryProcessInstance(
                taskDo.getProcessInstanceId());
        if (Objects.isNull(processInstanceDo)) {
            processInstanceDo = historicProcInstRepository.queryHistoricProcInst(taskDo.getProcessInstanceId());
        }
        processInstanceDomainService.loadProcessWrapper(processInstanceDo);
        if (null != taskDo.getExecutionId()) {
            historicTaskRepository.loadHistoricExecutionVariables(taskDo);
        } else {
            historicTaskRepository.loadHistoricVariables(taskDo);
        }
        // 计算操作按钮
        List<UserTaskOperation> userTaskOperations = taskDomainService.calculateTaskOperations(taskDo,
                processInstanceDo);
        TaskDetailRepsBuilder builder = new TaskDetailRepsBuilder().taskDo(taskDo).processInstanceDo(processInstanceDo)
                .userTaskOperation(userTaskOperations);
        List<ApprovalStrategy> strategyList = calculateApprovalStrategy(processInstanceDo.getProcessWrapper(),
                taskDo.getUserTaskWrapper());
        builder.strategyList(strategyList);
        return builder.build();
    }

    @Override
    public TaskDetailHistoryResp detailHistory(String taskId, ClientEnum client) {
        // 查询待办任务
        BpmUser operator = IdentityUtil.currentUser();
        TaskDo taskDo = internalGetTaskDo(taskId, operator);
        String procInstId = taskDo.getProcessInstanceId();
        ProcessInstanceDo processInstanceDo = processInstanceRepository.queryProcessInstance(procInstId);
        if (Objects.isNull(processInstanceDo)) {
            processInstanceDo = historicProcInstRepository.queryHistoricProcInst(taskDo.getProcessInstanceId());
            historicProcInstRepository.loadProcessVariables(processInstanceDo);
        } else {
            processInstanceDomainService.loadProcessVariables(processInstanceDo);
        }
        String status = (String) processInstanceDo.getProcessVariables()
                .get(BpmVariablesConstants.VARIABLE_PROC_INST_STATUS);
        ProcessInstanceStatus processInstanceStatus = ProcessInstanceStatus.getStatus(status,
                processInstanceDo.getEndTime());
        BpmnModel bpmnModel = bpmnModelService.getBpmnModelByProcessDefId(taskDo.getProcessDefinitionId());
        List<List<TaskDo>> historicTasks = historicTaskRepository.queryHistoricActivitiesByProcInstId(procInstId);
        TaskDetailHistoryResp resp = new TaskDetailHistoryResp();
        resp.setProcessInstanceStatus(processInstanceStatus);
        resp.setModelCode(processInstanceDo.getModelCode());
        List<TaskDetailHistoryResp.FlowNodeResp> taskNodes = new ArrayList<>();
        for (List<TaskDo> taskList : historicTasks) {
            groupTasks(taskList, resp, bpmnModel, taskNodes);
        }
        resp.setNodeList(taskNodes);
        return resp;
    }

    private void groupTasks(List<TaskDo> taskList, TaskDetailHistoryResp resp, BpmnModel bpmnModel, List<TaskDetailHistoryResp.FlowNodeResp> taskNodes) {
        if (CollectionUtils.isEmpty(taskList)) {
            return;
        }
        TaskDo first = taskList.get(0);
        resp.setProcessInstanceId(first.getProcessInstanceId());
        TaskDetailHistoryResp.FlowNodeResp nodeResp = new TaskDetailHistoryResp.FlowNodeResp();
        nodeResp.setNodeId(first.getTaskDefinitionKey());
        nodeResp.setName(first.getTaskName());
        List<TaskDo> nodeTaskList = new ArrayList<>();
        for (TaskDo task : taskList) {
            taskDomainService.loadActivityType(task, bpmnModel);
            taskDomainService.loadUserTaskWrapper(task, bpmnModel);
            taskDomainService.loadTaskCandidates(task);
            if (Objects.nonNull(task.getUserTaskWrapper())) {
                nodeResp.setSignType(task.getUserTaskWrapper().getSignType());
            }
            if ((null == task.getAssignee() || StringUtils.isBlank(task.getAssignee().getUid()))
                    && Objects.nonNull(task.getOwner())) {
                continue;
            }
            EmptyApproverStrategy emptyApproverStartegy = getEmptyApproverStartegy(bpmnModel, task);
            if (StringUtils.equals(BpmCommonConstants.ROBOT, task.getAssignee().getUserName())
                    && emptyApproverStartegy != null && BooleanUtils.isTrue(emptyApproverStartegy.getHideNode())) {
                return;
            }
            if (UserTaskSignType.ANONYMOUS_VOTE.equals(task.getSignType()) || UserTaskSignType.VOTE.equals(task.getSignType())) {
                if (null == task.getEndTime()) {
                    taskDomainService.loadTaskVariablesLocal(task);
                    Map<String, Object> taskVariables = task.getTaskLocalVariables();
                    if (null != taskVariables) {
                        Boolean vetoTask =
                                (Boolean) taskVariables.get(BpmVariablesConstants.VARIABLE_VOTE_IS_VETO_TASK);
                        task.setIsVetoUser(BooleanUtils.isTrue(vetoTask));
                    }
                }
                if (UserTaskSignType.ANONYMOUS_VOTE.equals(task.getSignType()) && !task.getAssignee().getUid().equals(IdentityUtil.currentUid())) {
                    task.setAssignee(null);
                }
            }
            nodeTaskList.add(task);
        }
        loadUpcomingSequentialTasks(nodeResp, nodeTaskList);
        nodeResp.setTaskList(taskInstConverter.doToDto(nodeTaskList));
        if (taskList.stream()
                .allMatch(t -> USER_TASK.equals(t.getActivityType())
                        && (null == t.getAssignee() || StringUtils.isBlank(t.getAssignee().getUid()))
                        && CollectionUtils.isEmpty(t.getCandidates()))
                && !UserTaskSignType.ANONYMOUS_VOTE.equals(nodeResp.getSignType())) {
            return;
        }
        taskNodes.add(nodeResp);
    }

    /**
     * 加载即将生成的顺序任务节点
     *
     * @param nodeResp 节点响应
     * @param nodeTaskList 节点任务列表
     */
    private void loadUpcomingSequentialTasks(TaskDetailHistoryResp.FlowNodeResp nodeResp, List<TaskDo> nodeTaskList) {
        Optional<TaskDo> any = nodeTaskList.stream().filter(i -> null == i.getEndTime()).findAny();
        if (UserTaskSignType.SEQUENTIAL.equals(nodeResp.getSignType()) && any.isPresent()) {
            long taskCount = nodeTaskList.stream().filter(t -> null == t.getParentTaskId()).count();
            TaskDo taskDo = any.get();
            taskDomainService.loadTaskVariables(taskDo);
            Map<String, Object> variables = taskDo.getTaskVariables();
            List<String> assignees = (List<String>) variables.get(BpmVariablesConstants.VARIABLE_SYS_MULTI_ASSIGNEES);
            if (CollectionUtils.isNotEmpty(assignees) && taskCount < assignees.size()) {
                List<String> subList = assignees.subList((int) taskCount, assignees.size());
                for (String s : subList) {
                    TaskDo sequentialTask = new TaskDo();
                    BeanUtils.copyProperties(taskDo, sequentialTask);
                    sequentialTask.setTaskId(null);
                    sequentialTask.setCreateTime(null);
                    sequentialTask.setEndTime(null);
                    sequentialTask.setAssignee(accountRemoteService.getUser(s));
                    sequentialTask.setPredict(true);
                    nodeTaskList.add(sequentialTask);
                }
            }
        }
    }

    @Override
    public TaskDetailOperationHistoryResp detailOperationHistory(String taskId, ClientEnum client) {
        BpmUser operator = IdentityUtil.currentUser();
        TaskDo taskDo = internalGetTaskDo(taskId, operator);
        List<OperationHistoryDo> historyDoList = operationHistoryRepository.queryByProcInst(
                taskDo.getProcessInstanceId());
        BpmnModel bpmnModel = bpmnModelService.getBpmnModelByProcessDefId(taskDo.getProcessDefinitionId());
        TaskDetailOperationHistoryResp resp = new TaskDetailOperationHistoryResp();
        resp.setProcessInstanceId(taskDo.getProcessInstanceId());
        List<TaskDetailOperationHistoryResp.TaskOperateHistoryResp> list = operationHistoryDtoConverter.toList(
                historyDoList);
        resp.setTaskList(list);
        for (TaskDetailOperationHistoryResp.TaskOperateHistoryResp historyResp : list) {
            String taskDefinitionKey = historyResp.getTaskDefinitionKey();
            FlowElement flowElement = bpmnModel.getFlowElement(taskDefinitionKey);
            if (flowElement instanceof UserTask) {
                historyResp.setActivityType(USER_TASK);
                UserTaskWrapper userTaskWrapper = bpmnExtensionHelper.getUserTaskWrapper((UserTask) flowElement);
                if (null != userTaskWrapper) {
                    historyResp.setTaskName(
                            userTaskWrapper.getLocalizationName(LocaleContextHolder.getLocale().getLanguage()));
                    historyResp.setSignType(userTaskWrapper.getSignType());
                    if (UserTaskSignType.ANONYMOUS_VOTE.equals(userTaskWrapper.getSignType())) {
                        historyResp.setOperator(null);
                        historyResp.setAssignee(null);
                    }
                }
                List<EventIdentify> signatureOperationList = Lists.newArrayList(EventIdentify.OPERATE_APPROVED,
                        EventIdentify.OPERATE_SIGNED_NEW, EventIdentify.OPERATE_RESOLED);
                if (null != historyResp.getOperator() && signatureOperationList.contains(historyResp.getOperation())) {
                    String signature =
                            userTaskSignatureAbility.findByUserIdAndTaskId(historyResp.getOperator().getUserName(),
                                    historyResp.getTaskId());
                    historyResp.setSignature(signature);
                }
            } else if (flowElement instanceof ReceiveTask) {
                ReceiveTaskWrapper receiveTaskWrapper = bpmnExtensionHelper.getReceiveTaskWrapper(
                        (ReceiveTask) flowElement);
                historyResp.setActivityType(ActivityTypeEnum.RECEIVE_TASK);
                if (null != receiveTaskWrapper) {
                    historyResp.setTaskName(
                            receiveTaskWrapper.getLocalizationName(LocaleContextHolder.getLocale().getLanguage()));
                }
            } else if (flowElement instanceof ServiceTask) {
                historyResp.setActivityType(ActivityTypeEnum.SERVICE_TASK);
                ServiceTaskWrapper serviceTaskWrapper = bpmnExtensionHelper.getServiceTaskWrapper(
                        (ServiceTask) flowElement);
                if (null != serviceTaskWrapper) {
                    historyResp.setTaskName(
                            serviceTaskWrapper.getLocalizationName(LocaleContextHolder.getLocale().getLanguage()));
                }
            }
        }
        return resp;
    }

    @Override
    public TaskDetailHistoryResp detailPredict(String taskId, ClientEnum client) {
        // 查询待办任务
        BpmUser operator = IdentityUtil.currentUser();
        TaskDo taskDo = internalGetTaskDo(taskId, operator);
        BpmnModel bpmnModel = bpmnModelService.getBpmnModelByProcessDefId(taskDo.getProcessDefinitionId());
        // 查询流程实例
        ProcessInstanceDo processInstanceDo = processInstanceRepository.queryProcessInstance(
                taskDo.getProcessInstanceId());
        if (Objects.isNull(processInstanceDo)) {
            processInstanceDo = historicProcInstRepository.queryHistoricProcInst(taskDo.getProcessInstanceId());
        }
        if (null == processInstanceDo.getEndTime()) {
            List<TaskDo> taskDos = taskDomainService.queryTaskList(processInstanceDo.getProcessInstanceId());
            List<String> collect = taskDos.stream().map(TaskDo::getTaskDefinitionKey).collect(Collectors.toList());
            List<TaskDo> predictTasks = taskDomainService.syncLoadPredictTasksWithKeys(taskDo, collect);
            // 加载历史审批任务的节点配置
            predictTasks.forEach(historicTask -> {
                taskDomainService.loadUserTaskWrapper(historicTask, bpmnModel);
                taskDomainService.loadActivityType(historicTask, bpmnModel);
                historicTask.setSignType(historicTask.getSignType());
                if (UserTaskSignType.ANONYMOUS_VOTE.equals(historicTask.getSignType())) {
                    historicTask.setAssignee(null);
                }
            });
            List<List<TaskDo>> predictLists = groupTasks(predictTasks);
            return taskInstConverter.listToDto(predictLists);
        }
        return null;
    }

    @Override
    public VoteConfigResp voteConfig(String procInstId, String taskDefKey) {
        VoteConfigResp voteConfigResp = new VoteConfigResp();
        if (StringUtils.isBlank(procInstId) || StringUtils.isBlank(taskDefKey)) {
            return voteConfigResp;
        }
        ProcessInstanceDo processInstanceDo = historicProcInstRepository.queryHistoricProcInst(procInstId);
        if (null == processInstanceDo) {
            return voteConfigResp;
        }
        processInstanceRepository.loadProcessWrapper(processInstanceDo);
        UserTask userTask = bpmnModelService.findUserTask(processInstanceDo.getProcessDefinitionId(), taskDefKey);
        UserTaskWrapper userTaskWrapper = bpmnExtensionHelper.getUserTaskWrapper(userTask);
        voteConfigResp.setVoteRuleConfigFe(userTaskWrapper.getVoteRuleConfigFe());
        List<TaskDo> tasks = historicTaskRepository.queryHistoricTasksByProcInstId(procInstId);
        if (CollectionUtils.isNotEmpty(tasks)) {
            List<TaskDo> taskList = tasks.stream()
                    .filter(task -> taskDefKey.equals(task.getTaskDefinitionKey()))
                    .collect(Collectors.toList());
            TaskDo runTask = taskList.stream()
                    .filter(task -> task.getEndTime() == null)
                    .findAny()
                    .orElse(null);
            List<BpmUser> vetoAssignees = new ArrayList<>();
            // 待办和已办查询方法不一样，因为待办任务的description修改了，但是没同步到hi_task表查不到
            if (null != runTask) {
                Execution execution =
                        runtimeService.createExecutionQuery().executionId(runTask.getExecutionId()).singleResult();
                if (null != execution) {
                    String rootExecutionId = execution.getParentId();
                    List<String> userIds = (List<String>) runtimeService.getVariable(rootExecutionId,
                            BpmVariablesConstants.VARIABLE_SYS_VETO_ASSIGNEES);
                    if (CollectionUtils.isNotEmpty(userIds)) {
                        List<BpmUser> bpmUsers = accountRemoteService.listUsers(userIds);
                        vetoAssignees.addAll(bpmUsers);
                    }
                }
            } else {
                vetoAssignees = taskList.stream().filter(t -> BooleanUtils.isTrue(t.getIsVetoUser()))
                        .map(TaskDo::getAssignee)
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(vetoAssignees)) {
                voteConfigResp.setVetoAssignees(vetoAssignees);
            }
        }
        return voteConfigResp;
    }

    private List<List<TaskDo>> groupTasks(List<TaskDo> taskDoList) {
        List<List<TaskDo>> taskNodes = new ArrayList<>();
        for (int i = 0; i < taskDoList.size(); ) {
            TaskDo a = taskDoList.get(i);
            LinkedList<TaskDo> list = new LinkedList<>();
            taskNodes.add(list);
            list.add(a);
            int j = i + 1;
            while (j < taskDoList.size()) {
                TaskDo b = taskDoList.get(j);
                if (StringUtils.equals(b.getTaskDefinitionKey(), a.getTaskDefinitionKey())) {
                    list.add(b);
                    j++;
                } else {
                    break;
                }
            }
            i = j;
        }
        return taskNodes;
    }

    @NotNull
    public static Integer calculateOldType(ProcessInstanceDo processInstanceDo, ModelDto modelDto) {
        Integer fromOld;
        fromOld = modelDto.getFromOld();
        if (fromOld == 0) {
            Integer processDefinitionVersion = processInstanceDo.getProcessDefinitionVersion();
            if (null != processDefinitionVersion && null != modelDto.getMigrationProcDefVersion()) {
                if (processDefinitionVersion > modelDto.getMigrationProcDefVersion()) {
                    fromOld = 0;
                } else {
                    fromOld = 2;
                    ProcessItem processItem = ((ModelMetaRepository) SpringContextUtil.getBean(ModelMetaRepository.class))
                            .queryProcessItemByModelCode(modelDto.getModelCode());
                    if (processItem != null && processItem.getUseFreeForm() == 0) {
                        fromOld = 1;
                    }
                }
            }
        }
        return fromOld;
    }

    private List<ApprovalStrategy> calculateApprovalStrategy(ProcessWrapper processWrapper, UserTaskWrapper userTaskWrapper) {
        List<ApprovalStrategy> strategyList = new ArrayList<>();
        if (null == userTaskWrapper) {
            return strategyList;
        }
        if (null != userTaskWrapper.getRejectStrategy()) {
            strategyList.add(userTaskWrapper.getRejectStrategy());
        } else if (null != processWrapper.getRejectStrategy()) {
            strategyList.add(processWrapper.getRejectStrategy());
        }
        RollbackStrategy rollbackStrategy = userTaskWrapper.getRollbackStrategy();
        if (null != rollbackStrategy) {
            rollbackStrategy.setName(BpmnExtensionElements.USER_TASK_ROLLBACK_STRATEGY.getKey());
            strategyList.add(rollbackStrategy);
        }
        if (null != userTaskWrapper.getApprovalSign()) {
            strategyList.add(userTaskWrapper.getApprovalSign());
        }
        if (null != userTaskWrapper.getAddSignSign()) {
            strategyList.add(userTaskWrapper.getAddSignSign());
        }
        return strategyList;
    }

    private EmptyApproverStrategy getEmptyApproverStartegy(BpmnModel bpmnModel, TaskDo taskDo) {
        ProcessWrapper processWrapper = bpmnExtensionHelper.getProcessWrapper(bpmnModel.getMainProcess());
        UserTaskWrapper userTaskWrapper = taskDo.getUserTaskWrapper();
        EmptyApproverStrategy globalEmptyStrategy = processWrapper.getEmptyApproverStrategy();
        EmptyApproverStrategy nodeEmptyStrategy = userTaskWrapper.getEmptyApproverStrategy();
        if (null == globalEmptyStrategy && null == nodeEmptyStrategy) {
            return null;
        } else if (null != nodeEmptyStrategy) {
            if (nodeEmptyStrategy.getPolicyType().equals(EmptyApproverStrategy.GLOBAL)) {
                return globalEmptyStrategy;
            }
            return nodeEmptyStrategy;
        } else {
            return globalEmptyStrategy;
        }
    }
}
