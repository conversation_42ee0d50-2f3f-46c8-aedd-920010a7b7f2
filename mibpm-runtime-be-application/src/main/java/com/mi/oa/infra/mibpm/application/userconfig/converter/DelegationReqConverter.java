package com.mi.oa.infra.mibpm.application.userconfig.converter;

import com.mi.oa.infra.mibpm.application.userconfig.dto.req.CreateDelegationReq;
import com.mi.oa.infra.mibpm.application.userconfig.dto.req.QueryDelegationReq;
import com.mi.oa.infra.mibpm.domain.userconfig.model.DelegationConfig;
import com.mi.oa.infra.mibpm.domain.userconfig.model.DelegationConfig.DelegationConfigBuilder;
import org.mapstruct.Mapper;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: qiuzhipeng
 * @Date: 2022/3/21 17:52
 */
@Mapper(componentModel = "spring")
public interface DelegationReqConverter {

    default DelegationConfig dtoTodo(CreateDelegationReq createDelegationReq){
        if ( createDelegationReq == null ) {
            return null;
        }

        DelegationConfigBuilder delegationDo = DelegationConfig.builder();

        delegationDo.userId( createDelegationReq.getUserId() );
        delegationDo.delegationUserId( createDelegationReq.getDelegationUserId() );
        List<String> list = createDelegationReq.getModelCode();
        if ( list != null ) {
            delegationDo.modelCode( new ArrayList<String>( list ) );
        }
        delegationDo.startTime(ZonedDateTime.ofInstant(new Date(Long.valueOf(createDelegationReq.getStartTime())).toInstant(), ZoneId.systemDefault()));
        delegationDo.endTime(ZonedDateTime.ofInstant(new Date(Long.valueOf(createDelegationReq.getEndTime())).toInstant(), ZoneId.systemDefault()));
        delegationDo.type( createDelegationReq.getType() );

        return delegationDo.build();
    }

    default DelegationConfig dtoTodo(QueryDelegationReq queryDelegationReq){
        if ( queryDelegationReq == null ) {
            return null;
        }

        DelegationConfigBuilder delegationDo = DelegationConfig.builder();

        delegationDo.userId( queryDelegationReq.getUserId() );
        delegationDo.delegationUserId( queryDelegationReq.getDelegationUserId() );
        if ( queryDelegationReq.getStartTime() != null ) {
            delegationDo.startTime( ZonedDateTime.ofInstant(new Date(Long.valueOf(queryDelegationReq.getStartTime())).toInstant(), ZoneId.systemDefault()));
        }
        if ( queryDelegationReq.getEndTime() != null ) {
            delegationDo.endTime(ZonedDateTime.ofInstant(new Date(Long.valueOf(queryDelegationReq.getEndTime())).toInstant(), ZoneId.systemDefault()));
        }

        return delegationDo.build();
    }
}
