package com.mi.oa.infra.mibpm.application.procinst.converter;

import com.mi.oa.infra.mibpm.application.proinst.dto.reps.OpenQueryProcInstResp;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/6 11:19
 */
@Mapper(componentModel = "spring")
public interface OpenQueryProcInstRespConverter {

    OpenQueryProcInstResp dtoToDto(ProcessInstanceDo processInstanceDo);

    List<OpenQueryProcInstResp> doListToDtoList(List<ProcessInstanceDo> processInstanceDoList);

    PageModel<OpenQueryProcInstResp> doPageModelToDtoPageModel(PageModel<ProcessInstanceDo> pageModel);
}
