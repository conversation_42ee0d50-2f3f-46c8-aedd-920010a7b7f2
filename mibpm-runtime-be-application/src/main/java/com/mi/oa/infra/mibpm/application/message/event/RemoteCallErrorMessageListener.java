package com.mi.oa.infra.mibpm.application.message.event;

import com.larksuite.appframework.sdk.client.message.card.TemplateColor;
import com.larksuite.appframework.sdk.client.message.card.objects.I18n;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.RemoteCallErrorEvent;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo.LarkMessageDoBuilder;
import com.mi.oa.infra.mibpm.domain.message.service.LarkMessageDomainService;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.infra.procinst.repository.HistoricProcInstRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 服务调用错误监听器
 *
 * <AUTHOR>
 * @date 2022/4/06 20:51
 */
@Slf4j
@Component
public class RemoteCallErrorMessageListener extends AbstractMessageListener<RemoteCallErrorEvent> {

    @Autowired
    private LarkMessageDomainService larkMessageDomainService;
    @Autowired
    private HistoricProcInstRepository historicProcInstRepository;

    @Override
    public String identifier() {
        return EventIdentify.REMOTE_CALL_ERROR.name();
    }

    @Override
    public void process(RemoteCallErrorEvent event) {
        log.info("消息模块消费服务调用错误事件，event = {}", event);
        List<String> adminUsers = event.getAdminUsers();
        if(CollectionUtils.isEmpty(adminUsers)){
            return;
        }
        // 由事件构建消息实例
        LarkMessageDo larkMessageDo = buildLarkMessage(event);
        // 发送消息
        adminUsers.forEach((user)->{
            larkMessageDo.setUsername(user);
            // 检查消息实例
            larkMessageDomainService.checkLarkMessage(larkMessageDo);
            larkMessageDomainService.sendMessageCard(larkMessageDo);
        });
    }

    private LarkMessageDo buildLarkMessage(RemoteCallErrorEvent event) {

        LarkMessageDoBuilder builder = LarkMessageDo.builder();
        // 获取流程实例
        ProcessInstanceDo instance = historicProcInstRepository
                .queryHistoricProcInst(event.getProcessInstanceId());
        if(Objects.isNull(instance)){
            return null;
        }
        builder.templateColor(TemplateColor.RED);
        buildContent(builder, event, instance);
        buildTitle(builder);
        builder.eventType(EventIdentify.REMOTE_CALL_ERROR);
        return builder.build();
    }

    /**
     * 构建流程标题
     * @param builder
     */
    private void buildTitle(LarkMessageDoBuilder builder) {
        String title = String.format("BPM服务调用失败告警");
        builder.title(new I18n(title, title, title));
    }

    /**
     * 构建业务数据区
     * @return
     */
    private void buildContent(LarkMessageDoBuilder builder, RemoteCallErrorEvent event, ProcessInstanceDo instance) {
        String message = "**服务名称:** " + event.getTemplateName()+
                "\n**流程名称:** " + instance.getProcessInstanceName()+
                "\n**流程ID:**" + instance.getProcessInstanceId()+
                "\n**消息ID:**" + event.getSequenceId()+
                "\n**调用时间:**" + event.getTime();
        builder.customizeContent(new I18n(message, message, message));
    }
}
