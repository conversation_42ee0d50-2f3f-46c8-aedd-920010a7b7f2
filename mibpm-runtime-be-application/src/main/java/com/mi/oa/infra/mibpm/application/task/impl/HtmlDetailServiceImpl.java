package com.mi.oa.infra.mibpm.application.task.impl;

import com.mi.oa.infra.mibpm.application.task.converter.TaskDetailRepsBuilder;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailResp;
import com.mi.oa.infra.mibpm.application.task.service.HtmlDetailService;
import com.mi.oa.infra.mibpm.common.enums.ClientEnum;
import com.mi.oa.infra.mibpm.common.exception.DomainException;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.ProcessTemplateContent;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.task.errorcode.TaskDomainErrorCodeEnum;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.domain.task.service.TaskDomainService;
import com.mi.oa.infra.mibpm.infra.procinst.repository.HistoricProcInstRepository;
import com.mi.oa.infra.mibpm.infra.procinst.repository.HtmlFormContentRepository;
import com.mi.oa.infra.mibpm.infra.procinst.repository.ProcInstPinRepository;
import com.mi.oa.infra.mibpm.utils.IdentityUtil;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import freemarker.cache.StringTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.DefaultObjectWrapper;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import freemarker.template.TemplateMethodModelEx;
import freemarker.template.TemplateModelException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.StringWriter;
import java.io.Writer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class HtmlDetailServiceImpl implements HtmlDetailService {

    @Value("${mibpm.larkTodoUrlFrom:http://api.miren.test.mi.com}")
    private String larkTodoUrlFrom;
    @Value("${mibpm.larkTodoUrlTo:http://api.test.mi.com/mioffice/workflow/mibpm/api}")
    private String larkTodoUrlTo;

    @Autowired
    private TaskDomainService taskDomainService;
    @Autowired
    private HistoricProcInstRepository historicProcInstRepository;
    @Autowired
    private ProcInstPinRepository procInstPinRepository;
    @Autowired
    private HtmlFormContentRepository htmlFormContentRepository;

    @Override
    public TaskDetailResp xmlDetailForm(String taskId, ClientEnum client) {
        // 查询待办任务
        BpmUser operator = IdentityUtil.currentUser();
        TaskDo taskDo = internalGetTaskDo(taskId, operator);
        TaskDetailRepsBuilder builder = new TaskDetailRepsBuilder().taskDo(taskDo);
        TaskDetailResp.Form form = new TaskDetailResp.Form();
        String processInstanceId = taskDo.getProcessInstanceId();

        // 小程序
        if (ClientEnum.WEB.equals(client)) {
            buildPcHtml(taskDo, form);
        } else {
            String pinStatus = procInstPinRepository.getProcPinStatus(processInstanceId,
                    IdentityUtil.currentUserName());
            builder.pinStatus(pinStatus == null ? "unpin" : pinStatus);

            buildAppDetailXml(taskDo, form);
        }
        builder.form(form);
        // 组装任务详情
        return builder.build();
    }

    @NotNull
    public TaskDo internalGetTaskDo(String taskId, BpmUser operator) {
        // 查询待办任务
        TaskDo taskDo = taskDomainService.queryTaskDo(taskId);
        if (Objects.isNull(taskDo)) {
            throw new DomainException(TaskDomainErrorCodeEnum.TASK_NOT_EXISTS, taskId);
        }
        // 填充任务操作人
        taskDomainService.fillTaskOperator(taskDo, operator);
        // 加载任务配置
        taskDomainService.loadUserTaskWrapper(taskDo);
        // 加载审批类型
        taskDomainService.loadActivityType(taskDo);
        // 加载候选人
        taskDomainService.loadTaskCandidates(taskDo);
        // 检查流程查看权限
        taskDomainService.checkTaskReadPermission(taskDo);
        return taskDo;
    }

    private String buildPcHtml(TaskDo taskDo, TaskDetailResp.Form form) {
        ProcessInstanceDo processInstanceDo = historicProcInstRepository.queryHistoricProcInst(taskDo.getProcessInstanceId());
        String businessKey = processInstanceDo.getBusinessKey();

        ProcessTemplateContent content = htmlFormContentRepository
                .getContent(ClientEnum.WEB, businessKey, taskDo.getModelCode());
        if (content == null || StringUtils.isBlank(content.getTemplateContent())) {
            return "";
        }
        Document document = Jsoup.parse(content.getTemplateContent());

        // 查找所有class为"box"的div标签
        String boxContent = "";
        Elements boxElements = document.select("div.box");
        if (!boxElements.isEmpty()) {
            boxContent = Objects.requireNonNull(boxElements).outerHtml();
        } else {
            Elements body = document.select("body");
            if (!body.isEmpty()) {
                boxContent = Objects.requireNonNull(body).outerHtml();
            }
        }
        try {
            String xml = fillXml(businessKey, boxContent, processInstanceDo, content);
            form.setPcContent(xml);
            form.setData(GsonUtils.fromJson(content.getBusiness(), Map.class));
        } catch (Exception e) {
            log.error("buildPcDetailXml error", e);
        }
        return "";
    }

    private String buildAppDetailXml(TaskDo taskDo, TaskDetailResp.Form form) {
        String appDetailXml = "<Root><HEAD><content ></content></HEAD></Root>";
        try {
            String appContent = "";
            Map<String, Object> context = null;
            ProcessInstanceDo processInstanceDo = historicProcInstRepository.queryHistoricProcInst(taskDo.getProcessInstanceId());
            String businessKey = processInstanceDo.getBusinessKey();
            ProcessTemplateContent content = htmlFormContentRepository.getContent(ClientEnum.APP, businessKey, taskDo.getModelCode());

            if (content == null || StringUtils.isBlank(content.getTemplateContent())) {
                return appDetailXml;
            }
            appContent = content.getTemplateContent();

            appDetailXml = fillXml(businessKey, appContent, processInstanceDo, content);

            String regexPattern = "<!--.[^-]*(?=-->)-->";
            appDetailXml = appDetailXml.replaceAll(larkTodoUrlFrom, larkTodoUrlTo);
            appDetailXml = appDetailXml.replaceAll(regexPattern, "");
            //替换不可见字符为空格
            appDetailXml = appDetailXml.replaceAll("\\s+", " ");
            //部分模板明细存在多余的逗号
            appDetailXml = appDetailXml.replaceAll(",\\s{0,1}]", "]");
            form.setData(GsonUtils.fromJson(content.getBusiness(), Map.class));
        } catch (Exception e) {
            log.error("buildAppDetailXml error", e);
        }

        form.setAppContent(appDetailXml);
        return appDetailXml;
    }

    public static String fillXml(String businessKey, String boxContent,
                                 ProcessInstanceDo processInstanceDo, ProcessTemplateContent content)
            throws IOException, TemplateException {
        Map<String, Object> context;
        Configuration cfg = new Configuration();
        cfg.setBooleanFormat("yes,no");
        StringTemplateLoader templateLoader = new StringTemplateLoader();
        templateLoader.putTemplate(businessKey, boxContent);
        cfg.setTemplateLoader(templateLoader);
        cfg.setObjectWrapper(new DefaultObjectWrapper());
        cfg.setClassicCompatible(true);
        Template template = cfg.getTemplate(businessKey);

        //创建数据模型
        context = new HashMap<>();
        context.put("title", processInstanceDo.getProcessInstanceName());
        if(content.getBusiness().startsWith("[")){
            context.put("business", GsonUtils.fromJson(content.getBusiness(), ArrayList.class));
        }else{
            context.put("business", GsonUtils.fromJson(content.getBusiness(), Map.class));
        }
        context.put("nameOfClass", new GetClassNameTMM());

        Writer out = new StringWriter();
        template.process(context, out);
        return out.toString();
    }

    static class GetClassNameTMM implements TemplateMethodModelEx {
        @Override
        public Object exec(List list) {
            if (list.size() != 1) {
                return new TemplateModelException("wrong param number, must be 1!");
            }
            return list.get(0).getClass().getSimpleName();
        }
    }
}
