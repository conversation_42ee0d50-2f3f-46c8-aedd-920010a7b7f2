package com.mi.oa.infra.mibpm.application.message.event;

import com.larksuite.appframework.sdk.client.message.card.TemplateColor;
import com.larksuite.appframework.sdk.client.message.card.objects.I18n;
import com.mi.oa.infra.mibpm.common.constant.LarkMessageConstants;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.TaskTimeoutEvent;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.Actions;
import com.mi.oa.infra.mibpm.domain.message.model.Content;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo;
import com.mi.oa.infra.mibpm.domain.message.service.LarkMessageDomainService;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.procinst.service.ProcessInstanceDomainService;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.infra.task.repository.HistoricTaskRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/13 7:55 PM
 **/
@Component
@Slf4j
public class TaskTimeoutRemindMessageListener extends AbstractMessageListener<TaskTimeoutEvent> {

    @Autowired
    private LarkMessageDomainService larkMessageDomainService;
    @Autowired
    private ProcessInstanceDomainService processInstanceDomainService;
    @Autowired
    private HistoricTaskRepository historicTaskRepository;


    @Override
    public String identifier() {
        return EventIdentify.TIMEOUT_REMIND.name();
    }

    @Override
    public void process(TaskTimeoutEvent event) {
        log.info("消息模块消费任务超时提醒事件，event = {}", event);
        // 由事件构建消息实例
        LarkMessageDo larkMessageDo = this.buildLarkMessage(event);
        // 检查消息实例
        larkMessageDomainService.checkLarkMessage(larkMessageDo);
        // 发送消息
        larkMessageDomainService.sendMessageCard(larkMessageDo, null);
    }


    /**
     * 构建消息实例对象
     *
     * @param event
     * @return
     */
    public LarkMessageDo buildLarkMessage(TaskTimeoutEvent event) {
        LarkMessageDo.LarkMessageDoBuilder builder = LarkMessageDo.builder();
        // 消息接收人
        builder.username(event.getTargetUser());
        builder.templateColor(TemplateColor.BLUE);
        // 获取流程实例
        ProcessInstanceDo instance = processInstanceDomainService
                .queryProcessInstance(event.getProcessInstanceId());
        BpmUser startUser = instance.getStartUser();
        if (Objects.isNull(startUser)) {
            return null;
        }
        buildTitle(builder, event.getTimeoutHours());

        Content content = buildContent(startUser);
        builder.content(content);
        // 构建流程信息
        buildInstanceInfo(event, builder, instance);
        // 构建按钮
        buildActions(builder);
        builder.eventType(EventIdentify.TIMEOUT_REMIND);
        LarkMessageDo messageDo = builder.build();
        parseSummaryAndAction(event.getTaskId(), messageDo);
        return messageDo;
    }

    /**
     * 构建流程标题
     *
     * @param builder
     */
    private void buildTitle(LarkMessageDo.LarkMessageDoBuilder builder, Long timeout) {
        // 构建title
        String title = String.format("审批任务已过期%s小时，请及时处理", timeout);
        String enTitle = String.format("The task has timed out for %s hour(s). Please check in time", timeout);
        builder.title(new I18n(title, enTitle, enTitle));
    }

    /**
     * 构建流程信息
     *
     * @param taskTimeoutEvent
     * @param builder
     * @param instance
     */
    private void buildInstanceInfo(TaskTimeoutEvent taskTimeoutEvent, LarkMessageDo.LarkMessageDoBuilder builder, ProcessInstanceDo instance) {
        TaskDo taskDo = historicTaskRepository.queryHistoricTask(taskTimeoutEvent.getTaskId());
        // 构建流程信息
        builder.processName(instance.getProcessInstanceName());
        builder.instanceId(taskTimeoutEvent.getProcessInstanceId());
        builder.startTime(instance.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        builder.taskId(taskTimeoutEvent.getTaskId());
        builder.taskName(taskDo.getTaskName());
        builder.taskDefKey(taskDo.getTaskDefinitionKey());
        builder.modelCode(instance.getModelCode());
        builder.categoryCode(instance.getCategoryCode());
    }

    /**
     * 构建业务数据区
     *
     * @param user
     * @return
     */
    private Content buildContent(BpmUser user) {

        // 构建content
        return Content.builder()
                .isApprove(false)
                .username(String.format("%s(%s)", user.getDisplayName(), user.getUserName()))
                .deptInfo(String.join("-", buildDeptInfo(user)))
                .build();
    }

    /**
     * 构建按钮区
     *
     * @param builder
     */
    private void buildActions(LarkMessageDo.LarkMessageDoBuilder builder) {

        List<Actions> actions = new ArrayList<>();
        // 填充审批按钮信息

        //设置查看详情按钮
        Actions detail = new Actions();
        detail.setActionKey(LarkMessageConstants.DETAIL);
        actions.add(detail);
        builder.actions(actions);
    }
}
