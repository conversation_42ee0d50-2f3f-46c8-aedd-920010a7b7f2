package com.mi.oa.infra.mibpm.application.message.event;

import com.google.common.collect.Lists;
import com.larksuite.appframework.sdk.client.message.card.TemplateColor;
import com.larksuite.appframework.sdk.client.message.card.objects.I18n;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.ProcInstStartedEvent;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.Actions;
import com.mi.oa.infra.mibpm.domain.message.model.Content;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo.LarkMessageDoBuilder;
import com.mi.oa.infra.mibpm.domain.message.service.LarkMessageDomainService;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.domain.procinst.service.ProcessInstanceDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Objects;

/**
 * 审批任务被同意监听器
 *
 * <AUTHOR>
 * @date 2022/3/18 20:51
 */
@Slf4j
@Component
public class ProInsStartedMessageListener extends AbstractMessageListener<ProcInstStartedEvent> {
    @Autowired
    private LarkMessageDomainService larkMessageDomainService;
    @Autowired
    private ProcessInstanceDomainService processInstanceDomainService;

    @Override
    public String identifier() {
        return EventIdentify.PROCESS_STARTED.name();
    }

    @Override
    public void process(ProcInstStartedEvent event) {
        log.info("消息模块消费流程发起事件，event = {}", event);
        // 由事件构建消息实例
        LarkMessageDo larkMessageDo = buildLarkMessage(event);
        // 检查消息实例
        larkMessageDomainService.checkLarkMessage(larkMessageDo);
        // 发送消息
        larkMessageDomainService.sendApprovalMessageCard(larkMessageDo);
    }

    private LarkMessageDo buildLarkMessage(ProcInstStartedEvent event) {

        LarkMessageDoBuilder builder = LarkMessageDo.builder();
        // 获取流程实例
        ProcessInstanceDo instance = processInstanceDomainService
                .queryProcessInstance(event.getProcessInstanceId());
        if (Objects.isNull(instance)) {
            return null;
        }
        BpmUser startUser = instance.getStartUser();
        builder.username(startUser.getUserName());
        builder.templateColor(TemplateColor.GREEN);
        buildInstanceInfo(event.getStartTaskId(), builder, instance, false);
        buildContent(startUser, builder, event.getStartTaskId());
        buildTitle(builder, startUser);
        buildActions(builder);
        builder.eventType(EventIdentify.PROCESS_STARTED);
        return builder.build();
    }

    /**
     * 构建流程标题
     *
     * @param builder
     * @param user
     */
    private void buildTitle(LarkMessageDoBuilder builder, BpmUser user) {
        String title = "您发起的流程已成功提交！";
        String enTitle = "Your process has been successfully submitted.";
        builder.title(new I18n(title, enTitle, enTitle));
    }


    /**
     * 构建业务数据区
     *
     * @param operator
     * @return
     */
    private void buildContent(BpmUser operator, LarkMessageDoBuilder builder, String taskId) {
        // 构建content
        Content content = Content.builder()
                .isApprove(false)
                .username(String.format("%s(%s)", operator.getDisplayName(), operator.getUserName()))
                .deptInfo(String.join("-", buildDeptInfo(operator)))
                .summaries(Lists.newArrayList(parseSummary(taskId)))
                .build();
        builder.content(content);
    }

    /**
     * 构建按钮区
     *
     * @param builder
     */
    private void buildActions(LarkMessageDoBuilder builder) {
        //设置查看详情按钮
        Actions detail = new Actions();
        detail.setActionKey("DETAIL");
        detail.setActionName("查看详情");
        builder.actions(Collections.singletonList(detail));
    }
}
