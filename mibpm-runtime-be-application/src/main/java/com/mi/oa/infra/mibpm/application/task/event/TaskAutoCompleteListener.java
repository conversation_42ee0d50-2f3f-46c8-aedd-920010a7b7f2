package com.mi.oa.infra.mibpm.application.task.event;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.mi.oa.infra.mibpm.application.task.dto.req.CompleteTaskReq;
import com.mi.oa.infra.mibpm.application.task.service.ApprovalService;
import com.mi.oa.infra.mibpm.common.enums.ClientEnum;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.TaskAutoCompleteEvent;
import com.mi.oa.infra.mibpm.domain.task.model.TaskDo;
import com.mi.oa.infra.mibpm.domain.task.service.TaskDomainService;
import com.mi.oa.infra.mibpm.eventbus.EventSubscriber;
import com.mi.oa.infra.mibpm.infra.remote.sdk.FormRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 自动跳过任务事件监听器
 *
 * <AUTHOR>
 * @date 2022/3/24 11:50
 */
@Slf4j
@Service
public class TaskAutoCompleteListener implements EventSubscriber<TaskAutoCompleteEvent> {

    @Autowired
    private ApprovalService approvalService;
    @Autowired
    private TaskDomainService taskDomainService;
    @Autowired
    private FormRemoteService formRemoteService;

    @Override
    public String identifier() {
        return EventIdentify.TASK_AUTO_COMPLETE.name();
    }

    @Override
    public void process(TaskAutoCompleteEvent event) {
        log.info("收到自动跳过任务事件，event = {}", event);

        // 查询查询第一个任务节点的任务
        TaskDo task = taskDomainService.queryTask(event.getTaskId());
        if (Objects.isNull(task)) {
            log.warn("[{}] 任务未找到，自动跳过操作终止!", event.getTaskId());
            return;
        }

        // 自动完成发起任务
        CompleteTaskReq completeTaskReq = new CompleteTaskReq();
        completeTaskReq.setTaskId(task.getTaskId());
        completeTaskReq.setClient(ClientEnum.SYSTEM);
        completeTaskReq.setFormData(CollectionUtils.isNotEmpty(event.getFormData()) ? event.getFormData() :
                formRemoteService.getFormData(task.getProcessInstanceId(), task.getTaskDefinitionKey()));
        completeTaskReq.setOperator(task.getAssignee());
        completeTaskReq.setComment(event.getComment());
        completeTaskReq.setNotSendLarkMessage(true);
        completeTaskReq.setCommentEn(event.getCommentEn());
        completeTaskReq.setAutoOperationType(event.getAutoOperationType());
        try {
            approvalService.approve(completeTaskReq);
            log.info("自动完成任务, taskId = [{}], operator = [{}]", task.getTaskId(), task.getAssignee());
        } catch (Exception e) {
            log.error("自动完成任务失败, taskId = [{}], operator = [{}]", task.getTaskId(), task.getAssignee(), e);
        }
    }
}
