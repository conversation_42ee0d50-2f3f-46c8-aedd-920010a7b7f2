package com.mi.oa.infra.mibpm.application.message.event;

import com.larksuite.appframework.sdk.client.message.card.TemplateColor;
import com.larksuite.appframework.sdk.client.message.card.objects.I18n;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.ApprovelIsEmptyEvent;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.domain.message.model.LarkMessageDo;
import com.mi.oa.infra.mibpm.domain.message.service.LarkMessageDomainService;
import com.mi.oa.infra.mibpm.domain.procinst.model.ProcessInstanceDo;
import com.mi.oa.infra.mibpm.infra.procinst.repository.HistoricProcInstRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/6/10
 */
@Slf4j
@Component
public class ApprovelIsEmptyListener extends AbstractMessageListener<ApprovelIsEmptyEvent> {
    @Autowired
    private LarkMessageDomainService larkMessageDomainService;
    @Autowired
    private HistoricProcInstRepository historicProcInstRepository;
    @Override
    public String identifier() {
        return EventIdentify.APPROVEL_IS_EMPTY.name();
    }

    @Override
    public void process(ApprovelIsEmptyEvent event) {
        log.info("消息模块消费审批人为空事件，event = {}", event);
        this.syncAppBadge(event.getProcessInstanceId(), event.getTaskDefinitionKey());
        // 由事件构建消息实例
        LarkMessageDo larkMessageDo = buildLarkMessage(event);
        // 检查消息实例
        larkMessageDomainService.checkLarkMessage(larkMessageDo);
        // 发送消息
        larkMessageDomainService.sendApprovalMessageCard(larkMessageDo);

    }

    private LarkMessageDo buildLarkMessage(ApprovelIsEmptyEvent event) {
        LarkMessageDo.LarkMessageDoBuilder builder = LarkMessageDo.builder();
        // 获取流程实例
        ProcessInstanceDo instance = historicProcInstRepository
                .queryHistoricProcInst(event.getProcessInstanceId());

        if (Objects.isNull(instance)) {
            return null;
        }

        BpmUser startUser = instance.getStartUser();
        BpmUser operator = event.getOperator();
        builder.username(startUser.getUserName());
        String comment = event.getComment();
        builder.comment(new I18n(comment, comment, comment));
        builder.templateColor(TemplateColor.GREEN);
        buildInstanceInfo(event.getTaskId(), builder, instance, true);
        builder.taskId(event.getTaskId());
        buildContent(operator, builder, event.getTaskId());
        buildTitle(builder, operator);
        buildActions(builder);
        builder.eventType(EventIdentify.OPERATE_SUBMITTED);
        return builder.build();


    }
}
