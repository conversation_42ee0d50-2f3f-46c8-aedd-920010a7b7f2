package com.mi.oa.infra.mibpm.application.mitask.converter;

import com.mi.oa.infra.mibpm.application.mitask.dto.resp.HistoricProcInstVoResp;
import com.mi.oa.infra.mibpm.application.mitask.dto.resp.HistoricTaskVoResp;
import com.mi.oa.infra.mibpm.application.mitask.dto.resp.TaskVoResp;
import com.mi.oa.infra.mibpm.common.enums.SourceEnum;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.domain.mitask.model.MiTaskDo;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/15
 * @Description
 */
@Mapper(componentModel = "spring", builder = @Builder(disableBuilder = true))
public interface MiTaskRespConverter {

    @Mappings({
            @Mapping(source = "procInstId", target = "processInstanceId"),
            @Mapping(source = "procDefId", target = "processDefinitionId"),
            @Mapping(source = "procInstName", target = "processInstanceName"),
            @Mapping(source = "taskDefKey", target = "taskDefinitionKey"),
            @Mapping(source = "procInstStarter", target = "processInstanceStartUser"),
            @Mapping(source = "isFastApproval", target = "operationList", qualifiedByName = "mapFastApprovalToOperationList"),
            @Mapping(source = "source", target = "extTask", qualifiedByName = "mapSourceToExtTask"),
            @Mapping(source = "source", target = "oldType", qualifiedByName = "mapSourceToOldType")
    })
    TaskVoResp convertMiTaskDoToTaskVoResp(MiTaskDo miTaskDo);

    @Mappings({
            @Mapping(source = "procInstId", target = "processInstanceId"),
            @Mapping(source = "procDefId", target = "processDefinitionId"),
            @Mapping(source = "taskDefKey", target = "taskDefinitionKey"),
            @Mapping(source = "procInstStarter", target = "processInstanceStartUser"),
            @Mapping(source = "source", target = "oldType", qualifiedByName = "mapSourceToOldType")
    })
    HistoricTaskVoResp convertMiTaskDoToHistoricTaskVoResp(MiTaskDo miTaskDo);

    @Mappings({
            @Mapping(source = "procInstId", target = "processInstanceId"),
            @Mapping(source = "procDefId", target = "processDefinitionId"),
            @Mapping(source = "modelCode", target = "processCode"),
            @Mapping(source = "procInstStarter", target = "startUserId"),
            @Mapping(source = "source", target = "oldType", qualifiedByName = "mapSourceToOldType"),
            @Mapping(source = "duration", target = "durationInMillis")
    })
    HistoricProcInstVoResp convertMiTaskDoToHistoricProcInstVoResp(MiTaskDo miTaskDo);

    @Mapping(source = "taskDefKey", target = "taskDefinitionKey")
    HistoricProcInstVoResp.HistoricTask miTaskDoToHistoricTaskResp(MiTaskDo miTaskDo);

    @Named("mapSourceToExtTask")
    default Integer mapSourceToExtTask(SourceEnum source) {
        if (source != null) {
            return source.equals(SourceEnum.UNIFIED_TODO_EXTERNAL) ? 1 : 0;
        }
        return null;
    }

    @Named("mapSourceToOldType")
    default Integer mapSourceToOldType(SourceEnum source) {
        if (source != null) {
            return source.equals(SourceEnum.BPM2_PROCESS) ? 1 : 0;
        }
        return null;
    }

    @Named("mapFastApprovalToOperationList")
    default List<UserTaskOperation> mapFastApprovalToOperationList(Boolean isFastApproval) {
        return isFastApproval ? Arrays.asList(UserTaskOperation.AGREE, UserTaskOperation.REJECT) : null;
    }

    default String mapBpmUserToUsername(BpmUser bpmUser) {
        return bpmUser.getUserName();
    }
}
