package com.mi.oa.infra.mibpm.application.operation.listener;

import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.TaskTimeoutEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/5/4 19:51
 **/
@Slf4j
@Component
public class TaskTimeoutAgreeHistoryListener extends AbstractOperationListener<TaskTimeoutEvent> {

    @Override
    public String identifier() {
        return EventIdentify.TIMEOUT_AGREE.name();
    }

}
