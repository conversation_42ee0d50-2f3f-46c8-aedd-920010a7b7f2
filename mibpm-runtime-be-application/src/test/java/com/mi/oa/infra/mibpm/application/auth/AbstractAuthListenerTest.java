package com.mi.oa.infra.mibpm.application.auth;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import com.mi.oa.infra.mibpm.application.ServiceTestApplication;
import com.mi.oa.infra.mibpm.domain.userconfig.model.ProcessViewAuthConfig;
import com.mi.oa.infra.mibpm.infra.userconfig.repository.UserConfigRepository;

@SpringBootTest(classes = {ServiceTestApplication.class})
class AbstractAuthListenerTest {
    @Autowired
    private UserConfigRepository userConfigRepository;

    @Autowired
    private TaskCcAuthListener authListener;

    @Test
    void testAssignDataPermission() {
        List<String> resourceCodes = new ArrayList<>();
        resourceCodes.add("BPMN_AUTH_PREFIX:procInstId:taskKey");
        resourceCodes.add("BPMN_AUTH_PREFIX:procInstId:taskKey1");
        resourceCodes.add("BPMN_AUTH_PREFIX:procInstId");
        List<String> assignUsers = new ArrayList<>();
        assignUsers.add("test");
        assignUsers.add("test1");

        authListener.assignDataPermission(resourceCodes, assignUsers);
        resourceCodes.forEach(resourceCode -> {
            String arr[] = resourceCode.split(":");
            String procInstId = arr[1];
            String taskKey = "";
            if(arr.length >= 3) {
                taskKey = arr[2];
            }
            final String t = taskKey;
            
            assignUsers.forEach(assignUser -> {
                assertTrue(userConfigRepository.hasProcessViewAuth(ProcessViewAuthConfig.builder()
                    .procInstId(procInstId)
                    .taskKey(t)
                    .userId(assignUser).build()
                ));
            });
        });
        authListener.revokeDataPermission(resourceCodes, assignUsers);
        resourceCodes.forEach(resourceCode -> {
            String arr[] = resourceCode.split(":");
            String procInstId = arr[1];
            String taskKey = "";
            if(arr.length >= 3) {
                taskKey = arr[2];
            }
            final String t = taskKey;
            assignUsers.forEach(assignUser -> {
                assertFalse(userConfigRepository.hasProcessViewAuth(ProcessViewAuthConfig.builder()
                    .procInstId(procInstId)
                    .taskKey(t)
                    .userId(assignUser).build()
                ));
            });
        });
    }
}