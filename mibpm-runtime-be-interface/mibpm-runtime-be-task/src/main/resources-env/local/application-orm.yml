# 日志配置
logging:
  level:
    com.baomidou.mybatisplus.samples: debug

# MyBatis-Plus 配置
# 对应配置类  MybatisPlusAutoConfiguration - MybatisPlusProperties
mybatis-plus:
  #myBatis mapper的文件路径
  mapper-locations: classpath:mapper/*.xml
  #别名数据对象包路径
  type-aliases-package: com.mi.oa.infra.mibpm.infra.repository.mybatis.entity
  type-handlers-package: com.mi.oa.infra.oaucf.mybatis.handler
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    # 设置默认枚举类型处理器
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
  # 对应配置类  MybatisPlusAutoConfiguration - MybatisPlusProperties - GlobalConfig
  global-config:
    banner: false
    db-config:
      logic-delete-value: 1
      logic-not-delete-value: 0
