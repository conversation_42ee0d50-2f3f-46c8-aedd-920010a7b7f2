package com.mi.oa.infra.mibpm.api.web;

import com.mi.oa.infra.mibpm.api.converter.ApiCallVoConverter;
import com.mi.oa.infra.mibpm.api.model.vo.RemoteFormDataVo;
import com.mi.oa.infra.mibpm.application.apicall.dto.reps.ApiCallHistoryReps;
import com.mi.oa.infra.mibpm.application.apicall.dto.reps.GetRemoteFormDataReps;
import com.mi.oa.infra.mibpm.application.apicall.dto.req.ApiCallHistoryQuery;
import com.mi.oa.infra.mibpm.application.apicall.dto.req.GetRemoteFormDataReq;
import com.mi.oa.infra.mibpm.application.apicall.dto.req.SaveRemoteFormDataReq;
import com.mi.oa.infra.mibpm.application.apicall.dto.req.X5DataSource;
import com.mi.oa.infra.mibpm.application.apicall.service.ApiCallService;
import com.mi.oa.infra.mibpm.common.model.ApiCallVariable;
import com.mi.oa.infra.mibpm.infra.remote.sdk.MiBpmRemoteService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 服务调用
 *
 * @author: qiuzhipeng
 * @Date: 2022/3/14 14:04
 */
@Api(tags = "服务调用")
@WebLog
@RestController
@RequestMapping("/api/v1/call")
public class ApiRemoteCallController {

    @Autowired
    private ApiCallService apiCallService;
    @Autowired
    private ApiCallVoConverter apiCallVoConverter;
    @Autowired
    private MiBpmRemoteService miBpmRemoteService;

    /**
     * 获取服务调用详情
     *
     * @return
     */
    @ApiOperation("获取服务调用详情")
    @GetMapping("/history/{id}")
    public BaseResp<ApiCallHistoryReps> getApiCallHistoryById(@PathVariable("id") Integer id) {
        return BaseResp.success(apiCallService.getApiCallHistoryById(id));
    }

    /**
     * 调用记录
     *
     * @return
     */
    @ApiOperation("加载调用记录")
    @GetMapping("/history")
    public BaseResp<PageModel<ApiCallHistoryReps>> listApiCallHistory(@RequestParam(value = "startTime", required = false) @ApiParam(value = "开始时间") String startTime,
                                                                      @RequestParam(value = "endTime", required = false) @ApiParam(value = "结束时间") String endTime,
                                                                      @RequestParam(value = "modelCode", required = false) @ApiParam(value = "模型编码") String modelCode,
                                                                      @RequestParam(value = "apiId", required = true) @ApiParam(value = "服务ID") String apiId,
                                                                      @RequestParam(value = "instanceId", required = false) @ApiParam(value = "流程ID") String instanceId,
                                                                      @RequestParam(value = "status", required = false) @ApiParam(value = "状态") Byte status,
                                                                      @RequestParam(value = "pageNum", required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                                                      @RequestParam(value = "pageSize", required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize) {
        ApiCallHistoryQuery apiCallHistoryQuery = ApiCallHistoryQuery.builder()
                .apiId(apiId)
                .startTime(startTime)
                .endTime(endTime)
                .modelCode(modelCode)
                .instanceId(instanceId)
                .status(status)
                .pageNum(pageNum)
                // 固定为10条
                .pageSize(10)
                .build();
        PageModel<ApiCallHistoryReps> apiCallHistory = apiCallService.queryApiCallHistoryListPage(apiCallHistoryQuery);
        return BaseResp.success(apiCallHistory);
    }

    /**
     * 获取表单下拉选择组件数据
     *
     * @return
     */
    @PostMapping("/form/data-source/{apiTemplateId}")
    @ApiOperation("代理表单数据源")
    public BaseResp<Object> getOptionFormData(@PathVariable("apiTemplateId") String apiTemplateCode,
                                              @RequestBody ApiCallVariable callVariable) {
        Object optionFormDataResp = apiCallService.optionFormDataSourceProxyInvoke(apiTemplateCode, callVariable);
        return BaseResp.success(optionFormDataResp);
    }

    /**
     * 获取下拉选择组件数据(代理旧服务)
     *
     * @return
     */
    @PostMapping("/form/data-source/old")
    @ApiOperation("代理表单数据源（代理旧服务）")
    public BaseResp<Map> getOldBpmFormDataSource(@RequestBody X5DataSource x5DataSource) {
        Map result = miBpmRemoteService
                .getBpmFormDataSource(x5DataSource.getAppCode(), x5DataSource.getPath(), x5DataSource.getParams());
        return BaseResp.success(result);
    }

    /**
     * 远程获取表单数据
     *
     * @return
     */
    @GetMapping("/form/remote-data/{apiTemplateId}")
    @ApiOperation("远程获取表单数据")
    public BaseResp<RemoteFormDataVo> getRemoteFormData(@PathVariable("apiTemplateId") String apiTemplateId,
                                                        @RequestParam("processInstanceId") String processInstanceId,
                                                        @RequestParam("businessKey") String businessKey,
                                                        @RequestParam("modelCode") String modelCode,
                                                        @RequestParam("taskDefinitionKey") String taskDefinitionKey,
                                                        @RequestParam("locale") String locale) {

        GetRemoteFormDataReq getRemoteFormDataReq = GetRemoteFormDataReq.builder()
                .apiTemplateId(apiTemplateId)
                .processInstanceId(processInstanceId)
                .businessKey(businessKey)
                .modelCode(modelCode)
                .taskDefinitionKey(taskDefinitionKey)
                .locale(locale)
                .build();

        GetRemoteFormDataReps reps = apiCallService.getRemoteFormDataProxyInvoke(getRemoteFormDataReq);
        return BaseResp.success(apiCallVoConverter.toRemoteFormDataVo(reps));
    }

    /**
     * 远程更新表单数据
     *
     * @return
     */
    @PostMapping("/form/remote-data/{apiTemplateId}")
    @ApiOperation("远程更新表单数据")
    public BaseResp<Void> saveRemoteFormData(@PathVariable("apiTemplateId") String apiTemplateId,
                                             @RequestBody SaveRemoteFormDataReq saveRemoteFormDataReq) {

        apiCallService.saveRemoteFormDataProxyInvoke(apiTemplateId, saveRemoteFormDataReq);
        return BaseResp.success();
    }

    /**
     * 手动触发重试
     *
     * @param apiCallHistoryId
     */
    @ApiOperation("手动触发重试")
    @PostMapping("/retry/{apiCallHistoryId}")
    public BaseResp<Void> retryCall(@PathVariable("apiCallHistoryId") Integer apiCallHistoryId) {
        apiCallService.retryCall(apiCallHistoryId);
        return BaseResp.success();
    }
}
