package com.mi.oa.infra.mibpm.api.web;

import com.mi.flowable.rpc.x5.X5Response;
import com.mi.oa.infra.mibpm.infra.apicall.repository.ApiCallHistoryRepository;
import com.mi.oa.infra.mibpm.infra.mitask.repository.MiTaskRepository;
import com.mi.oa.infra.mibpm.infra.remote.sdk.OrgRemoteService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.flowable.dmn.api.DmnRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Slf4j
@Api(tags = "管理后台工具类")
@RestController
@RequestMapping("/api/v1/tools")
public class ToolsController {
    @Autowired
    private DmnRuleService dmnRuleService;
    @Autowired
    private ApiCallHistoryRepository apiCallHistoryRepository;
    @Autowired
    private OrgRemoteService orgRemoteService;
    @Autowired
    private MiTaskRepository miTaskRepository;

    @ApiOperation("删除服务调用记录")
    @DeleteMapping("/actions/delete-api-histories/{maxId}")
    public BaseResp<Object> deleteApiHistory(@PathVariable("maxId") long maxId) {
        apiCallHistoryRepository.deleteHistoryLessThanId(maxId);
        return BaseResp.success();
    }

    @ApiOperation("决策表执行测试")
    @PostMapping("/dmnTest/{modelCode}")
    public BaseResp<Object> dmn(@RequestBody Map<String, Object> var,
                                @PathVariable("modelCode") String modelCode) {

        List<Map<String, Object>> execute = dmnRuleService
                .createExecuteDecisionBuilder()
                .decisionKey(modelCode)
                .variables(var)
                .execute();

        return BaseResp.success(execute);
    }

    @ApiOperation("获取组织信息")
    @DeleteMapping("/org/{orgCode}")
    public BaseResp<Object> getOrgInfo(@PathVariable("orgCode") String orgCode) {
        return BaseResp.success(orgRemoteService.listOrg(orgCode));
    }

    @PostMapping("/callbackTest")
    public BaseResp<Object> callbackTest(@RequestBody Map<String, Object> var) {
        return BaseResp.success(var);
    }

    @ApiOperation("获取用户待办任务数量")
    @GetMapping("/wait-number/{userId}")
    public BaseResp<Integer> waitNumber(@PathVariable("userId") String userId) {
        return BaseResp.success(miTaskRepository.getPendingTaskCountByUser(userId));
    }

    @PostMapping("/business-form")
    public Object businessForm() {

        Business business = new Business();
        business.setProcessName("BPM2.0流程迁移-动态表单数据测试");
        business.setChangeComment("这是一个变更说明的测试用例");
        business.setDocumentUrl("https://xiaomi.f.mioffice.cn/wiki/wikk4NkSN6lTN0LnQIaGrK7HeRf");
        business.setAttachments("无附件");
        business.setAppName("BPM3.0");
        business.setProcessTypeName("迁移测试流程");
        business.setIsInvolveLeader(false);
        business.setRequester("zou");

        X5Response<Business> response = new X5Response<>();
        X5Response.X5ResponseHeader header = new X5Response.X5ResponseHeader();
        header.setCode(200);
        header.setDesc("Success");
        response.setHeader(header);
        response.setBody(business);

        return response;
    }
}


@Data
class Business {
    private String processName;
    private String changeComment;
    private String documentUrl;
    private String attachments;
    private String appName;
    private String processTypeName;
    private Boolean isInvolveLeader;
    private String requester;
}