package com.mi.oa.infra.mibpm.api.model.vo;

import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/8/28
 * @Description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LeaveDelegationVo {
    /**
     * 请假人
     */
    private String leaver;
    /**
     * 请假类型
     */
    private String leaveType;
    /**
     * 请假天数
     */
    private Float leaveDays;
    /**
     * 请假开始时间
     */
    private String leaveStartTime;
    /**
     * 请假结束时间
     */
    private String leaveEndTime;
    /**
     * 请假交接人
     */
    private String handover;
    /**
     * 请假流程状态
     */
    private ProcessInstanceStatus processInstanceStatus;
}
