package com.mi.oa.infra.mibpm.api.web.openapi;

import com.mi.oa.infra.mibpm.api.converter.ProcInstVoConverter;
import com.mi.oa.infra.mibpm.api.model.vo.OpenCreateProcInstRespVo;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.OpenCreateProcInstResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.OpenQueryProcInstResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.OpenViewAuthResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.OpenCreateProcInstReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.OpenListProcInstReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.OpenQueryViewAuthReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.OpenRecallProcInstReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.OpenSetViewAuthReq;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.OpenTerminateProcInstReq;
import com.mi.oa.infra.mibpm.application.proinst.service.OpenProcessInstanceService;
import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.common.model.ProcessInstanceQueryDto;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.utils.ZoneDateTimeUtil;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.dto.PageModel;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2022/5/5 14:50
 */
@Slf4j
@Api(tags = "Open API 流程实例-V2")
@WebLog
@RestController
@RequestMapping("/openapi/v2/proc-insts")
public class OpenProcInstController {

    private static final int PAGE_SIZE_LIMIT = 50;
    @Autowired
    private OpenProcessInstanceService openProcessInstanceService;
    @Autowired
    private AccountRemoteService accountRemoteService;
    @Autowired
    private ProcInstVoConverter procInstVoConverter;

    @ApiOperation("发起流程实例")
    @PostMapping("/create")
    public BaseResp<OpenCreateProcInstRespVo> create(@Valid @RequestBody OpenCreateProcInstReq createProcInstReq) {
        log.info("发起流程实例, request = {}", GsonUtils.toJsonWtihNullField(createProcInstReq));
        OpenCreateProcInstResp resp = openProcessInstanceService.startProcessInstance(createProcInstReq);
        OpenCreateProcInstRespVo vo = procInstVoConverter.dtoToVo(resp);
        return BaseResp.success(vo);
    }

    @ApiOperation("终止流程实例")
    @PostMapping("/terminate")
    public BaseResp<Void> terminate(@Valid @RequestBody OpenTerminateProcInstReq terminateProcInstReq) {
        log.info("终止流程实例, request = {}", GsonUtils.toJsonWtihNullField(terminateProcInstReq));
        openProcessInstanceService.terminateProcessInstance(terminateProcInstReq);
        return BaseResp.success();
    }

    @ApiOperation("撤回流程")
    @PostMapping("/recall")
    public BaseResp<Void> recall(OpenRecallProcInstReq recallProcInstReq) {
        log.info("撤回流程, request = {}", GsonUtils.toJsonWtihNullField(recallProcInstReq));
        openProcessInstanceService.recallProcessInstance(recallProcInstReq);
        return BaseResp.success();
    }

    @ApiOperation("批量获取流程实例ID")
    @PostMapping("/list")
    public BaseResp<PageModel<String>> list(@Valid @RequestBody OpenListProcInstReq listProcInstReq) {
        log.info("批量获取流程实例ID, request = {}", GsonUtils.toJsonWtihNullField(listProcInstReq));
        return BaseResp.success(openProcessInstanceService.listPageProcessInstanceId(listProcInstReq));
    }

    @ApiOperation("获取单个流程实例详情")
    @GetMapping("/get")
    public BaseResp<OpenQueryProcInstResp> get(@RequestParam(value = "businessKey", required = false) String businessKey,
                                               @RequestParam(value = "processInstanceId", required = false) String processInstanceId) {
        log.info("获取单个流程实例详情, businessKey = {}, processInstanceId = {}", businessKey, processInstanceId);
        if (StringUtils.isNotBlank(processInstanceId)) {
            return BaseResp.success(openProcessInstanceService.queryProcessInstanceById(processInstanceId));
        } else {
            return BaseResp.success(openProcessInstanceService.queryProcessInstanceByBusinessKey(businessKey));
        }
    }

    @ApiOperation("流程列表查询")
    @GetMapping("/history")
    public BaseResp<PageModel<OpenQueryProcInstResp>> queryPage(
            @RequestParam(value = "username", required = false) @ApiParam(value = "流程发起人") String username,
            @RequestParam(value = "modelCode", required = false) @ApiParam(value = "模型编码") String modelCode,
            @RequestParam(value = "businessKey", required = false) @ApiParam(value = "业务key") String businessKey,
            @RequestParam(value = "processInstanceName", required = false) @ApiParam(value = "流程实例名称") String processInstanceName,
            @RequestParam(value = "status", required = false) @ApiParam(value = "流程状态") ProcessInstanceStatus status,
            @RequestParam(value = "createTimeStart", required = false) @ApiParam(value = "流程实例创建时间（unix毫秒时间戳）") String createTimeStart,
            @RequestParam(value = "createTimeEnd", required = false) @ApiParam(value = "流程实例结束时间（unix毫秒时间戳,跨度不得大小7天)") String createTimeEnd,
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") @ApiParam(value = "页码") Long pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") @ApiParam(value = "分页大小（最大50条)") Long pageSize) {

        ProcessInstanceQueryDto processInstanceQueryDto = ProcessInstanceQueryDto.builder()
                .modelCode(modelCode)
                .businessKey(businessKey)
                .processInstanceName(processInstanceName)
                .status(status)
                .byCreateTimeAsc(Boolean.FALSE)
                .build();

        if (StringUtils.isNotBlank(username)) {
            BpmUser user = accountRemoteService.getUser(username);
            if (Objects.nonNull(user)) {
                processInstanceQueryDto.setCurrentUserId(user.getUid());
                processInstanceQueryDto.setCurrentUserName(user.getUserName());
            }
        }

        // 查询时间跨度最多为7天
        if (Objects.isNull(createTimeStart) && Objects.isNull(createTimeEnd)) {
            ZonedDateTime timeEnd = ZonedDateTime.now();
            ZonedDateTime timeStart = timeEnd.minusDays(7L);
            processInstanceQueryDto.setCreateTimeStart(timeStart);
            processInstanceQueryDto.setCreateTimeEnd(timeEnd);
        } else if (Objects.nonNull(createTimeStart) && Objects.isNull(createTimeEnd)) {
            ZonedDateTime timeStart = ZoneDateTimeUtil.getZonedDateTime(createTimeStart);
            ZonedDateTime timeEnd = timeStart.plusDays(7L);
            processInstanceQueryDto.setCreateTimeStart(timeStart);
            processInstanceQueryDto.setCreateTimeEnd(timeEnd);
        } else if (Objects.isNull(createTimeStart) && Objects.nonNull(createTimeEnd)) {
            ZonedDateTime timeEnd = ZoneDateTimeUtil.getZonedDateTime(createTimeEnd);
            ZonedDateTime timeStart = timeEnd.minusDays(7L).truncatedTo(ChronoUnit.DAYS);
            processInstanceQueryDto.setCreateTimeStart(timeStart);
            processInstanceQueryDto.setCreateTimeEnd(timeEnd);
        } else {
            ZonedDateTime timeStart = ZoneDateTimeUtil.getZonedDateTime(createTimeStart);
            ZonedDateTime timeEnd = ZoneDateTimeUtil.getZonedDateTime(createTimeEnd);
            ZonedDateTime maxCreateTimeEnd = timeStart.plusDays(7L);
            processInstanceQueryDto.setCreateTimeStart(timeStart);
            processInstanceQueryDto.setCreateTimeEnd(maxCreateTimeEnd.isBefore(timeEnd) ?
                    maxCreateTimeEnd : timeEnd);
        }

        PageModel<OpenQueryProcInstResp> pageModel = openProcessInstanceService.queryProcessInstanceListPage(
                processInstanceQueryDto,
                pageNum, pageSize > PAGE_SIZE_LIMIT ? PAGE_SIZE_LIMIT : pageSize);
        return BaseResp.success(pageModel);
    }

    @PostMapping("/viewAuth")
    @ApiOperation("对某个用户授予查看权限")
    public BaseResp<String> setViewAuth(@RequestBody OpenSetViewAuthReq req) {
        openProcessInstanceService.setProcInstViewAuth(req);
        return BaseResp.success();
    }

    @GetMapping("/viewAuth")
    @ApiOperation("查询某个用户被授予的查看权限")
    public BaseResp<List<OpenViewAuthResp>> getViewAuth(@ApiParam("用户id") @RequestParam("userId") String userId,
                                                        @ApiParam("业务唯一id") @RequestParam(value = "businessKey", required = false) String businessKey,
                                                        @ApiParam("被授权的任务节点key") @RequestParam(value = "taskKey", required = false) String taskKey) {
        OpenQueryViewAuthReq req = OpenQueryViewAuthReq.builder().userId(userId).businessKey(businessKey)
                .taskDefKey(taskKey).build();
        List<OpenViewAuthResp> openViewAuthResps = openProcessInstanceService.queryProcInstViewAuth(req);
        return BaseResp.success(openViewAuthResps);
    }
}
