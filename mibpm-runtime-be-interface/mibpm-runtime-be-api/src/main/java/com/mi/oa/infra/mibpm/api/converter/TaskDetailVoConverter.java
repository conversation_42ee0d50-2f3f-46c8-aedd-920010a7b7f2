package com.mi.oa.infra.mibpm.api.converter;

import com.mi.oa.infra.mibpm.api.model.vo.TaskDetailBaseVo;
import com.mi.oa.infra.mibpm.api.model.vo.TaskDetailCcHistoryVo;
import com.mi.oa.infra.mibpm.api.model.vo.TaskDetailConfigVo;
import com.mi.oa.infra.mibpm.api.model.vo.TaskDetailFormVo;
import com.mi.oa.infra.mibpm.api.model.vo.TaskDetailHistoryVo;
import com.mi.oa.infra.mibpm.api.model.vo.TaskDetailHtmlFormVo;
import com.mi.oa.infra.mibpm.api.model.vo.TaskDetailOperateHistoryVo;
import com.mi.oa.infra.mibpm.api.model.vo.TaskDetailVo;
import com.mi.oa.infra.mibpm.api.model.vo.TaskInstanceVo;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailCcResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailHistoryResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailOperationHistoryResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskInstanceResp;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @date 2022/3/23 15:54
 */
@Mapper(componentModel = "spring")
public interface TaskDetailVoConverter {

    TaskDetailVo.TaskConfig taskConfigDtoToVo(TaskDetailResp.TaskConfig taskConfig);

    TaskDetailVo.Form formDtoToVo(TaskDetailResp.Form form);

    TaskDetailVo.Task taskDtoToVo(TaskDetailResp.Task task);

    TaskDetailVo dtoToVo(TaskDetailResp taskDetailReps);

    TaskDetailBaseVo dtoToBase(TaskDetailResp taskDetailReps);

    TaskDetailHistoryVo dtoToHistory(TaskDetailHistoryResp taskDetailReps);

    TaskDetailOperateHistoryVo dtoToVo(TaskDetailOperationHistoryResp dto);

    @Mapping(source = "form.schema", target = "schema")
    @Mapping(source = "form.data", target = "data")
    @Mapping(source = "form.link", target = "link")
    @Mapping(source = "form.appContent", target = "appContent")
    @Mapping(source = "form.pcContent", target = "pcContent")
    TaskDetailFormVo dtoToForm(TaskDetailResp taskDetailReps);

    @Mapping(source = "form.appContent", target = "appContent")
    @Mapping(source = "form.pcContent", target = "pcContent")
    TaskDetailHtmlFormVo dtoToHtmlForm(TaskDetailResp taskDetailReps);


    TaskDetailCcHistoryVo dtoToCc(TaskDetailCcResp taskDetailReps);

    @Mapping(source = "taskConfig.operationList", target = "operationList")
    @Mapping(source = "taskConfig.variables", target = "variables")
    @Mapping(source = "taskConfig.strategyList", target = "strategyList")
    @Mapping(source = "taskConfig.requiredComments", target = "requiredComments")
    TaskDetailConfigVo dtoToConfig(TaskDetailResp taskDetailReps);

    @Mapping(source = "isPredict", target = "predict")
    TaskInstanceVo dtoToVo(TaskInstanceResp taskInstanceResp);


    default UserTaskOperation map(EventIdentify eventIdentify) {
        switch (eventIdentify) {
            case OPERATE_APPROVED:
                return UserTaskOperation.AGREE;
            case OPERATE_REJECTED:
                return UserTaskOperation.REJECT;
            case OPERATE_SUBMITTED:
            case OPERATE_START_SUBMIT:
            case SUBMIT_TASK_AUTO_COMPLETE:
                return UserTaskOperation.SUBMIT;
            case OPERATE_DELEGATED:
                return UserTaskOperation.DELEGATE;
            case OPERATE_RETURNED:
                return UserTaskOperation.ROLLBACK;
            case OPERATE_TRANSFERRED:
            case ASSIGNEE_CHANGE:
                return UserTaskOperation.TRANSFER;
            case OPERATE_SIGNED:
                return UserTaskOperation.SIGN;
            case OPERATE_SIGNED_NEW:
                return UserTaskOperation.SIGNATURE;
            case OPERATE_TERMINATED:
                return UserTaskOperation.TERMINATE;
            case OPERATE_CC:
                return UserTaskOperation.CC;
            case OPERATE_RESOLED:
                return UserTaskOperation.RESOLVE;
            case OPERATE_RECALLED:
                return UserTaskOperation.RECALL;
            case OPERATE_CLAIM:
                return UserTaskOperation.CLAIM;
            case VOTE_AGREE:
                return UserTaskOperation.VOTE_AGREE;
            case VOTE_REJECT:
                return UserTaskOperation.VOTE_REJECT;
            case VOTE_ABSTAIN:
                return UserTaskOperation.VOTE_ABSTAIN;
            default:
                return null;
        }
    }
}
