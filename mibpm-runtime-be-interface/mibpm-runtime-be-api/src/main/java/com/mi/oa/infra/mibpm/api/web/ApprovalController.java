package com.mi.oa.infra.mibpm.api.web;

import com.mi.oa.infra.mibpm.api.common.LoginUser;
import com.mi.oa.infra.mibpm.api.converter.TaskDetailVoConverter;
import com.mi.oa.infra.mibpm.api.model.vo.TaskDetailBaseVo;
import com.mi.oa.infra.mibpm.api.model.vo.TaskDetailCcHistoryVo;
import com.mi.oa.infra.mibpm.api.model.vo.TaskDetailConfigVo;
import com.mi.oa.infra.mibpm.api.model.vo.TaskDetailFormVo;
import com.mi.oa.infra.mibpm.api.model.vo.TaskDetailHistoryVo;
import com.mi.oa.infra.mibpm.api.model.vo.TaskDetailOperateHistoryVo;
import com.mi.oa.infra.mibpm.api.model.vo.TaskDetailVo;
import com.mi.oa.infra.mibpm.application.task.dto.req.CcTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.ClaimTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.CompleteTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.DelegateTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.PinTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.RejectTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.ReturnAfterSubmitReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.ReturnTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.ReturnToSubmitReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.SignTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.TaskSignatureReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.TransferTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.req.VoteTaskReq;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailCcResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailHistoryResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailOperationHistoryResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.TaskDetailResp;
import com.mi.oa.infra.mibpm.application.task.dto.resp.VoteConfigResp;
import com.mi.oa.infra.mibpm.application.task.impl.detail.DetailServiceComposite;
import com.mi.oa.infra.mibpm.application.task.service.ApprovalService;
import com.mi.oa.infra.mibpm.application.task.service.HtmlDetailService;
import com.mi.oa.infra.mibpm.common.enums.ClientEnum;
import com.mi.oa.infra.mibpm.common.model.UserTaskActivity;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import com.mi.oa.infra.mibpm.utils.IdentityUtil;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2022/3/11 16:28
 */
@Slf4j
@WebLog
@RestController
@RequestMapping("/api/v1/task")
@Api(tags = "审批任务")
public class ApprovalController {

    @Autowired
    private ApprovalService approvalService;
    @Autowired
    private TaskDetailVoConverter taskDetailVoConverter;
    @Autowired
    private DetailServiceComposite detailService;
    @Autowired
    private HtmlDetailService htmlDetailService;

    @LoginUser
    @PostMapping("/approve")
    @ApiOperation("审批任务同意")
    public BaseResp<Void> approve(@Valid @RequestBody CompleteTaskReq completeTaskReq) {
        log.info("审批任务同意, request = {}", completeTaskReq);
        if (Integer.valueOf(3).equals(completeTaskReq.getOldType())) {
            approvalService.extApprove(completeTaskReq, UserTaskOperation.AGREE);
        } else {
            approvalService.approve(completeTaskReq);
        }
        return BaseResp.success();
    }

    @LoginUser
    @PostMapping("/pin")
    @ApiOperation("审批任务收藏")
    public BaseResp<Void> pin(@Valid @RequestBody PinTaskReq completeTaskReq) {
        log.info("审批任务收藏, request = {}", completeTaskReq);
        approvalService.pin(completeTaskReq);
        return BaseResp.success();
    }

    @LoginUser
    @PostMapping("/reject")
    @ApiOperation("审批任务拒绝")
    public BaseResp<Void> reject(@Valid @RequestBody RejectTaskReq rejectTaskReq) {
        log.info("审批任务拒绝, request = {}", rejectTaskReq);
        if (Integer.valueOf(3).equals(rejectTaskReq.getOldType())) {
            approvalService.extReject(rejectTaskReq, UserTaskOperation.REJECT);
        } else {
            approvalService.reject(rejectTaskReq);
        }
        return BaseResp.success();
    }

    @LoginUser
    @PostMapping("/transfer")
    @ApiOperation("审批任务转交")
    public BaseResp<Void> transfer(@Valid @RequestBody TransferTaskReq transferTaskReq) {
        log.info("审批任务转交, request = {}", transferTaskReq);
        approvalService.transfer(transferTaskReq);
        return BaseResp.success();
    }

    @LoginUser
    @PostMapping("/sign")
    @ApiOperation("审批任务加签")
    public BaseResp<Void> sign(@Valid @RequestBody SignTaskReq signTaskReq) {
        log.info("审批任务加签, request = {}", signTaskReq);
        approvalService.sign(signTaskReq);
        return BaseResp.success();
    }

    @LoginUser
    @PostMapping("/cc")
    @ApiOperation("审批任务抄送")
    public BaseResp<Void> carbonCopy(@Valid @RequestBody CcTaskReq ccTaskReq) {
        log.info("审批任务抄送, request = {}", ccTaskReq);
        approvalService.carbonCopy(ccTaskReq);
        return BaseResp.success();
    }

    @LoginUser
    @PostMapping("/returns")
    @ApiOperation("审批任务退回")
    public BaseResp<Void> returns(@Valid @RequestBody ReturnTaskReq returnTaskReq) {
        log.info("审批任务退回, request = {}", returnTaskReq);
        approvalService.returns(returnTaskReq);
        return BaseResp.success();
    }

    @LoginUser
    @PostMapping("/returnToSubmit")
    @ApiOperation("审批任务加签到提交节点")
    public BaseResp<Void> returnToSubmit(@Valid @RequestBody ReturnToSubmitReq returnToSubmitReq) {
        log.info("审批任务加签到提交节点, request = {}", returnToSubmitReq);
        approvalService.returnToSubmit(returnToSubmitReq);
        return BaseResp.success();
    }

    @LoginUser
    @PostMapping("/submit")
    @ApiOperation("审批任务重新提交请求")
    public BaseResp<Void> submit(@Valid @RequestBody ReturnAfterSubmitReq returnAfterSubmitReq) {
        log.info("审批任务重新提交请求, request = {}", returnAfterSubmitReq);
        approvalService.submit(returnAfterSubmitReq);
        return BaseResp.success();
    }

    @LoginUser
    @PostMapping("/delegate")
    @ApiOperation("审批任务委派")
    public BaseResp<Void> delegate(@Valid @RequestBody DelegateTaskReq delegateTaskReq) {
        log.info("审批任务委派, request = {}", delegateTaskReq);
        approvalService.delegate(delegateTaskReq);
        return BaseResp.success();
    }

    @LoginUser
    @PostMapping("/claim")
    @ApiOperation("审批任务认领")
    public BaseResp<Void> claim(@Valid @RequestBody ClaimTaskReq claimTaskReq) {
        log.info("审批任务认领, request = {}", claimTaskReq);
        approvalService.claim(claimTaskReq);
        return BaseResp.success();
    }

    @ApiOperation("审批任务详情")
    @Deprecated
    @GetMapping("/{taskId}/detail")
    public BaseResp<TaskDetailVo> detail(@PathVariable String taskId, @RequestParam("client") String client) {
        log.info("审批任务详情, taskId = {}, client = {}", taskId, client);
        TaskDetailResp taskDetailReps = approvalService.detail(taskId, ClientEnum.valueOf(client));
        return BaseResp.success(taskDetailVoConverter.dtoToVo(taskDetailReps));
    }

    @ApiOperation("审批任务详情-基本信息")
    @GetMapping("/{taskId}/detail/base")
    public BaseResp<TaskDetailBaseVo> detailBase(@PathVariable String taskId, @RequestParam("client") String client) {
        log.info("审批任务详情基本信息, taskId = {}, client = {}", taskId, client);
        TaskDetailResp taskDetailReps = detailService.detailBase(taskId, ClientEnum.valueOf(client));
        return BaseResp.success(taskDetailVoConverter.dtoToBase(taskDetailReps));
    }

    @ApiOperation("审批任务详情-审批进度")
    @GetMapping("/{taskId}/detail/history")
    public BaseResp<TaskDetailHistoryVo> detailHistory(@PathVariable String taskId, @RequestParam("client") String client) {
        log.info("审批任务详情审批进度, taskId = {}, client = {}", taskId, client);
        TaskDetailHistoryResp taskDetailHistoryResp = detailService.detailHistory(taskId, ClientEnum.valueOf(client));
        return BaseResp.success(taskDetailVoConverter.dtoToHistory(taskDetailHistoryResp));
    }

    @ApiOperation("审批任务详情-审批预测")
    @GetMapping("/{taskId}/detail/predict")
    public BaseResp<TaskDetailHistoryVo> detailPredict(@PathVariable String taskId, @RequestParam("client") String client) {
        log.info("审批任务详情预测, taskId = {}, client = {}", taskId, client);
        TaskDetailHistoryResp taskDetailHistoryResp = detailService.detailPredict(taskId, ClientEnum.valueOf(client));
        return BaseResp.success(taskDetailVoConverter.dtoToHistory(taskDetailHistoryResp));
    }

    @ApiOperation("审批任务详情-操作记录")
    @GetMapping("/{taskId}/detail/operation")
    public BaseResp<TaskDetailOperateHistoryVo> detailOperation(@PathVariable String taskId, @RequestParam("client") String client) {
        log.info("审批任务详情, taskId = {}, client = {}", taskId, client);
        TaskDetailOperationHistoryResp resp = detailService.detailOperationHistory(taskId,
                ClientEnum.valueOf(client));
        return BaseResp.success(taskDetailVoConverter.dtoToVo(resp));
    }

    @ApiOperation("审批任务详情-抄送记录")
    @GetMapping("/{taskId}/detail/cc")
    public BaseResp<TaskDetailCcHistoryVo> detailCc(@PathVariable String taskId, @RequestParam("client") String client) {
        log.info("审批任务详情抄送记录, taskId = {}, client = {}", taskId, client);
        TaskDetailCcResp taskDetailResp = detailService.detailCc(taskId, ClientEnum.valueOf(client));
        return BaseResp.success(taskDetailVoConverter.dtoToCc(taskDetailResp));
    }

    @ApiOperation("审批任务详情-审批配置")
    @GetMapping("/{taskId}/detail/config")
    public BaseResp<TaskDetailConfigVo> detailConfig(@PathVariable String taskId, @RequestParam("client") String client) {
        log.info("审批任务详情审批配置, taskId = {}, client = {}", taskId, client);
        TaskDetailResp taskDetailResp = detailService.detailConfig(taskId, ClientEnum.valueOf(client));
        return BaseResp.success(taskDetailVoConverter.dtoToConfig(taskDetailResp));
    }

    @ApiOperation("审批任务详情-表单数据")
    @GetMapping("/{taskId}/detail/form")
    public BaseResp<TaskDetailFormVo> detailForm(@PathVariable String taskId, @RequestParam("client") String client) {
        log.info("审批任务详情, taskId = {}, client = {}", taskId, client);
        TaskDetailResp taskDetailResp = detailService.detailForm(taskId, ClientEnum.valueOf(client));
        TaskDetailFormVo taskDetailFormVo = taskDetailVoConverter.dtoToForm(taskDetailResp);
        return BaseResp.success(taskDetailFormVo);
    }

    @ApiOperation("审批任务可以退回到的节点")
    @GetMapping("/{taskId}/returns/activities")
    public BaseResp<List<UserTaskActivity>> queryReturnActivities(@PathVariable String taskId) {
        log.info("审批任务可以退回到的节点, taskId = {}", taskId);
        List<UserTaskActivity> userTaskActivities = approvalService.queryReturnActivities(taskId);
        return BaseResp.success(userTaskActivities);
    }

    @ApiOperation("审批任务跳签")
    @GetMapping("/{taskId}/skip/activities/{targetActivityId}")
    public BaseResp<Void> skip(@PathVariable("taskId") String taskId,
                               @PathVariable("targetActivityId") String targetActivityId) {
        log.info("审批任务跳签, taskId = {}, targetActivityId = {}", taskId, targetActivityId);
        approvalService.skip(taskId, targetActivityId);
        return BaseResp.success();
    }

    @ApiOperation("流程实例跳签")
    @GetMapping("/{procInstId}/skip/executions/{targetActivityId}")
    public BaseResp<Void> skipExecution(@PathVariable("procInstId") String procInstId,
                                        @PathVariable("targetActivityId") String targetActivityId) {
        log.info("流程实例跳签, procInstId = {}, targetActivityId = {}", procInstId, targetActivityId);
        approvalService.skipExecution(procInstId, targetActivityId);
        return BaseResp.success();
    }

    @LoginUser
    @PostMapping("/addSignature")
    @ApiOperation("审批任务签名")
    public BaseResp<Void> addSignature(@Valid @RequestBody TaskSignatureReq taskSignatureReq) {
        log.info("审批任务签名, request = {}", taskSignatureReq);
        approvalService.saveTaskSignature(taskSignatureReq.getTaskId(),
                taskSignatureReq.getOperator().getUserName(), taskSignatureReq.getSignature(), taskSignatureReq.isSaveAsDefault());
        return BaseResp.success();
    }

    @LoginUser
    @GetMapping("/userSignature")
    @ApiOperation("获取审批任务签名")
    public BaseResp<String> getSignature(@RequestParam("taskId") String taskId) {
        log.info("审批任务签名, request = {}", taskId);
        String taskSignature = approvalService.getTaskSignature(taskId, IdentityUtil.currentUserName());
        return BaseResp.success(taskSignature);
    }

    @LoginUser
    @PostMapping("/voteAgree")
    @ApiOperation("投票同意")
    @WebLog
    public BaseResp<Void> voteAgree(@RequestBody VoteTaskReq req) {
        approvalService.voteAgree(req);
        return BaseResp.success();
    }

    @LoginUser
    @PostMapping("/voteReject")
    @ApiOperation("投票拒绝")
    @WebLog
    public BaseResp<String> voteReject(@RequestBody VoteTaskReq req) {
        approvalService.voteReject(req);
        return BaseResp.success();
    }

    @LoginUser
    @PostMapping("/voteAbstain")
    @ApiOperation("投票弃权")
    @WebLog
    public BaseResp<String> voteAbstain(@RequestBody VoteTaskReq req) {
        approvalService.voteAbstain(req);
        return BaseResp.success();
    }

    @LoginUser
    @GetMapping("/voteConfig")
    @ApiOperation("获取投票配置")
    @WebLog
    public BaseResp<VoteConfigResp> getVoteConfig(@RequestParam("procInstId") String procInstId,
                                                  @RequestParam("taskDefKey") String taskDefKey) {
        VoteConfigResp voteConfigResp = detailService.voteConfig(procInstId, taskDefKey);
        return BaseResp.success(voteConfigResp);
    }
}
