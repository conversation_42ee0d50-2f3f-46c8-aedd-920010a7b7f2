package com.mi.oa.infra.mibpm.api.web;

import com.mi.oa.infra.mibpm.application.mitask.dto.req.MiTaskMigrateReq;
import com.mi.oa.infra.mibpm.application.migrate.Constants;
import com.mi.oa.infra.mibpm.application.migrate.MiTaskBpmMigrateService;
import com.mi.oa.infra.mibpm.application.migrate.MiTaskExtMigrateService;
import com.mi.oa.infra.mibpm.application.migrate.MigrateLog;
import com.mi.oa.infra.mibpm.utils.RedisUtil;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "MiTask 工具类")
@RestController
@RequestMapping("/api/v1/mi-task/tools")
public class MiTaskToolsController {
    @Autowired
    private MiTaskBpmMigrateService miTaskCompletedMigrateService;
    @Autowired
    private MiTaskExtMigrateService miTaskExtMigrateService;
    @Autowired
    private RedisUtil redisUtil;

    @ApiOperation("迁移BPM任务")
    @PostMapping("/actions/migrate_bpm")
    public BaseResp<Object> migrateBpm(@RequestBody MiTaskMigrateReq miTaskMigrateReq) {
        return BaseResp.success(miTaskCompletedMigrateService.dispatch(miTaskMigrateReq));
    }

    @ApiOperation("迁移统一待办任务")
    @PostMapping("/actions/migrate_ext")
    public BaseResp<Object> migrateExt(@RequestBody MiTaskMigrateReq miTaskMigrateReq) {
        return BaseResp.success(miTaskExtMigrateService.dispatch(miTaskMigrateReq));
    }

    @ApiOperation("获取redis key")
    @GetMapping("/redis")
    public BaseResp<Object> get(@RequestParam("key") String key) {
        return BaseResp.success(redisUtil.get(key));
    }

    @ApiOperation("删除redis key")
    @DeleteMapping("/redis")
    public BaseResp<Object> delete(@RequestParam("key") String key) {
        redisUtil.delete(key);
        return BaseResp.success();
    }

    @ApiOperation("获取BPM迁移进度")
    @GetMapping("/redis/getMigrateLog")
    public BaseResp<Object> getMigrateLog() {
        return BaseResp.success(MigrateLog.get(Constants.START_MIGRATE, Constants.OFFSET));
    }

    @ApiOperation("获取统一待办迁移进度")
    @GetMapping("/redis/getExtMigrateLog")
    public BaseResp<Object> getExtMigrateLog() {
        return BaseResp.success(MigrateLog.get(Constants.START_MIGRATE_EXT, Constants.OFFSET_EXT));
    }

    @ApiOperation("获取迁移失败列表")
    @GetMapping("/redis/getFail")
    public BaseResp<Object> getFail() {
        return BaseResp.success(redisUtil.lRange(Constants.FAIL, 0, 10000));
    }

    @ApiOperation("迁移分类数据")
    @GetMapping("/actions/migrate_category")
    public BaseResp<Object> migrateCategory() {
        miTaskCompletedMigrateService.migrateCategory();
        return BaseResp.success();
    }
}