package com.mi.oa.infra.mibpm.api.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/18 15:38
 */
@Data
@ApiModel(value = "审批任务-表单详情")
public class TaskDetailFormVo {

    private String taskId;
    private String processInstanceId;
    @ApiModelProperty("表单模型")
    private Map<String, Object> schema;
    @ApiModelProperty("表单数据")
    private Map<String, Object> data;
    @ApiModelProperty("跳转链接")
    private Link link;
    @ApiModelProperty("移动端xml模板")
    private String appContent;
    @ApiModelProperty("pc端xml")
    private String pcContent;

    @Data
    public static class Link {
        private String pcLink;
        private String appLink;
    }

}
