package com.mi.oa.infra.mibpm.api.web;

import com.mi.oa.infra.mibpm.api.converter.WpsCallBackConverter;
import com.mi.oa.infra.mibpm.api.model.dto.FilePreviewReq;
import com.mi.oa.infra.mibpm.api.model.vo.FilePreviewReps;
import com.mi.oa.infra.mibpm.infra.remote.sdk.WpsPreviewRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.impl.WpsPreviewRemoteServiceImpl;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.net.URL;
import java.net.URLConnection;
import java.net.URLDecoder;

/**
 * <AUTHOR>
 * @date 2024/3/07 8:34
 *
 * 文件预览
 **/
@Api(tags = "文件预览")
@RestController
@RequestMapping("/api/v1/file/preview")
@Slf4j
public class FilePreviewController {

    @Autowired
    private WpsPreviewRemoteService wpsPreviewRemoteService;
    @Autowired
    private WpsCallBackConverter wpsCallBackConverter;

    // 获取文件元数据信息（wps回调接口）
    @GetMapping("/meta-info")
    public FilePreviewReps getFileMetaInfo(@RequestParam("_w_third_file_url") String fileUrl,
                                           @RequestParam("_w_third_file_id") String fileId,
                                           @RequestParam("_w_third_file_type") String fileType) {
        log.info("获取文件元数据，fileUrl={}", fileUrl);
        try {
            fileUrl = new String(WpsPreviewRemoteServiceImpl.customBase64Decode(fileUrl));
            fileUrl = URLDecoder.decode(fileUrl, "utf-8");
            URL url = new URL(fileUrl);
            URLConnection connection = url.openConnection();

            int documentSize = connection.getContentLength();
            FilePreviewReps previewReps = wpsCallBackConverter.toPreviewReps(documentSize,
                    fileUrl, fileId, fileType);
            log.info("获取文件元数据响应: {}", JacksonUtils.bean2Json(previewReps));
            return previewReps;
        } catch (Exception e) {
            log.error("获取文件元数据异常", e);
            return null;
        }
    }

    @PostMapping("/link")
    public BaseResp<String> getPreviewLink(@RequestBody FilePreviewReq req) {
        return BaseResp.success(wpsPreviewRemoteService.getFilePreviewLink(req.getUri(), req.getOriginFilename()));
    }
}
