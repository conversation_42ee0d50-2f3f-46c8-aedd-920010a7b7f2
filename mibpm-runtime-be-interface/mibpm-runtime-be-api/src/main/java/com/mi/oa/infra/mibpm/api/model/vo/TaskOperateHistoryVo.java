package com.mi.oa.infra.mibpm.api.model.vo;

import com.mi.oa.infra.mibpm.common.enums.ActivityTypeEnum;
import com.mi.oa.infra.mibpm.common.enums.AutoOperationTypeEnum;
import com.mi.oa.infra.mibpm.common.enums.ClientEnum;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskSignType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/18 15:38
 */
@Data
@ApiModel(value = "操作记录对象")
public class TaskOperateHistoryVo {

    @ApiModelProperty("任务ID")
    protected String taskId;
    protected String processInstanceId;
    @ApiModelProperty("任务名称")
    protected String taskName;
    @ApiModelProperty("任务定义ID")
    protected String taskDefinitionId;
    @ApiModelProperty("任务定义KEY")
    protected String taskDefinitionKey;
    @ApiModelProperty("任务处理人")
    protected BpmUser assignee;
    @ApiModelProperty("操作人")
    protected BpmUser operator;
    @ApiModelProperty("任务创建时间")
    protected ZonedDateTime createTime;
    @ApiModelProperty("任务结束时间")
    protected ZonedDateTime endTime;
    @ApiModelProperty("任务操作")
    private UserTaskOperation operation;
    @ApiModelProperty("评论")
    private String comment;
    @ApiModelProperty("处理任务的客户端")
    private ClientEnum client;
    @ApiModelProperty("审批方式")
    private UserTaskSignType signType;
    @ApiModelProperty("活动节点类型")
    private ActivityTypeEnum activityType;
    @ApiModelProperty("操作附加属性 部分操作才有")
    private OperateAttribute operateAttribute;
    private String signature;
    private AutoOperationTypeEnum autoOperationType;
    private String commentEn;

    @Data
    public static class OperateAttribute {
        @ApiModelProperty("操作目标，用于委托、加签、委派、转审")
        private List<BpmUser> targetUser;
        @ApiModelProperty("操作目标节点id，用于退回")
        private String targetActivityId;
        @ApiModelProperty("操作目标节点名称，用于退回")
        private String targetActivityName;
    }

}
