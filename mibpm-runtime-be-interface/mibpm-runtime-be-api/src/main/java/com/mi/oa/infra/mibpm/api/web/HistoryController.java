package com.mi.oa.infra.mibpm.api.web;

import com.mi.oa.infra.mibpm.application.common.NewQuery;
import com.mi.oa.infra.mibpm.api.converter.ProcInstExportVoConverter;
import com.mi.oa.infra.mibpm.api.converter.ProcInstVoConverter;
import com.mi.oa.infra.mibpm.api.model.vo.HistoricProcInstVo;
import com.mi.oa.infra.mibpm.api.model.vo.ProcInstExportHistoryVo;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.HistoricProcInstResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.ProcessInstanceExportHistoryDto;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.RecentUseModelResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.ProcessInstanceQueryReqDto;
import com.mi.oa.infra.mibpm.application.task.dto.resp.HistoricTaskResp;
import com.mi.oa.infra.mibpm.application.task.service.HistoryProcessInstanceService;
import com.mi.oa.infra.mibpm.application.task.service.TaskService;
import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import com.mi.oa.infra.mibpm.common.model.ProcessInstanceExportReq;
import com.mi.oa.infra.mibpm.utils.IdentityUtil;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/18 11:05
 */
@Api(tags = "历史查询")
@WebLog
@RestController
@RequestMapping("/api/v1/history")
public class HistoryController {

    @Autowired
    private HistoryProcessInstanceService historyProcessInstanceService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private ProcInstVoConverter procInstVoConverter;
    @Autowired
    private ProcInstExportVoConverter procInstExportVoConverter;
    @Autowired
    private NewQuery newQuery;

    @ApiOperation("查询我发起的流程列表")
    @GetMapping("/proc-insts/mine")
    public BaseResp<PageVO<HistoricProcInstVo>> listStartProcInstByMinePage(@RequestParam(value = "categoryCode", required = false) @ApiParam(value = "分类编码") String categoryCode,
                                                                            @RequestParam(value = "modelCode", required = false) @ApiParam(value = "模型编码") String modelCode,
                                                                            @RequestParam(value = "processInstanceName", required = false) @ApiParam(value = "流程实例名称") String processInstanceName,
                                                                            @RequestParam(value = "processInstanceStatus", required = false) @ApiParam(value = "流程状态") ProcessInstanceStatus processInstanceStatus,
                                                                            @RequestParam(value = "statusList", required = false) @ApiParam(value = "流程状态") List<ProcessInstanceStatus> statusList,
                                                                            @RequestParam(value = "startTimeBegin", required = false) @ApiParam(value = "流程发起时间-起止点（unix毫秒时间戳）时间间隔不可超过一周，若超过默认只查开始时间后一周") String startTimeBegin,
                                                                            @RequestParam(value = "startTimeEnd", required = false) @ApiParam(value = "流程发起时间-截止点（unix毫秒时间戳）") String startTimeEnd,
                                                                            @RequestParam(value = "byStartTimeAsc", required = false) @ApiParam(value = "按流程发起时间升序排列") boolean byStartTimeAsc,
                                                                            @RequestParam(value = "pageNum", required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                                                            @RequestParam(value = "pageSize", required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize) {
        if (StringUtils.isNotBlank(modelCode) && modelCode.startsWith("ext_")) {
            categoryCode = modelCode;
            modelCode = null;
        }
        ProcessInstanceQueryReqDto req = ProcessInstanceQueryReqDto.builder().currentUserId(IdentityUtil.currentUid())
                .currentUserName(IdentityUtil.currentUserName())
                .createTimeStart(startTimeBegin)
                .createTimeEnd(startTimeEnd)
                .categoryCode(categoryCode)
                .modelCode(modelCode)
                .status(processInstanceStatus)
                .statusList(statusList)
                .processInstanceName(processInstanceName)
                .byCreateTimeAsc(byStartTimeAsc)
                .newQueryFlag(newQuery.get())
                .build();
        PageVO<HistoricProcInstResp> resp = historyProcessInstanceService.queryProcessInstanceListPage(req,
                pageNum, pageSize);
        List<HistoricProcInstVo> procInstListVo = procInstVoConverter.dtoToList(resp.getList());
        return BaseResp.success(PageVO.build(procInstListVo, resp.getPageSize(), resp.getPageNum(), resp.getTotal()));

    }

    @ApiOperation("导出审批记录")
    @PostMapping("/proc-insts/export")
    public BaseResp<Void> exportProcessInstances(@RequestBody ProcessInstanceExportReq req) {
        historyProcessInstanceService.exportProcessInstance(req);
        return BaseResp.success();
    }

    @ApiOperation("导出流程表单数据")
    @PostMapping("/proc-insts/form/export")
    public BaseResp<Void> exportProcessInstancesAndForm(@RequestBody ProcessInstanceExportReq req) {
        historyProcessInstanceService.exportProcessInstanceAndForm(req);
        return BaseResp.success();
    }

    @ApiOperation("查询导出记录")
    @GetMapping("/proc-insts/export-hisotry")
    public BaseResp<PageVO<ProcInstExportHistoryVo>> getExportHistory(@RequestParam(value = "pageNum", required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                                                      @RequestParam(value = "pageSize", required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize) {
        PageVO<ProcessInstanceExportHistoryDto> page = historyProcessInstanceService.queryExportHistory(
                pageNum, pageSize);
        List<ProcInstExportHistoryVo> pageVo = procInstExportVoConverter.dtoToVo(page.getList());
        return BaseResp.success(PageVO.build(pageVo, page.getPageSize(), page.getPageNum(), page.getTotal()));
    }

    @ApiOperation("查询最近发起的流程")
    @GetMapping("/proc-insts/recent-model")
    public BaseResp<List<RecentUseModelResp>> queryRecentUseModelList(@RequestParam(value = "maxNumber", required = false, defaultValue = "10") @ApiParam(value = "最大条数") Integer maxNumber) {
        return BaseResp.success(historyProcessInstanceService.queryRecentUseModelList(maxNumber));
    }

    @ApiOperation("查询指定历史任务")
    @GetMapping("/tasks/{taskId}")
    public BaseResp<HistoricTaskResp> queryHistoricTask(@PathVariable("taskId") String taskId,
                                                        @RequestParam("operator") String operator) {
        return BaseResp.success(taskService.queryHistoricTask(taskId, operator));
    }

}
