package com.mi.oa.infra.mibpm.api.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/2/25 2:47 PM
 **/
@Data
@ApiModel
public class ProcessCategoryListVo {

    @ApiModelProperty("分类名称")
    private String categoryName;
    @ApiModelProperty("分类编码")
    private String categoryCode;
    @ApiModelProperty("数量")
    private Long count;
    @ApiModelProperty("分类下流程信息")
    List<ProcessCountVo> processCountList;
}
