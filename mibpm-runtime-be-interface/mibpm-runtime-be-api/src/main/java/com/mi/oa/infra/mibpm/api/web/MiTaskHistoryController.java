package com.mi.oa.infra.mibpm.api.web;

import com.mi.oa.infra.mibpm.api.converter.ProcInstVoConverter;
import com.mi.oa.infra.mibpm.api.model.vo.HistoricProcInstVo;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.HistoricProcInstResp;
import com.mi.oa.infra.mibpm.application.proinst.dto.req.ProcessInstanceQueryReqDto;
import com.mi.oa.infra.mibpm.application.task.service.HistoryProcessInstanceService;
import com.mi.oa.infra.mibpm.common.enums.ProcessInstanceStatus;
import com.mi.oa.infra.mibpm.utils.IdentityUtil;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.dto.PageVO;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/18 11:05
 */
@Api(tags = "历史查询(MiTask)")
@WebLog
@RestController
@RequestMapping("/api/v2/history")
public class MiTaskHistoryController {

    @Autowired
    private HistoryProcessInstanceService historyProcessInstanceService;
    @Autowired
    private ProcInstVoConverter procInstVoConverter;

    @ApiOperation("查询我发起的流程列表")
    @GetMapping("/proc-insts/mine")
    public BaseResp<PageVO<HistoricProcInstVo>> listStartProcInstByMinePage(@RequestParam(value = "categoryCode", required = false) @ApiParam(value = "分类编码") String categoryCode,
                                                                            @RequestParam(value = "modelCode", required = false) @ApiParam(value = "模型编码") String modelCode,
                                                                            @RequestParam(value = "processInstanceName", required = false) @ApiParam(value = "流程实例名称") String processInstanceName,
                                                                            @RequestParam(value = "processInstanceStatus", required = false) @ApiParam(value = "流程状态") ProcessInstanceStatus processInstanceStatus,
                                                                            @RequestParam(value = "statusList", required = false) @ApiParam(value = "流程状态") List<ProcessInstanceStatus> statusList,
                                                                            @RequestParam(value = "startTimeBegin", required = false) @ApiParam(value = "流程发起时间-起止点（unix毫秒时间戳）时间间隔不可超过一周，若超过默认只查开始时间后一周") String startTimeBegin,
                                                                            @RequestParam(value = "startTimeEnd", required = false) @ApiParam(value = "流程发起时间-截止点（unix毫秒时间戳）") String startTimeEnd,
                                                                            @RequestParam(value = "byStartTimeAsc", required = false) @ApiParam(value = "按流程发起时间升序排列") boolean byStartTimeAsc,
                                                                            @RequestParam(value = "pageNum", required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                                                            @RequestParam(value = "pageSize", required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize) {
        if (StringUtils.isNotBlank(modelCode) && modelCode.startsWith("ext_")) {
            categoryCode = modelCode;
            modelCode = null;
        }
        ProcessInstanceQueryReqDto req = ProcessInstanceQueryReqDto.builder().currentUserId(IdentityUtil.currentUid())
                .currentUserName(IdentityUtil.currentUserName())
                .createTimeStart(startTimeBegin)
                .createTimeEnd(startTimeEnd)
                .categoryCode(categoryCode)
                .modelCode(modelCode)
                .status(processInstanceStatus)
                .statusList(statusList)
                .processInstanceName(processInstanceName)
                .byCreateTimeAsc(byStartTimeAsc)
                .newQueryFlag(true)
                .build();
        PageVO<HistoricProcInstResp> resp = historyProcessInstanceService.queryProcessInstanceListPage(req,
                pageNum, pageSize);
        List<HistoricProcInstVo> procInstListVo = procInstVoConverter.dtoToList(resp.getList());
        return BaseResp.success(PageVO.build(procInstListVo, resp.getPageSize(), resp.getPageNum(), resp.getTotal()));

    }
}
