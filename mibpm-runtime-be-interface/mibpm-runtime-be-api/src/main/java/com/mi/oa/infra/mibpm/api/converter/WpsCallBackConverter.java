package com.mi.oa.infra.mibpm.api.converter;

import com.mi.oa.infra.mibpm.api.model.vo.FilePreviewReps;
import org.mapstruct.Mapper;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2024/3/8 15:04
 */
@Mapper(componentModel = "spring")
public interface WpsCallBackConverter {

    default FilePreviewReps toPreviewReps(int byteSize, String downloadUrl, String fileId, String fileType) {
        FilePreviewReps.File file = new FilePreviewReps.File();
        String fileName = downloadUrl.length() <= 10 ? downloadUrl : downloadUrl.substring(downloadUrl.length() - 10);
        file.setId(fileId);
        if (!fileName.endsWith("." + fileType)) {
            fileName = fileName.concat("." + fileType);
        }
        file.setName(fileName);
        file.setVersion(1);
        file.setSize(byteSize);
        file.setCreator("userid");
        file.setCreateTime(System.currentTimeMillis() / 1000);
        file.setModifier("userid");
        file.setModifyTime(System.currentTimeMillis() / 1000);
        if (isUrlEncoded(downloadUrl)) {
            file.setDownloadUrl(downloadUrl);
        } else {
            file.setDownloadUrl(UriComponentsBuilder.fromHttpUrl(downloadUrl)
                    .encode().build().toString());
        }
        file.setPreviewPages(0);

        FilePreviewReps.User user = new FilePreviewReps.User();
        user.setId("userid");
        user.setName("user");
        user.setPermission("read");

        FilePreviewReps reps = new FilePreviewReps();
        reps.setFile(file);
        reps.setUser(user);

        return reps;
    }

    /**
     * 判断 URL 是否已经被编码
     *
     * @param url 需要检查的 URL
     * @return 如果 URL 已经被编码，返回 true；否则返回 false
     */
    default boolean isUrlEncoded(String url) {
        try {
            String decodedUrl = URLDecoder.decode(url, StandardCharsets.UTF_8.name());
            return !decodedUrl.equals(url);
        } catch (Exception e) {
            return false;
        }
    }
}
