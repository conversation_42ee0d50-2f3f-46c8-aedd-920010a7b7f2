package com.mi.oa.infra.mibpm.api.web;

import com.mi.oa.infra.mibpm.infra.remote.sdk.DataReportRemoteService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 数据报表控制器
 *
 * @author: qiuzhipeng
 * @Date: 2022/3/29 19:33
 */
@Api(tags = "数据报表")
@WebLog
@RestController
@RequestMapping("/api/v1/data-report")
public class DataReportController {

    @Autowired
    private DataReportRemoteService dataReportRemoteService;

    @PostMapping("")
    public BaseResp<Object> queryDataReport(@RequestBody Map<String, Object> param){
        return BaseResp.success(dataReportRemoteService.dataReport(param));
    }

}
