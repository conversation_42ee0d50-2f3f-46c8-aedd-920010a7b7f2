package com.mi.oa.infra.mibpm.api.web;

import com.larksuite.appframework.sdk.core.protocol.common.I18nText;
import com.mi.id.oaucf.feign.BaseResp;
import com.mi.oa.infra.mibpm.api.model.dto.NotifyReciveReq;
import com.mi.oa.infra.mibpm.api.model.dto.NotifyReciveResponse;
import com.mi.oa.infra.mibpm.api.model.dto.PersonResignReq;
import com.mi.oa.infra.mibpm.api.model.dto.PersonTransferReq;
import com.mi.oa.infra.mibpm.common.enums.EventIdentify;
import com.mi.oa.infra.mibpm.common.event.PersonResignEvent;
import com.mi.oa.infra.mibpm.common.event.PersonTransferEvent;
import com.mi.oa.infra.mibpm.common.model.BpmUser;
import com.mi.oa.infra.mibpm.eventbus.EventPublisher;
import com.mi.oa.infra.mibpm.infra.remote.entity.UserReportLineEntity;
import com.mi.oa.infra.mibpm.infra.remote.sdk.AccountRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.LarkAppRemoteService;
import com.mi.oa.infra.mibpm.infra.remote.sdk.ModelsAuthorityRemote;
import com.mi.oa.infra.mibpm.utils.IdentityUtil;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Base64;
import java.util.List;
import java.util.UUID;

/**
 * @author: qiuzhipeng
 * @Date: 2022/10/8 16:51
 */
@Slf4j
@Api(tags = "Notify 接口")
@WebLog
@RestController
@RequestMapping("/api/v1/notify")
public class NotifyController {

    @Autowired
    private EventPublisher eventPublisher;
    @Autowired
    private AccountRemoteService accountRemoteService;
    @Autowired
    private LarkAppRemoteService larkAppRemoteService;
    @Autowired
    private ModelsAuthorityRemote authorityRemote;

    @ApiOperation("人员离职")
    @PostMapping("/person-resign")
    public NotifyReciveResponse personResign(@RequestBody String param) {

        log.info("接收到notify人员离职通知，msg={}", param);

        NotifyReciveResponse rsp = new NotifyReciveResponse();
        rsp.setNotifyCode(NotifyReciveResponse.NOTIFY_SUCCESS);
        if (StringUtils.isBlank(param)) {
            log.warn("notifyInfo is Empty");
            return rsp;
        }

        try {
            String data = new String(Base64.getDecoder().decode(param));
            NotifyReciveReq dataInfo = GsonUtils.fromJson(data, NotifyReciveReq.class);
            String body = dataInfo.getBody();
            PersonResignReq personResignReq = GsonUtils.fromJson(body, PersonResignReq.class);

            log.info("接收到notify人员离职通知，personResignReq={}", GsonUtils.toJsonWtihNullField(personResignReq));

            // 如果交接人信息为空，则默认设置为部门领导的Oprid
            if (StringUtils.isBlank(personResignReq.getMiHandoverOprid())) {
                personResignReq.setMiHandoverOprid(personResignReq.getDeptInfo().getLeaderOprid());
            }

            // 特殊逻辑 如果交接人为leijun，更改为BPM流程管理员
            checkHandover(personResignReq);

            // 获取离职人员的信息
            BpmUser currentUser = accountRemoteService.getUser(personResignReq.getOprid());

            // 处理 p-账号的逻辑，并获取交接人
            BpmUser handoverUser = getHandoverUserForPAccount(currentUser.getUserName(), personResignReq.getMiHandoverOprid());

            if (handoverUser == null) {
                log.warn("无法找到交接人，用户: {}", currentUser.getUserName());
                return rsp;
            }

            // 构建人员离职事件
            PersonResignEvent personResignEvent = PersonResignEvent.builder()
                    .userName(currentUser)
                    .handoverUser(handoverUser)
                    .build();

            // 设置事件基础信息
            personResignEvent.setId(UUID.randomUUID().toString());
            personResignEvent.setIdentifier(EventIdentify.PERSON_RESIGN.name());
            personResignEvent.setTimestamp(System.currentTimeMillis());
            // 发送人员离职事件
            eventPublisher.publish(personResignEvent);

        } catch (Exception e) {
            log.warn("notifyInfo exception:", e);
            rsp.setNotifyCode(NotifyReciveResponse.NOTIFY_ERROR_EXCEPTION);
        }

        return rsp;
    }

    private void checkHandover(PersonResignReq personResignReq) {
        if ("leijun".equals(personResignReq.getMiHandoverOprid())) {
            log.info("离职交接人为leijun, 更换为chenzhang");
            // 发送预警通知
            I18nText i18n = new I18nText();
            i18n.setZhCn(String.format("%s离职,交接人为leijun", personResignReq.getOprid()));
            i18n.setEnUs(i18n.getZhCn());
            larkAppRemoteService.sendTipsChatMessage("oc_c8cb33bce6d63a8309decc20f49fb32d", i18n, "chenzhang");
            personResignReq.setMiHandoverOprid("chenzhang");
        }
    }

    @ApiOperation("人员转岗")
    @PostMapping("/person-transfer")
    public NotifyReciveResponse personTransfer(@RequestBody String param) {

        log.info("接收到notify人员转岗通知，msg={}", param);

        NotifyReciveResponse rsp = new NotifyReciveResponse();
        rsp.setNotifyCode(NotifyReciveResponse.NOTIFY_SUCCESS);
        if (StringUtils.isBlank(param)) {
            log.warn("notifyInfo is Empty");
            return rsp;
        }

        try {
            String data = new String(Base64.getDecoder().decode(param));
            NotifyReciveReq dataInfo = GsonUtils.fromJson(data, NotifyReciveReq.class);
            String body = dataInfo.getBody();
            PersonTransferReq personTransferReq = GsonUtils.fromJson(body, PersonTransferReq.class);

            if (StringUtils.isBlank(personTransferReq.getOprid())) {
                return rsp;
            }

            // 获取转岗用户的信息
            BpmUser currentUser = accountRemoteService.getUser(personTransferReq.getOprid());

            // 处理 p-账号的逻辑，并获取交接人
            BpmUser handoverUser = getHandoverUserForPAccount(currentUser.getUserName(), personTransferReq.getMiHandoverOprid());

            if (handoverUser == null) {
                log.warn("无法找到交接人，用户: {}", currentUser.getUserName());
                return rsp;
            }

            // 构建人员转岗事件
            PersonTransferEvent personTransferEvent = PersonTransferEvent.builder()
                    .userName(currentUser)
                    .handoverUser(handoverUser)
                    .build();

            // 设置事件基础信息
            personTransferEvent.setId(UUID.randomUUID().toString());
            personTransferEvent.setIdentifier(EventIdentify.PERSON_TRANSFER.name());
            personTransferEvent.setTimestamp(System.currentTimeMillis());
            // 发送人员转岗事件
            eventPublisher.publish(personTransferEvent);

        } catch (Exception e) {
            log.warn("notifyInfo exception:", e);
            rsp.setNotifyCode(NotifyReciveResponse.NOTIFY_ERROR_EXCEPTION);
        }

        return rsp;
    }

    /**
     * 获取交接人信息，如果是 p- 开头的账号，则获取其账号的负责人；否则直接获取交接人。
     */
    private BpmUser getHandoverUserForPAccount(String currentUser, String defaultHandoverOprid) {
        BpmUser handoverUser = null;

        // 处理 p-账号的逻辑
        if (currentUser.startsWith("p-")) {
            // 获取p账号的负责人列表，并设置handoverUser为p账号的负责人
            List<BpmUser> bpmUsers = accountRemoteService.listUserReportLine(currentUser);
            handoverUser = bpmUsers.stream()
                    .filter(user -> ((UserReportLineEntity) user).getLevel() == 2)
                    .findAny()
                    .orElse(null);
            if (handoverUser != null)
                handoverUser = accountRemoteService.getUser(handoverUser.getUserName());
        } else {
            // 非 p- 账号的逻辑，使用传入的默认交接人
            handoverUser = accountRemoteService.getUser(defaultHandoverOprid);
        }

        return handoverUser;
    }


    // 测试转岗接口
    @ApiOperation("测试人员转岗")
    @PostMapping("/person-transfer-test")
    public BaseResp<String> personTransferTest(String user, String handoverUserName) {
        // check auth
        if (!authorityRemote.isSuperAdmin(IdentityUtil.currentUserName())) {
            return BaseResp.error("没有权限执行此操作");
        }
        // 处理 p-账号的逻辑，并获取交接人
        BpmUser handoverUser = getHandoverUserForPAccount(user, handoverUserName);

        if (handoverUser == null) {
            log.warn("无法找到交接人，用户: {}", user);
            return BaseResp.error("无法找到交接人");
        }

        PersonTransferEvent personTransferEvent = PersonTransferEvent.builder()
                .userName(accountRemoteService.getUser(user))
                .handoverUser(handoverUser)
                .build();

        // 设置事件基础信息
        personTransferEvent.setId(UUID.randomUUID().toString());
        personTransferEvent.setIdentifier(EventIdentify.PERSON_TRANSFER.name());
        personTransferEvent.setTimestamp(System.currentTimeMillis());

        // 发送人员转岗事件
        eventPublisher.publish(personTransferEvent);
        return BaseResp.success("人员转岗事件成功发送");
    }

    // 测试离职接口
    @ApiOperation("测试人员离职")
    @PostMapping("/person-resign-test")
    public BaseResp<String> personResignTest(String currentUser, String handoverUserName) {
        // check auth
        if (!authorityRemote.isSuperAdmin(IdentityUtil.currentUserName())) {
            return BaseResp.error("没有权限执行此操作");
        }
        // 处理 p-账号的逻辑，并获取交接人
        BpmUser handoverUser = getHandoverUserForPAccount(currentUser, handoverUserName);

        if (handoverUser == null) {
            log.warn("无法找到交接人，用户: {}", currentUser);
            return BaseResp.error("无法找到交接人");
        }

        PersonResignEvent personResignEvent = PersonResignEvent.builder()
                .userName(accountRemoteService.getUser(currentUser))
                .handoverUser(handoverUser)
                .build();

        // 设置事件基础信息
        personResignEvent.setId(UUID.randomUUID().toString());
        personResignEvent.setIdentifier(EventIdentify.PERSON_RESIGN.name());
        personResignEvent.setTimestamp(System.currentTimeMillis());

        // 发送人员离职事件
        eventPublisher.publish(personResignEvent);
        return BaseResp.success("人员离职事件成功发送");
    }


}
