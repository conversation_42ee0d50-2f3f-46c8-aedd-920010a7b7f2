package com.mi.oa.infra.mibpm.api.errorcode;

import com.mi.oa.infra.oaucf.core.exception.InterfaceErrorCode;

/**
 * by roger
 */
public enum InterfaceErrorCodeEnum implements InterfaceErrorCode {

    INTERFACE_UNKNOWN_ERROR(1, "接口层未知错误");


    /**
     * 具体错误码
     */
    private int errCode;
    /**
     * 描述
     */
    private String errDesc;

    @Override
    public int getBizCode() {
        return 0;
    }

    @Override
    public int getErrorCode() {
        return errCode;
    }

    @Override
    public String getErrDesc() {
        return this.errDesc;
    }

    /**
     * 构造方法
     *
     * @param errCode
     * @param errDesc
     */
    InterfaceErrorCodeEnum(int errCode, String errDesc) {
        this.errCode = errCode;
        this.errDesc = errDesc;
    }


}
