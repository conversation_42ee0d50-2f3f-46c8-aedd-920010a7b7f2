/*
 * Copyright (c) 2020. XiaoMi Inc.All Rights Reserved
 */
package com.mi.oa.infra.mibpm.api;

import com.mi.oa.infra.mibpm.common.model.MiBpmProperties;
import com.mi.oa.infra.mibpm.common.model.MibpmPredictProperties;
import com.mi.oa.infra.oaucf.EnableFDS;
import com.mi.oa.infra.oaucf.newauth.autoconfig.authority.UcAuthorityServiceConfiguration;
import org.flowable.spring.boot.mi.MiEngineServicesAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;

/**
 * 启动类
 *
 * <AUTHOR>
 * @date 2021/5/21 10:24 上午
 */
@EnableFDS
@SpringBootApplication
@ComponentScan(basePackages = {"com.mi.oa"})
@Import({MiEngineServicesAutoConfiguration.class, UcAuthorityServiceConfiguration.class})
@EnableCaching
@EnableConfigurationProperties(value = {MiBpmProperties.class, MibpmPredictProperties.class,})
public class MibpmApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(MibpmApiApplication.class, args);
    }

}
