package com.mi.oa.infra.mibpm.api.converter;

import com.mi.oa.infra.form.core.api.dto.FormDefResp;
import com.mi.oa.infra.form.def.common.model.schema.container.FormSchema;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.OpenFormDefResp;
import com.mi.oa.infra.oaucf.utils.GsonUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/9 10:08
 */
@Mapper(componentModel = "spring")
public interface OpenFormDefRespConverter {

    OpenFormDefResp dtoToDto(FormDefResp formDefResp);
}
