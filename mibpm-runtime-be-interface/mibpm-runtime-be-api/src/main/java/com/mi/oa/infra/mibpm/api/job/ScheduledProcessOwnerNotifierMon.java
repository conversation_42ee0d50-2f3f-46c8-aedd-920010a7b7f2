package com.mi.oa.infra.mibpm.api.job;

import com.mi.oa.infra.mibpm.application.message.service.MessageService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Calendar;


/**
 * @author: liwanxue
 * @Date: 2024/8/27 10:25
 * <p>
 * 流程负责人填写提醒
 */
@Component
@Slf4j
@PlanTask(name = "ScheduledProcessOwnerNotifierMon", quartzCron = "0 0 14 ? * MON *",
        description = "流程负责人填写提醒")
public class ScheduledProcessOwnerNotifierMon implements PlanExecutor {

    @Autowired
    private MessageService messageService;

    @Override
    public void execute() {
        // 记录当前时间日志
        log.info("-----now Time : {}", Calendar.getInstance().getTime().toString());
        // 每周一下午2点触发流程负责人填写提醒
        messageService.ownersReminder();
    }
}
