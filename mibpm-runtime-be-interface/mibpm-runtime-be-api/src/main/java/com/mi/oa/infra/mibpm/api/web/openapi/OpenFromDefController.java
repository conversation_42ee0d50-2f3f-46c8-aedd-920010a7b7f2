package com.mi.oa.infra.mibpm.api.web.openapi;

import com.mi.oa.infra.form.core.api.dto.FormDefResp;
import com.mi.oa.infra.form.core.api.remote.FormDefService;
import com.mi.oa.infra.mibpm.api.converter.OpenFormDefRespConverter;
import com.mi.oa.infra.mibpm.application.proinst.dto.reps.OpenFormDefResp;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/5/9 10:05
 */
@Api(tags = "Open API 表单定义")
@WebLog
@RestController
@RequestMapping("/openapi/v2/forms")
public class OpenFromDefController {

    @Autowired
    private FormDefService formDefService;
    @Autowired
    private OpenFormDefRespConverter openFormDefRespConverter;


    @ApiOperation("查询表单定义")
    @GetMapping(value = "/definitions")
    public BaseResp<OpenFormDefResp> queryFormDef(@RequestParam(value = "formDefinitionId") String formDefinitionId,
                                                  @RequestParam(value = "taskDefinitionKey", required = false) String taskDefinitionKey) {
        BaseResp<FormDefResp> resp = formDefService.queryFormDef(formDefinitionId, taskDefinitionKey);
        if (resp.getCode() == 0) {
            return BaseResp.success(openFormDefRespConverter.dtoToDto(resp.getData()));
        }

        return BaseResp.error(resp.getCode(), resp.getMessage());
    }

}
