package com.mi.oa.infra.mibpm.api.model.vo;

import com.mi.oa.infra.mibpm.flowable.extension.model.ApprovalStrategy;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskOperation;
import com.mi.oa.infra.mibpm.flowable.extension.model.UserTaskSignType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/7 11:26
 **/
@Data
public class TaskDetailConfigVo {
    private String processInstanceId;
    private String taskId;
    protected Boolean disableAppOperation;
    @ApiModelProperty("审批操作列表")
    private List<UserTaskOperation> operationList;
    @ApiModelProperty("参数列表")
    private Map<String, Object> variables;
    @ApiModelProperty("审批策略 含退回策略、拒绝策略，结构跟流程配置一样")
    private List<ApprovalStrategy> strategyList;
    private List<UserTaskOperation> requiredComments;
    protected Boolean isVetoUser;
    protected UserTaskSignType signType;
}
