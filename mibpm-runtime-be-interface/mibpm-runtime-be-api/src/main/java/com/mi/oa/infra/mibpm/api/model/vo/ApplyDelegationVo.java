package com.mi.oa.infra.mibpm.api.model.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.mi.oa.infra.mibpm.common.model.DelegationExclusionProcess;
import com.mi.oa.infra.mibpm.common.model.DelegationProcess;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/22
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApplyDelegationVo {
    // 委托人id
    private String delegatorId;
    // 代批人id（流程全部委托时用）
    private String approverAllId;
    // 创建委托申请人的id（一般管理员或者委托人）
    private String createUserId;
    // 记录更新人id
    private String updaterId;
    // 委托开始时间（配置序列化和反序列化器，防止在对象中操作出错）
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime startTime;
    // 委托结束时间
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime endTime;
    // 委托理由
    private String delegationReason;
    // 委托类型（枚举类型，全部为0，部分为1）
    private int delegationType;
    // 委托流程列表 delegationType为0时为空，为1时包含N个流程信息
    private List<DelegationProcess> delegationProcessList;
    // 全部流程委托，排除部分
    private List<DelegationExclusionProcess> delegationExclusionProcessList;
    // 委托需求提出人
    private String delegationRequirementProposer;
    // 是否是请假委托
    private Boolean isLeave;

}

