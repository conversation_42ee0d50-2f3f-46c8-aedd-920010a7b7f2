mibpm:
  runtime:
    offline-bpmn-model:
    baiteda:
      mobile-detail-template: https://baiteda.test.mioffice.cn/apps/mobile/approval/%s/%s
      pc-detail-template: https://baiteda.test.mioffice.cn/apps/desktop/approval/%s/%s
  repository:
    enabled: true
    appId: ${oaucf.app.appId}
    appSecret: ${oaucf.app.appSecret}
    url: https://bpm-infra-dev.test.mioffice.cn/repository
  approval:
    task-url: https://work-dev.test.mioffice.cn/apps/approval/approvalDetail?taskId=
    app-task-url: https://applink.feishu.cn/client/mini_program/open?appId=${oaucf.auth.auth.lark.app-id}&path=%s
    lark-pc-url: https://applink.feishu.cn/client/web_app/open?appId=${oaucf.auth.auth.lark.app-id}&path=/approval/approvalDetail&taskId=
    lark-pc-delegation-url: https://applink.feishu.cn/client/web_app/open?appId=${oaucf.auth.auth.lark.app-id}&path=/approval/setting?type=%s
  lark:
    callback-url: https://jtest.mioffice.cn/mibpm/lark/notify/cardEvent
    message:
      excludes:
        - bpmn_801417896971599872

  mibpm-v2:
    appid: mibpm_1008
    appkey@kc-sid: oa-infra.g
    appkey: GCDo1WzeDcjTjClW2wNqda+gnmvlQEsrlpQJiWhcig2f5BgSYj33L78RRFWtd/gZuY+2N4P/GBBH9sN5vetDFocaRpdNupHWGBRg5zomR9l4P6+QJp1RWng33/sJ6AA=
    host: http://jtest.mioffice.cn/mibpm/api/
    auth-url: http://jtest.mioffice.cn/mibpm/auth/getAccessToken
    detail-jsp-url: https://jtest.mioffice.cn/mibpm/client/taskDetails?taskId=%s&procInstId=%s&bpmType=%s
    detail-form-url: https://jtest.mioffice.cn/bpm/#/approvalDetail?taskId=%s&procInstId=%s&bpmType=%s&procDefIdPrefix=%s
  predict:
    enabled: true
    url: http://bpm-infra-dev.test.mioffice.cn/predict
    appId: ${oaucf.app.appId}
    server-app-id: ${oaucf.app.appId}
    appSecret: ${oaucf.app.appSecret}
    env: TEST
  env: TEST
  view-auth:
    admin-auth-excludes:
      - bpmn_786562160424779776

  app-config:
    phone_key:
      url: ${profiles.service.url}
      appid: mibpm
      appkey: 982ed6395d0980d74e1e805bbc6aa3

    # 示例配置
    479023823232:
      appkey: ihenefnorl9io4idfu0ihf8eewoi30w3o

    200001:
      appkey: 93a759af-1758-47ac-bd35-93171f942dad

    mibpm_1003:
      appkey: a51dbbd56e037f2fh0f03fh7f20f

    mibpm_1007:
      appkey: b51dbbd56e03er48fduj99j767f20f7

    mibpm_1008:
      appkey: b51dbbd56e03er48fduj99j767f20f7

    mibpm_1009:
      appkey: b51dbbd56e03er48fduj99j767f2

    mibpm_1010:
      appkey: db1cac7e-9288-48a0-953f-33c4962a5ee4

    mibpm_1011:
      appkey: f5601c21-c1a1-41f0-98df-18f5a90ab5a2

    mibpm_1012:
      appkey: 34945a49-97c0-440a-a205-0c2607796a59

    mibpm_1013:
      appkey: 702dafb0-5aca-4120-80f7-e75463e48b9f

    mibpm_1014:
      appkey: 1493e19b-d816-45c5-954b-a22f27bd59ce

    mibpm_1015:
      appkey: 189ge1fb-dtf6-46d5-944h-aksf2mbd59cf

    sku_1008:
      appkey: 268050d1acae4eca8c68109a638005c0

    mibpm_1016:
      appkey: 05f06839-54fb-4516-badd-2d0d0e916791

    mibpm_1017:
      appkey: 6f6bb2b3-57e0-4cb9-830d-c9884147ef76

    mibpm_1019:
      appkey: 1122937488EF4B203df77A5DEa2c977f3

    bigdata_bpm:
      appkey: A2EA348488EF4B207786A5DE2D7FFBCC

    reimburse_dept_cost:
      appkey: 1484FYB58SDSAWD456864SDW24

    mibpm_1018:
      appkey: 1122937f-8a9b-424d-b9b3-13aa2c9777f3

    miniapp:
      appkey: 92453de1-d183-4d91-8b60-12aa8ee569c7

    contractCloud:
      appkey: 457ef1dd-c40e-4b0a-8dbd-3d23658addde

    mierpc:
      appkey: d4c09d2e-f549-4e2c-9a7f-b97f52bc786c

    fanwei:
      appkey: 8cb018b9-4c5b-4b0e-94d1-4a497b707946

    taxManager:
      appkey: 886179fb-75ce-4fed-ad4b-ae81bd68ce47

    common01:
      appid: mibpm_1008
      appkey: b51dbbd56e03er48fduj99j767f20f7

    sku:
      appid: mibpm_1008
      appkey: b51dbbd56e03er48fduj99j767f20f7

    store:
      appid: mibpm_1008
      appkey: b51dbbd56e03er48fduj99j767f20f7

    storeProtoCompensation:
      appid: mibpm_1013
      appkey: 702dafb0-5aca-4120-80f7-e75463e48b9f

    travelApply:
      appid: mibpm_1009
      appkey: b51dbbd56e03er48fduj99j767f2

    internalBuy:
      appid: xm_1001
      appkey: b8848ce2828a451ce2b5b537bcc8fb35

    internalBuyDealer:
      appid: xm_1001
      appkey: b8848ce2828a451ce2b5b537bcc8fb35

    internalBuyActivityApply:
      appid: xm_1001
      appkey: b8848ce2828a451ce2b5b537bcc8fb35

    internalBuyCertainPriceApply:
      appid: xm_1001
      appkey: b8848ce2828a451ce2b5b537bcc8fb35

    internalBuyDeptActivityApply:
      appid: xm_1001
      appkey: b8848ce2828a451ce2b5b537bcc8fb35

    internalBuyPOApply:
      appid: xm_1001
      appkey: b8848ce2828a451ce2b5b537bcc8fb35

    internalBuyProtoApply:
      appid: xm_1001
      appkey: b8848ce2828a451ce2b5b537bcc8fb35

    internalBuyGiftActivityApply:
      appid: xm_1001
      appkey: b8848ce2828a451ce2b5b537bcc8fb35

    internalBuyDealerRebateAmount:
      appid: xm_1001
      appkey: b8848ce2828a451ce2b5b537bcc8fb35

    internalBuyDeptAccrual:
      appid: xm_1001
      appkey: b8848ce2828a451ce2b5b537bcc8fb35

    internalBuySalvageActivity:
      appid: xm_1001
      appkey: b8848ce2828a451ce2b5b537bcc8fb35

    internalBuyStoreGrowthIncentives:
      appid: xm_1001
      appkey: b8848ce2828a451ce2b5b537bcc8fb35

    miHomeActivityApply:
      appid: xm_bpm
      appkey: 16e04a67a541ef1e21a5948705e2f890

    mihomeCouponApply:
      appid: xm_bpm
      appkey: 16e04a67a541ef1e21a5948705e2f890

    miHomeAuthApply:
      appid: xm_bpm
      appkey: 16e04a67a541ef1e21a5948705e2f890

    paymentTb:
      appid: mibpm_1014
      appkey: 1493e19b-d816-45c5-954b-a22f27bd59ce

    miHomeExpense:
      appid: ecs_1000
      appkey: ihenefnorl9io4idfu0ihf8eewoi30w3o

    miNetExpense:
      appid: ecs_1000
      appkey: ihenefnorl9io4idfu0ihf8eewoi30w3o

    mioseExpense:
      appid: ecs_1000
      appkey: ihenefnorl9io4idfu0ihf8eewoi30w3o

    mirmeExpense:
      appid: ecs_1000
      appkey: ihenefnorl9io4idfu0ihf8eewoi30w3o

    service:
      url: https://t.mioffice.cn/service/
      appid: uc_1000
      appkey: weddbbd56e03er48fduj99j767f20f7

    newMaterial:
      appid: 100007
      appkey: 0bcebedd4136834e6ad64becc34ed39d

    servicesrd:
      appid: sap_1008
      appkey: ihenefnorl9io4idfu0ihf8eewoi30w3o

    huyuSettlement:
      appid: huyu_settlement
      appkey: 45341c49-25e2-231b-a205-03e6275963e5

    buySystem:
      url: http://buy.test.mi.com/
      appid: xm_reimburse
      appkey: 8fb35b2828a451ce537b

    buySys:
      url: http://buy.test.mi.com/
      appid: xm_1001
      appkey: b8848ce2828a451ce2b5b537bcc8fb35

    mirenSysCommon:
      appid: 100021
      appkey: e10adc3949ba59abbe56e057f20f883e

    fanwei01:
      appkey: 2867a8d2626728823bfb0989719d7a6

    contract:
      esign:
        partb: 325jlsi5jsie565h